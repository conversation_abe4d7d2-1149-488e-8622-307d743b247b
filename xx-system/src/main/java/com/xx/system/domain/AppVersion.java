package com.xx.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xx.interfaces.validatedGroups.Delete;
import com.xx.interfaces.validatedGroups.Insert;
import com.xx.interfaces.validatedGroups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
* <p>
*
* </p>
* <AUTHOR>
* @since 2023-06-13
*/
@Data
@Accessors(chain = true)
@TableName("app_version")
@ApiModel(value = "AppVersion对象", description = "app版本更新")
public class AppVersion {

    @ApiModelProperty("app版本主键id")
    @NotNull(message = "id不能空",groups = {Update.class, Delete.class})
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("app版本号")
    @NotBlank(message = "版本号不能为空",groups = {Insert.class, Update.class})
    @Pattern(regexp = "^\\d+(?:\\.\\d+){2}$",message = "版本格式纯数字xx.xx.xx",groups = {Insert.class, Update.class})
    @Length(min = 1,max = 16,message = "版本号长度限制1-16个字符")
    @TableField("`app_version`")
    private String appVersion;
    @JsonIgnore
    @TableField("`app_version_format`")
    private String appVersionFormat;
    @ApiModelProperty("app版本更新内容")
    @NotBlank(message="更新内容不能空",groups = {Insert.class, Update.class})
    @Length(min = 1,max = 1000,message = "版本内容长度限制1-1000个字符")
    @TableField("`version_content`")
    private String versionContent;
    @ApiModelProperty("是否强制更新")
    @NotNull(message = "是否强制更新不能空",groups = {Insert.class, Update.class})
    @TableField("`is_mandatory`")
    private Boolean isMandatory;
    @ApiModelProperty("安装包平台 1android 2ios")
    @TableField("`platform`")
    private Byte platform;
    @ApiModelProperty("1wgt 2apk")
    @TableField("`type`")
    private Byte type;
    @ApiModelProperty("1册子 2金塘")
    @NotNull(message = "册子金塘选择",groups = {Insert.class, Update.class})
    @TableField("`place`")
    private Byte place;
    @ApiModelProperty("下载url")
    @NotBlank(message="包路径不能空",groups = {Insert.class, Update.class})
    @Length(min = 1,max = 500,message = "url长度限制1-500个字符")
    @TableField("`url`")
    private String url;
    @ApiModelProperty("添加时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
}
