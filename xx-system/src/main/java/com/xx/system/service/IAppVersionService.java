package com.xx.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xx.system.domain.AppVersion;
import com.xx.system.domain.dto.VersionCheckDto;

import java.util.List;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-06-13
*/
public interface IAppVersionService extends IService<AppVersion> {

    /**
    * 分页列表
    * @param appVersion 根据需要进行传值
    * @return
    */
    IPage<AppVersion> page(AppVersion appVersion);

    /**
    * 详情
    * @param id
    * @return
    */
    AppVersion info(Long id);

    /**
    * 新增
    * @param appVersion 根据需要进行传值
    * @return
    */
    boolean add(AppVersion appVersion);

    /**
    * 修改
    * @param appVersion 根据需要进行传值
    * @return
    */
    boolean modify(AppVersion appVersion);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);

    AppVersion checkVersion(VersionCheckDto dto);
}
