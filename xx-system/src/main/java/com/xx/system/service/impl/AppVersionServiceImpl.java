package com.xx.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.system.domain.AppVersion;
import com.xx.system.domain.dto.VersionCheckDto;
import com.xx.system.mapper.AppVersionMapper;
import com.xx.system.service.IAppVersionService;
import com.xx.utils.base.MPageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-13
 */
@Service
@RequiredArgsConstructor
public class AppVersionServiceImpl extends ServiceImpl<AppVersionMapper, AppVersion> implements IAppVersionService {

    private final AppVersionMapper mapper;

    /**
     * 分页列表
     *
     * @param appVersion 根据需要进行传值
     * @return
     */
    @Override
    public IPage<AppVersion> page(AppVersion appVersion) {

        QueryWrapper<AppVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // app版本主键id
                .eq(appVersion.getId() != null, AppVersion::getId, appVersion.getId())
                // app版本号
                .eq(!StringUtils.isEmpty(appVersion.getAppVersion()), AppVersion::getAppVersion,
                        appVersion.getAppVersion())
                // app版本更新内容
                .eq(!StringUtils.isEmpty(appVersion.getVersionContent()), AppVersion::getVersionContent,
                        appVersion.getVersionContent())
                // 是否强制更新
                .eq(appVersion.getIsMandatory() != null, AppVersion::getIsMandatory, appVersion.getIsMandatory())
                // 安装包平台 1android 2ios
                .eq(appVersion.getPlatform() != null, AppVersion::getPlatform, appVersion.getPlatform())
                // 1wgt 2apk
                .eq(appVersion.getType() != null, AppVersion::getType, appVersion.getType())
                .eq(appVersion.getPlace() != null, AppVersion::getPlace, appVersion.getPlace())
                // 下载url
                .eq(!StringUtils.isEmpty(appVersion.getUrl()), AppVersion::getUrl, appVersion.getUrl())
                // 添加时间
                .eq(appVersion.getGmtCreate() != null, AppVersion::getGmtCreate, appVersion.getGmtCreate());
        IPage<AppVersion> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public AppVersion info(Long id) {
        return getById(id);
    }

    /**
     * 新增
     *
     * @param appVersion 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(AppVersion appVersion) {
        //版本号格式化
        appVersion.setAppVersionFormat(com.xx.utils.string.StringUtils.versionFormat(appVersion.getAppVersion(), "\\.", "."
                , 3, 3, 3));
        return save(appVersion);
    }

    /**
     * 修改
     *
     * @param appVersion 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(AppVersion appVersion) {
        appVersion.setAppVersionFormat(com.xx.utils.string.StringUtils.versionFormat(appVersion.getAppVersion(), "\\.", "."
                , 3, 3, 3));
        return updateById(appVersion);
    }

    /**
     * 删除(单个条目)
     *
     * @param id
     * @return
     */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
     * 删除(多个条目)
     *
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

    /**
     * 检测是否有新版本更新
     *
     * @param dto
     * @return
     */
    @Override
    public AppVersion checkVersion(VersionCheckDto dto) {
        dto.setAppVersion(com.xx.utils.string.StringUtils.versionFormat(dto.getAppVersion(), "\\.", "."
                , 3, 3, 3));
        AppVersion appVersion =
                getOne(Wrappers.<AppVersion>lambdaQuery().eq(AppVersion::getPlace,
                        dto.getPlace()).gt(AppVersion::getAppVersionFormat, dto.getAppVersion()).orderByDesc(AppVersion::getAppVersion).last("limit 1"));
   /*     if (appVersion != null) {
            List<AppVersion> list = list(Wrappers.<AppVersion>lambdaQuery().eq(AppVersion::getPlace, dto.getPalce()).eq(AppVersion::getAppVersion, appVersion.getAppVersion()).orderByDesc(AppVersion::getAppVersion));
        }*/
        return appVersion;
    }
}
