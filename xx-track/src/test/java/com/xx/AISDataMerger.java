//package com.xx;
//
//
//import com.xx.dk.tbsalling.aismessages.ais.messages.*;
//import com.xx.dk.tbsalling.aismessages.ais.messages.types.AISMessageType;
//import com.xx.dk.tbsalling.aismessages.nmea.NMEAMessageSocketClient;
//import com.xx.mp.mapper.XxBaseMapper;
//import com.xx.utils.redis.RedisUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.*;
//
//@Slf4j
//@SpringBootTest(classes = XxBaseMapper.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class AISDataMerger {
//
//    private static final String AIS_HASH_KEY_PREFIX = "ais_data_";
//
//    @Autowired
//    private RedisUtil redisUtil;
//
//
//    public static final Map<Integer, String[][]> AIS_MSG_TYPES = new HashMap<>();
//
//    static {
//        // AIS message type 1
//        AIS_MSG_TYPES.put(1, new String[][]{
//                {"type", "repeat_indicator", "mmsi", "nav_status", "rot", "sog", "position_accuracy", "longitude",
//                        "latitude", "cog", "true_heading", "timestamp", "maneuver_indicator", "spare", "raim", "sync_state",
//                        "slot_timeout", "received_stations", "slot_number", "utc_hour", "utc_minute", "utc_second",
//                        "regional_reserved", "dte", "assigned_mode"}
//        });
//
//        // Other AIS message types can be added here
//    }
//    public void mergeStaticAndTrackData(List<Map<String, Object>> msgList) {
//        // 构造每个船舶的键值
//        Map<String, Map<String, String>> shipData = new HashMap<>();
//        for (Map<String, Object> msg : msgList) {
//            int msgType = (int) msg.get("msg_type");
//            if (msgType == 1 || msgType == 2 || msgType == 3 || msgType == 4 || msgType == 18 || msgType == 19 ||
//                    msgType == 27) {
//                // 航迹数据
//                String mmsi = String.valueOf(msg.get("mmsi"));
//                if (!shipData.containsKey(mmsi)) {
//                    shipData.put(mmsi, new HashMap<>());
//                }
//
//
//                // 更新航迹数据
//                for (String field : AIS_MSG_TYPES.get(msgType)[1]) {
//                    if (msg.containsKey(field)) {
//                        String value = String.valueOf(msg.get(field));
//                        shipData.get(mmsi).put(field, value);
//                    }
//                }
//            } else if (msgType == 5 || msgType == 6 || msgType == 10 || msgType == 11 || msgType == 24) {
//                // 静态数据
//                String mmsi = String.valueOf(msg.get("mmsi"));
//                if (!shipData.containsKey(mmsi)) {
//                    shipData.put(mmsi, new HashMap<>());
//                }
//                // 更新静态数据
//                for (String field : AIS_MSG_TYPES.get(msgType)[1]) {
//                    if (msg.containsKey(field)) {
//                        String value = String.valueOf(msg.get(field));
//                        shipData.get(mmsi).put(field, value);
//                    }
//                }
//            }
//        }
//        // 将每个船舶的数据写入Redis
//        for (Map.Entry<String, Map<String, String>> entry : shipData.entrySet()) {
//            String mmsi = entry.getKey();
//            Map<String, String> data = entry.getValue();
//            String hashKey = AIS_HASH_KEY_PREFIX + mmsi;
//            redisUtil.hmset(hashKey, data);
//        }
//    }
//
//    public List<Map<String, String>> getMergedData(List<String> mmsiList) {
//        // 从Redis中获取每个船舶的完整数据
//        List<Map<String, String>> mergedData = new ArrayList<>();
//        for (String mmsi : mmsiList) {
//            String hashKey = AIS_HASH_KEY_PREFIX + mmsi;
//            Map<String, String> data = redisUtil.hgetAll(hashKey);
//            mergedData.add(data);
//        }
//        return mergedData;
//    }
//
//    public void mergeStaticAndTrackData(Map<String, Map<String, String>> shipData, Map<String, Object> msg) {
//        int msgType = (int) msg.get("msg_type");
//        if (msgType == 1 || msgType == 2 || msgType == 3 || msgType == 4 || msgType == 18 || msgType == 19 || msgType == 27) {
//            // 航迹数据
//            String mmsi = String.valueOf(msg.get("mmsi"));
//            if (!shipData.containsKey(mmsi)) {
//                shipData.put(mmsi, new HashMap<>());
//            }
//            // 更新航迹数据
//            for (String field : AIS_MSG_TYPES.get(msgType)[1]) {
//                if (msg.containsKey(field)) {
//                    String value = String.valueOf(msg.get(field));
//                    shipData.get(mmsi).put(field, value);
//                }
//            }
//        } else if (msgType == 5 || msgType == 6 || msgType == 10 || msgType == 11 || msgType == 24) {
//            // 静态数据
//            String mmsi = String.valueOf(msg.get("mmsi"));
//            if (!shipData.containsKey(mmsi)) {
//                shipData.put(mmsi, new HashMap<>());
//            }
//            // 更新静态数据
//            for (String field : AIS_MSG_TYPES.get(msgType)[1]) {
//                if (msg.containsKey(field)) {
//                    String value = String.valueOf(msg.get(field));
//                    shipData.get(mmsi).put(field, value);
//                }
//            }
//        }
//    }
//
///*    public void mergeStaticAndTrackData(Map<Integer, AISMessage> shipData, AISMessage msg) {
//        int msgType = msg.getMessageType().getCode();
//        if (msgType == 1 || msgType == 2 || msgType == 3 || msgType == 4 || msgType == 18 || msgType == 19 || msgType == 27) {
//            // 航迹数据
//            int mmsi = msg.getSourceMmsi().getMMSI();
//            if (!shipData.containsKey(mmsi)) {
//                shipData.put(mmsi, new AISMessage());
//            }
//            // 更新航迹数据
//            String[][] messageFields = AIS_MSG_TYPES.get(msgType);
//            for (String[] field : messageFields[1]) {
//                Object value = msg.getFieldValue(field[0]);
//                if (value != null) {
//                    String stringValue = String.valueOf(value);
//                    shipData.get(mmsi).setFieldValue(field[0], stringValue);
//                }
//            }
//        } else if (msgType == 5 || msgType == 6 || msgType == 10 || msgType == 11 || msgType == 24) {
//            // 静态数据
//            String mmsi = msg.getMmsi();
//            if (!shipData.containsKey(mmsi)) {
//                shipData.put(mmsi, new AISMessage());
//            }
//            // 更新静态数据
//            String[][] messageFields = AIS_MSG_TYPES.get(msgType);
//            for (String[] field : messageFields[1]) {
//                Object value = msg.getFieldValue(field[0]);
//                if (value != null) {
//                    String stringValue = String.valueOf(value);
//                    shipData.get(mmsi).setFieldValue(field[0], stringValue);
//                }
//            }
//        }
//    }*/
//
//
//    public void mergeStaticAndTrackData(Map<Integer, AISMessage> shipData, AISMessage msg) {
//        int msgType = msg.getMessageType().getCode();
//        if (msgType == 1 || msgType == 2 || msgType == 3 || msgType == 4 || msgType == 18 || msgType == 19 || msgType == 27) {
//            // 航迹数据
//            Integer mmsi = msg.getSourceMmsi().getMMSI();
//            if (!shipData.containsKey(mmsi)) {
//                AISMessage newMessage = createAISMessageObject(msg);
//                shipData.put(mmsi, newMessage);
//            }
//            // 更新航迹数据
//            String[][] messageFields = AIS_MSG_TYPES.get(msgType);
//
//            for (String[] field : messageFields) {
//                Object value = msg.getFieldValue(field[0]);
//                if (value != null) {
//                    String stringValue = String.valueOf(value);
//                    shipData.get(mmsi).setFieldValue(field[0], stringValue);
//                }
//            }
//        } else if (msgType == 5 || msgType == 6 || msgType == 10 || msgType == 11 || msgType == 24) {
//            // 静态数据
//            String mmsi = msg.getMmsi();
//            if (!shipData.containsKey(mmsi)) {
//                AISMessage newMessage = createAISMessageObject(msg);
//                shipData.put(mmsi, newMessage);
//            }
//            // 更新静态数据
//            String[][] messageFields = AIS_MSG_TYPES.get(msgType);
//            for (String[] field : messageFields) {
//                Object value = msg.getFieldValue(field[0]);
//                if (value != null) {
//                    String stringValue = String.valueOf(value);
//                    shipData.get(mmsi).setFieldValue(field[0], stringValue);
//                }
//            }
//        }
//    }
//    public static void main(String[] args) {
//        //创建一个二维数组12*12
//        double[][] arr = new double[12][12];
//        Scanner sc = new java.util.Scanner(System.in);
//        //进行赋值
//        for (int i = 0; i < 12; i++) {
//            for (int j = 0; j < 12; j++) {
//                arr[i][j] = sc.nextDouble();
//            }
//        }
//        //增强for循环遍历
//        /**
//         * 第一层遍历：
//         * 因为二维数组中的每一个元素都是一维数组，因此应将每个数组元素遍历到
//         * 一个新的数组中去。
//         * 第二层遍历：
//         * 由于第一层遍历完成后，每个元素已经编程一个一维数组，则只需要用新的
//         * 变量来遍历数组的每个元素，进行输出即可。
//         *
//         */
//        for (double[] aa : arr) {
//            for (double bb : aa) {
//                System.out.print(bb + " ");
//            }
//            System.out.println();
//        }
//
//        new NMEAMessageSocketClient();
//    }
//
//    public AISMessage createAISMessageObject(AISMessage message) {
//        AISMessageType messageType = message.getMessageType();
//        AISMessage newMessage = null;
//        switch (messageType) {
//            case ShipAndVoyageRelatedData: {
//                ShipAndVoyageData shipMessage =
//                        (ShipAndVoyageData) message;
//                break;
//            }
//            case PositionReportClassAScheduled: {
//                PositionReportClassAScheduled shipMessage =
//                        (PositionReportClassAScheduled) message;
//                log.info("PositionReportClassAScheduled: {}",
//                        shipMessage);
//                break;
//            }
//            case PositionReportClassAAssignedSchedule: {
//                PositionReportClassAAssignedSchedule shipMessage =
//                        (PositionReportClassAAssignedSchedule) message;
//                log.info("PositionReportClassAAssignedSchedule: {}",
//                        shipMessage);
//                break;
//            }
//            case PositionReportClassAResponseToInterrogation: {
//                PositionReportClassAResponseToInterrogation shipMessage =
//                        (PositionReportClassAResponseToInterrogation) message;
//                log.info("PositionReportClassAResponseToInterrogation: {}",
//                        shipMessage);
//                break;
//            }
//            case BaseStationReport: {
//                BaseStationReport shipMessage =
//                        (BaseStationReport) message;
//                log.info("BaseStationReport: {}", shipMessage);
//                break;
//            }
//            case AddressedBinaryMessage: {
//                AddressedBinaryMessage shipMessage =
//                        (AddressedBinaryMessage) message;
//                log.info("AddressedBinaryMessage: {}", shipMessage);
//                break;
//            }
//            case BinaryAcknowledge: {
//                BinaryAcknowledge shipMessage =
//                        (BinaryAcknowledge) message;
//                log.info("BinaryAcknowledge: {}", shipMessage);
//                break;
//            }
//            case BinaryBroadcastMessage: {
//                BinaryBroadcastMessage shipMessage =
//                        (BinaryBroadcastMessage) message;
//                log.info("BinaryBroadcastMessage: {}", shipMessage);
//                break;
//            }
//            case StandardSARAircraftPositionReport: {
//                StandardSARAircraftPositionReport shipMessage =
//                        (StandardSARAircraftPositionReport) message;
//                log.info("StandardSARAircraftPositionReport: {}",
//                        shipMessage);
//                break;
//            }
//            case UTCAndDateInquiry: {
//                UTCAndDateInquiry shipMessage =
//                        (UTCAndDateInquiry) message;
//                log.info("UTCAndDateInquiry: {}", shipMessage);
//                break;
//            }
//            case UTCAndDateResponse: {
//                UTCAndDateResponse shipMessage =
//                        (UTCAndDateResponse) message;
//                log.info("UTCAndDateResponse: {}", shipMessage);
//                break;
//            }
//            case AddressedSafetyRelatedMessage: {
//                AddressedSafetyRelatedMessage shipMessage =
//                        (AddressedSafetyRelatedMessage) message;
//                log.info("AddressedSafetyRelatedMessage: {}",
//                        shipMessage);
//                break;
//            }
//            case SafetyRelatedAcknowledge: {
//                SafetyRelatedAcknowledge shipMessage =
//                        (SafetyRelatedAcknowledge) message;
//                log.info("SafetyRelatedAcknowledge: {}", shipMessage);
//                break;
//            }
//            case SafetyRelatedBroadcastMessage: {
//                SafetyRelatedBroadcastMessage shipMessage =
//                        (SafetyRelatedBroadcastMessage) message;
//                log.info("SafetyRelatedBroadcastMessage: {}",
//                        shipMessage);
//                break;
//            }
//            case Interrogation: {
//                Interrogation shipMessage = (Interrogation) message;
//                log.info("Interrogation: {}", shipMessage);
//                break;
//            }
//            case AssignedModeCommand: {
//                AssignedModeCommand shipMessage =
//                        (AssignedModeCommand) message;
//                log.info("AssignedModeCommand: {}", shipMessage);
//                break;
//            }
//            case GNSSBinaryBroadcastMessage: {
//                GNSSBinaryBroadcastMessage shipMessage =
//                        (GNSSBinaryBroadcastMessage) message;
//                log.info("GNSSBinaryBroadcastMessage: {}", shipMessage);
//                break;
//            }
//            case StandardClassBCSPositionReport: {
//                StandardClassBCSPositionReport shipMessage =
//                        (StandardClassBCSPositionReport) message;
//                log.info("StandardClassBCSPositionReport: {}",
//                        shipMessage);
//                break;
//            }
//            case ExtendedClassBEquipmentPositionReport: {
//                ExtendedClassBEquipmentPositionReport shipMessage =
//                        (ExtendedClassBEquipmentPositionReport) message;
//                log.info("ExtendedClassBEquipmentPositionReport: {}",
//                        shipMessage);
//                break;
//            }
//            case DataLinkManagement: {
//                DataLinkManagement shipMessage =
//                        (DataLinkManagement) message;
//                log.info("DataLinkManagement: {}", shipMessage);
//                break;
//            }
//            case AidToNavigationReport: {
//                AidToNavigationReport shipMessage =
//                        (AidToNavigationReport) message;
//                log.info("AidToNavigationReport: {}", shipMessage);
//                break;
//            }
//            case ChannelManagement: {
//                ChannelManagement shipMessage =
//                        (ChannelManagement) message;
//                log.info("ChannelManagement: {}", shipMessage);
//                break;
//            }
//            case GroupAssignmentCommand: {
//                GroupAssignmentCommand shipMessage =
//                        (GroupAssignmentCommand) message;
//                log.info("GroupAssignmentCommand: {}", shipMessage);
//                break;
//            }
//            case ClassBCSStaticDataReport: {
//                ClassBCSStaticDataReport shipMessage =
//                        (ClassBCSStaticDataReport) message;
//                log.info("ClassBCSStaticDataReport: {}", shipMessage);
//                break;
//            }
//            case BinaryMessageSingleSlot: {
//                BinaryMessageSingleSlot shipMessage =
//                        (BinaryMessageSingleSlot) message;
//                log.info("BinaryMessageSingleSlot: {}", shipMessage);
//                break;
//            }
//            case BinaryMessageMultipleSlot: {
//                BinaryMessageMultipleSlot shipMessage =
//                        (BinaryMessageMultipleSlot) message;
//                log.info("BinaryMessageMultipleSlot: {}", shipMessage);
//                break;
//            }
//            case LongRangeBroadcastMessage: {
//                LongRangeBroadcastMessage shipMessage =
//                        (LongRangeBroadcastMessage) message;
//                log.info("LongRangeBroadcastMessage: {}", shipMessage);
//                break;
//            }
//            default:
//                throw new UnsupportedMessageType(messageType.getCode());
//        }
//        if (newMessage != null) {
//            newMessage.copyFrom(msg);
//        }
//        return newMessage;
//    }
//}
