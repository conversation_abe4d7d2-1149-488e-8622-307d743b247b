<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.mapper.DisposeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="DisposeLogResult" type="com.xx.domain.entity.DisposeLog">
                <id column="id" property="id"/>
                <result column="warning" property="warning"/>
                <result column="vhf_content" property="vhfContent"/>
                <result column="loudspeaker_content" property="loudspeakerContent"/>
                <result column="ais_content" property="aisContent"/>
                <result column="sms_template" property="smsTemplate"/>
                <result column="hand_over" property="handOver"/>
                <result column="event_record" property="eventRecord"/>
                <result column="track_record" property="trackRecord"/>
                <result column="video_record" property="videoRecord"/>
                <result column="gmt_create" property="gmtCreate"/>
                <result column="env_type" property="envType"/>
                <result column="env_id" property="envId"/>
                <result column="mmsi" property="mmsi"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectDisposeLogVo">
        select `id`, `warning`, `vhf_content`, `loudspeaker_content`, `ais_content`, `sms_template`, `hand_over`, `event_record`, `track_record`, `video_record`, `gmt_create`, 
        `env_type`, `env_id`, `mmsi` from dispose_log
    </sql>

</mapper>
