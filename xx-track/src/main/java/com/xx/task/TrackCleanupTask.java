package com.xx.task;

import com.xx.web.service.impl.MgTrackServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Component
@RequiredArgsConstructor
public class TrackCleanupTask {

    private final MongoTemplate mongoTemplate;


    @Scheduled(cron = "0 0 0 1 * ?") // 每个月1号凌晨执行任务
    public void deleteOldData() {
        // 获取上个月的1号日期
        LocalDate currentDate = LocalDate.now();
        //LocalDate lastMonthFirstDay = LocalDate.of(currentDate.getYear(), currentDate.getMonth().minus(1), 1);
        LocalDate threeMonthsAgo = currentDate.minusMonths(3);
        // 将日期格式化为您表格或集合的名称格式
        String tableName = MgTrackServiceImpl.getCollectionNameFromDate(threeMonthsAgo);
        // 在这里添加删除表格的代码，具体操作取决于您使用的数据库和驱动
        // 删除集合
        mongoTemplate.getCollection(tableName).drop();
        // 更新为下一天的日期
        threeMonthsAgo = threeMonthsAgo.plusDays(1);
    }
}
