package com.xx.websocket.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: WebSocketConfig
 * @Description:
 * @Date: 2023/4/11 14:11
 * @since JDK 1.8
 */
@Configuration
public class WebSocketConfig  {
    /**
     * ServerEndpointExporter 作用
     * 这个Bean会自动注册使用@ServerEndpoint注解声明的websocket endpoint
     * @return
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
    /*
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        //container.setMaxTextMessageBufferSize(8192*10); // 设置文本消息的最大大小为8192字节
        //container.setMaxBinaryMessageBufferSize(8192*10); // 设置二进制消息的最大大小为8192字节
        return container;
    }
    */


}
