package com.xx.linkage.handler;

import com.xx.domain.entity.DisposeLog;
import com.xx.web.domain.dto.EnvDto;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.proto.ShipOuterClass;

public abstract class AbstractDisposeHandler implements DisposeHandler {
    private DisposeHandler next;

    public void handle(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto, DisposeLog disposeLog) {
        if (test(disposeRule)) {
            accept(disposeRule, ship, envDto,disposeLog);
        }

        if (next != null) {
            next.handle(disposeRule, ship, envDto,disposeLog);
        }
    }

    public DisposeHandler getNext() {
        return next;
    }

    public void setNext(Dispose<PERSON>and<PERSON> next) {
        this.next = next;
    }

    protected abstract boolean test(DisposeRule disposeRule);

    protected abstract void accept(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto,DisposeLog disposeLog);
}
