package com.xx.linkage.handler;

import com.alibaba.fastjson2.JSON;
import com.xx.common.constant.Constants;
import com.xx.domain.entity.DisposeLog;
import com.xx.utils.giu.BmapPoint;
import com.xx.utils.redis.RedisUtil;
import com.xx.web.domain.dto.EarlyWarningTrackDto;
import com.xx.web.domain.dto.EnvDto;
import com.xx.web.domain.dto.WarningInfo;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.domain.entity.EarlyWarningRule;
import com.xx.web.proto.ShipOuterClass;
import com.xx.web.service.IEarlyWarningService;
import com.xx.web.service.IShipTrackService;
import com.xx.websocket.server.EarlyPushServer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class WarningHandler extends AbstractDisposeHandler {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IShipTrackService shipTrackService;

    @Autowired
    private IEarlyWarningService earlyWarningService;

    @Value("${ef.ew-past}")
    private Long past;

    //private static ObjectMapper objectMap = new ObjectMapper();

    protected boolean test(DisposeRule disposeRule) {
        return disposeRule.getWarning();
    }

    protected void accept(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto, DisposeLog disposeLog) {
        // 开启预警
        log.info("开启预警~~{}",ship.getId());

        EarlyWarningRule rule = redisUtil.hmGet(Constants.WARNING_RULES, envDto.getEnvTypeId() + "");
        String key = Constants.TRACK_EARLY_KEY + ship.getId();
        //log.info("预警规则:{}" ,rule);
        pushWarning(ship, envDto, rule, key,disposeLog);

    }

    private  static Map<String,String> map = new HashMap<String,String>(){{
        put("one_warning", "红色预警");
        put("two_warning", "橙色预警");
        put("three_warning", "蓝色预警");
        put("shipping_lane", "航道偏离预警");
    }};
    private void pushWarning(ShipOuterClass.Ship ship, EnvDto envDto, EarlyWarningRule rule, String key,DisposeLog disposeLog) {
        //TrackInfo trackInfo = redisUtil.get(key);
        //Long time = trackInfo.getTime();
        //if ((ship.getTime() - time) > rule.getDuration() * 60000) {
        //if (true) {
            //持续超过规则时间
            //报警
            EarlyWarningTrackDto trackDto = new EarlyWarningTrackDto();
            trackDto.setMmsi(ship.getMmsi());
            trackDto.setShipName(StringUtils.isBlank(ship.getChineseName()) ? ship.getName() : ship.getChineseName());
            //trackDto.setEventContent(rule.getName());
            trackDto.setEventContent(map.get(envDto.getEnvType()));
            trackDto.setAddress(envDto.getName());
            trackDto.setLon(ship.getLon());
            trackDto.setLat(ship.getLat());
            trackDto.setShipType(ship.getShipType());
            trackDto.setWarningTime(ship.getTime());
            trackDto.setEndTime(ship.getTime() + 7200); //秒
            trackDto.setEnvId(envDto.getId());
            trackDto.setEnvType(envDto.getEnvTypeId());
            trackDto.setPlace(envDto.getPlace());


            WarningInfo warningInfo = new WarningInfo();
            warningInfo.setMmsi(ship.getMmsi());
            warningInfo.setShipName(trackDto.getShipName());
            warningInfo.setEnvType(envDto.getEnvType());
            warningInfo.setEnvId(trackDto.getEnvId());
            warningInfo.setPoint(new BmapPoint(trackDto.getLon(), trackDto.getLat()));
            warningInfo.setGroup(envDto.getGroup());


            //查询近一个小时的航迹保存
            //TODO 可能需要优化,航迹数据有可能很多 需要过滤
            earlyWarningService.saveEarlyWarningTrack(trackDto);
            trackDto.setTrack(shipTrackService.history(ship.getId(), 1.0));
            disposeLog.setMmsi(trackDto.getMmsi());
            disposeLog.setWarningId(trackDto.getId());
            disposeLog.setEnvType(envDto.getEnvType());
            disposeLog.setEnvId(trackDto.getEnvId());
            redisUtil.set(Constants.WARNING_LIST + ship.getId(), warningInfo,7200L);
            EarlyPushServer.sendInfo2All(trackDto.getPlace(),JSON.toJSONString(trackDto));

        //}

    }
}
