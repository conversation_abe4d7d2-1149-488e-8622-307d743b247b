package com.xx.linkage.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xx.enums.DeviceEnum;
import com.xx.web.domain.pojo.Camera;
import com.xx.web.domain.pojo.DeviceOwnAttr;
import com.xx.web.domain.pojo.Loudspeaker;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

@Slf4j
@MappedJdbcTypes(value = {JdbcType.OTHER}, includeNullJdbcType = true)
public class DeviceEntityTypeHandler extends BaseTypeHandler<DeviceOwnAttr> {

    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, DeviceOwnAttr parameter, JdbcType jdbcType) throws SQLException {
        String json = null;
        try {
            json = mapper.writeValueAsString(parameter);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        ps.setString(i, json);
    }

    @Override
    public DeviceOwnAttr getNullableResult(ResultSet rs, String columnName) throws SQLException {
        DeviceEnum type = (DeviceEnum) rs.getObject("type");
        String json = rs.getString(columnName);
        return deserialize(json, type);
    }

    @Override
    public DeviceOwnAttr getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String json = rs.getString(columnIndex);
        return deserialize(json, (DeviceEnum) rs.getObject("type"));
    }

    @Override
    public DeviceOwnAttr getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String json = cs.getString(columnIndex);
        return deserialize(json, (DeviceEnum) cs.getObject("type"));
    }

    private DeviceOwnAttr deserialize(String json, DeviceEnum type) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        try {
            System.out.println("deviceType: " + type);
            switch (type) {
                case CAMERA:
                    System.out.println("摄像头");
                    break;
                case VHF:
                    System.out.println("VHF");
                    break;
                case SEARCHLIGHT:
                    System.out.println("探照灯");
                    break;
                default:
                    System.out.println("其他");
            }
            // 这里需要根据你的实际情况来判断应该反序列化为哪个类
            if (json.contains("\"type\":\"Camera\"")) {
                return mapper.readValue(json, Camera.class);
            } else if (json.contains("\"type\":\"Sensor\"")) {
                return mapper.readValue(json, Loudspeaker.class);
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
