package com.xx.linkage.handler;

import com.xx.domain.entity.DisposeLog;
import com.xx.service.IDisposeLogService;
import com.xx.web.domain.dto.EnvDto;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.proto.ShipOuterClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LogHandler extends AbstractDisposeHandler {
    @Autowired
    private IDisposeLogService disposeLogService;
    protected boolean test(DisposeRule disposeRule) {
        return true;
    }

    protected void accept(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto,DisposeLog disposeLog) {
        log.info("联动日志---");
        // 日志记录
        if (disposeLog.isSave()) {
            disposeLogService.add(disposeLog);
            log.info("联动日志记录: {}",disposeLog);
        }

    }
}
