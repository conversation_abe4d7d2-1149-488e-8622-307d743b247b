package com.xx.linkage.handler;

import com.xx.domain.entity.DisposeLog;
import com.xx.web.domain.dto.EnvDto;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.proto.ShipOuterClass;

public interface DisposeHandler {
    void handle(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto, DisposeLog disposeLog);

    DisposeHandler getNext();

    void setNext(Dispose<PERSON>and<PERSON> next);
}

