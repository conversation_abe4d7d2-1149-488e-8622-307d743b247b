package com.xx.linkage.handler;

import com.xx.domain.entity.DisposeLog;
import com.xx.web.domain.dto.EnvDto;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.proto.ShipOuterClass;
import org.springframework.stereotype.Component;

@Component
public class VideoRecordHandler extends AbstractDisposeHandler {
    protected boolean test(DisposeRule disposeRule) {
        return disposeRule.getVideoRecord();
    }

    protected void accept(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto, DisposeLog disposeLog) {
        // 开启 视频记录
    }
}
