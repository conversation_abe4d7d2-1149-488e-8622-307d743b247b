package com.xx.linkage.chain;

import com.xx.domain.entity.DisposeLog;
import com.xx.linkage.handler.*;
import com.xx.web.domain.dto.EnvDto;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.proto.ShipOuterClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DisposeChain {
    private DisposeHandler firstHandler;


    @Autowired
    public DisposeChain(WarningHandler warningHandler, VhfHandler vhfHandler, LoudspeakerHandler loudspeakerHandler, SearchlightHandler searchlightHandler,
                        AisHandler aisHandler, SmsH<PERSON><PERSON> smsHandler, HandOverHandler handOverHandler, EventRecordHandler eventRecordHandler,
                        TrackRecordHandler trackRecordHandler, VideoRecordHand<PERSON> videoRecordHandler,LogHandler logHandler) {
        warningHandler.setNext(vhfHandler);
        vhfHandler.setNext(loudspeakerHandler);
        loudspeakerHandler.setNext(searchlightHandler);
        searchlightHandler.setNext(aisHandler);
        aisHandler.setNext(smsHandler);
        smsHandler.setNext(handOverHandler);
        handOverHandler.setNext(eventRecordHandler);
        eventRecordHandler.setNext(trackRecordHandler);
        trackRecordHandler.setNext(videoRecordHandler);
        videoRecordHandler.setNext(logHandler);
        log.info("初始化firstHandler");
        firstHandler = warningHandler;
    }

    public void handle(DisposeRule disposeRule, ShipOuterClass.Ship ship, EnvDto envDto) {
        firstHandler.handle(disposeRule, ship,envDto,new DisposeLog());
    }
}
