package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.BaseStationReport;
import org.springframework.stereotype.Component;

/**
 * @description: 04 基站报告
 * @author: xx
 * @Date 2023/7/14 17:34
 * @version: 1.0
 */
@Component
public class BaseStationReportHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        BaseStationReport data = (BaseStationReport) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
