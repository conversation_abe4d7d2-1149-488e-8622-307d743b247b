package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.Error;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/14 18:06
 * @version: 1.0
 */
@Component
@Slf4j
public class ErrorHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        Error data = (Error) message;
        log.error(data.toString());
    }
}
