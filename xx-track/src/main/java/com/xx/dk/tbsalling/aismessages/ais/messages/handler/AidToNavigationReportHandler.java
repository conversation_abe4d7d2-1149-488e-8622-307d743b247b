package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.AidToNavigationReport;
import org.springframework.stereotype.Component;

/**
 * @description: 21 助航报告
 * @author: xx
 * @Date 2023/7/14 17:47
 * @version: 1.0
 */
@Component
public class AidToNavigationReportHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        AidToNavigationReport data = (AidToNavigationReport) message;
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
