/*
 * AISMessages
 * - a java-based library for decoding of AIS messages from digital VHF radio traffic related
 * to maritime navigation and safety in compliance with ITU 1371.
 *
 * (C) Copyright 2011- by S-<PERSON>t ApS, VAT no. **********, Denmark.
 *
 * Released under the Creative Commons Attribution-NonCommercial-ShareAlike 3.0 Unported License.
 * For details of this license see the nearby LICENCE-full file, visit http://creativecommons.org/licenses/by-nc-sa/3.0/
 * or send a letter to Creative Commons, 171 Second Street, Suite 300, San Francisco, California, 94105, USA.
 *
 * NOT FOR COMMERCIAL USE!
 * Contact <PERSON> <<EMAIL>> to obtain a commercially licensed version of this software.
 *
 */

package com.xx.dk.tbsalling.aismessages.nmea;

import com.xx.dk.tbsalling.aismessages.nmea.exceptions.InvalidMessage;
import com.xx.dk.tbsalling.aismessages.nmea.exceptions.NMEAParseException;
import com.xx.dk.tbsalling.aismessages.nmea.exceptions.UnsupportedMessageType;
import com.xx.dk.tbsalling.aismessages.nmea.messages.NMEAMessage;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Supplier;

@Slf4j
public class NMEAMessageInputStreamReader {

	//private static final System.Logger LOG = System.getLogger(NMEAMessageInputStreamReader.class.getName());

	public NMEAMessageInputStreamReader(List<String> nmeaStrings, Consumer<? super NMEAMessage> nmeaMessageHandler) {
		Objects.requireNonNull(nmeaStrings, "nmeaStrings cannot be null.");
		Objects.requireNonNull(nmeaMessageHandler, "nmeaMessageHandler cannot be null.");

		if (nmeaStrings instanceof Queue)
			this.stringSupplier = () -> ((Queue<String>) nmeaStrings).poll();
		else {
			final Queue<String> nmeaStringsQueue = new LinkedList<>(nmeaStrings);
			this.stringSupplier = () -> nmeaStringsQueue.poll();
		}

		this.nmeaMessageHandler = nmeaMessageHandler;
	}

	public NMEAMessageInputStreamReader(InputStream inputStream, Consumer<? super NMEAMessage> nmeaMessageHandler) {
		this.nmeaMessageHandler = nmeaMessageHandler;

		InputStreamReader reader = new InputStreamReader(inputStream, Charset.defaultCharset());
		BufferedReader bufferedReader = new BufferedReader(reader);
		this.stringSupplier = () -> {
			try {
				return bufferedReader.readLine();
			} catch (IOException e) {
				throw new RuntimeException(e.getMessage(), e);
			}
		};
	}

	public final void requestStop() {
		this.stopRequested.set(true);
	}

	public void run() {
		//log.info("NMEAMessageInputStreamReader running.");

		String string;
		while ((string = stringSupplier.get()) != null) {
			if (isStopRequested())
				break;

			try {
				NMEAMessage nmea = NMEAMessage.fromString(string);
				nmeaMessageHandler.accept(nmea);
				log.debug("Received: " + nmea.toString());
			} catch (InvalidMessage invalidMessageException) {
				log.warn("Received invalid AIS message: \"" + string + "\"");
			} catch (UnsupportedMessageType unsupportedMessageTypeException) {
				log.warn("Received unsupported NMEA message: \"" + string + "\"");
			} catch (NMEAParseException parseException) {
				log.warn("Received non-compliant NMEA message: \"" + string + "\"");
			}
		}

		//log.info("NMEAMessageInputStreamReader stopping.");
	}

	public final Boolean isStopRequested() {
		return this.stopRequested.get();
	}

	private final AtomicBoolean stopRequested = new AtomicBoolean(false);
	private final Supplier<String> stringSupplier;
	private final Consumer<? super NMEAMessage> nmeaMessageHandler;
}
