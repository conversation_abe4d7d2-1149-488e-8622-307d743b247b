package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.GroupAssignmentCommand;
import org.springframework.stereotype.Component;

/**
 * @description: 23 组分配命令
 * @author: xx
 * @Date 2023/7/14 17:48
 * @version: 1.0
 */
@Component
public class GroupAssignmentCommandHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        GroupAssignmentCommand data = (GroupAssignmentCommand) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
