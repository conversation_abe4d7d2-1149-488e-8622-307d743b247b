package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.BinaryMessageMultipleSlot;
import org.springframework.stereotype.Component;

/**
 * @description: 26 具有通信状态的多插槽二进制消息
 * @author: xx
 * @Date 2023/7/14 17:52
 * @version: 1.0
 */
@Component
public class BinaryMessageMultipleSlotHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        BinaryMessageMultipleSlot data = (BinaryMessageMultipleSlot) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
