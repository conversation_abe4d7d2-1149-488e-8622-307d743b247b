package com.xx.dk.tbsalling.aismessages.utils;

import java.util.*;

public class LimitedSizeMap<K, V> extends LinkedHashMap<K, V> {
    private int maxSize;
    public final Object lock = new Object();
    public LimitedSizeMap(int maxSize) {
        super(maxSize);
        this.maxSize = maxSize;
    }

    /**
     * 这个类继承自 LinkedHashMap，使用 maxSize 参数来指定队列的大小。在构造函数中，使用 super(maxSize, 0.75f, true) 来调用父类的构造函数，并传入 maxSize、0.75 和
     * true 三个参数，表示使用 LRU（最近最少使用）策略来移除不经常使用的元素，并且开启访问顺序排序。
     */
    public LimitedSizeMap(int maxSize, float loadFactor) {
        //0.75f
        super(maxSize, loadFactor, true);
        this.maxSize = maxSize;
    }


    /**
     *在 removeEldestEntry 方法中，使用 size() > maxSize
     * 来判断队列是否已经达到最大大小，如果是，则移除最旧的元素。
     */
    protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
        return size() > maxSize;
    }

    @Override
    public Set<Map.Entry<K, V>> entrySet() {
        synchronized (lock) {
            return new HashSet<>(super.entrySet());
        }
    }

}
