package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.UTCAndDateResponse;
import org.springframework.stereotype.Component;

/**
 * @description: 11 UTC和日期响应
 * @author: xx
 * @Date 2023/7/14 17:39
 * @version: 1.0
 */
@Component
public class UTCAndDateResponseHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        UTCAndDateResponse data = (UTCAndDateResponse) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
