package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.SafetyRelatedBroadcastMessage;
import org.springframework.stereotype.Component;

/**
 * @description: 14 安全相关广播消息
 * @author: xx
 * @Date 2023/7/14 17:41
 * @version: 1.0
 */
@Component
public class SafetyRelatedBroadcastMessageHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        SafetyRelatedBroadcastMessage data = (SafetyRelatedBroadcastMessage) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
