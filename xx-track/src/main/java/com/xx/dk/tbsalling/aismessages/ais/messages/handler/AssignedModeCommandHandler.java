package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.AssignedModeCommand;
import org.springframework.stereotype.Component;

/**
 * @description: 16 分配模式命令
 * @author: xx
 * @Date 2023/7/14 17:42
 * @version: 1.0
 */
@Component
public class AssignedModeCommandHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        AssignedModeCommand data = (AssignedModeCommand) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
