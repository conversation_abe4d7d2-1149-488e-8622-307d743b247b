package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.LongRangeBroadcastMessage;
import org.springframework.stereotype.Component;

/**
 * @description: 27 远程应用的位置报告
 * @author: xx
 * @Date 2023/7/14 17:52
 * @version: 1.0
 */
@Component
public class LongRangeBroadcastMessageHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        LongRangeBroadcastMessage data = (LongRangeBroadcastMessage) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
