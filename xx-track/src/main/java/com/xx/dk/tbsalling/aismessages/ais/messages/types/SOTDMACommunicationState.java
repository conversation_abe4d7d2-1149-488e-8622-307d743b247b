/*
 * AISMessages
 * - a java-based library for decoding of AIS messages from digital VHF radio traffic related
 * to maritime navigation and safety in compliance with ITU 1371.
 *
 * (C) Copyright 2011- by S-Consult ApS, VAT no. **********, Denmark.
 *
 * Released under the Creative Commons Attribution-NonCommercial-ShareAlike 3.0 Unported License.
 * For details of this license see the nearby LICENCE-full file, visit http://creativecommons.org/licenses/by-nc-sa/3.0/
 * or send a letter to Creative Commons, 171 Second Street, Suite 300, San Francisco, California, 94105, USA.
 *
 * NOT FOR COMMERCIAL USE!
 * Contact <PERSON> <<EMAIL>> to obtain a commercially licensed version of this software.
 *
 */

package com.xx.dk.tbsalling.aismessages.ais.messages.types;

import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

import static com.xx.dk.tbsalling.aismessages.ais.Decoders.UNSIGNED_INTEGER_DECODER;
import static java.util.Objects.requireNonNull;

@Slf4j
public class SOTDMACommunicationState extends CommunicationState implements Serializable {

	//private static final System.Logger LOG = System.getLogger(SOTDMACommunicationState.class.getName());

	private SOTDMACommunicationState(SyncState syncState, Integer slotTimeout, Integer numberOfReceivedStations, Integer slotNumber, Integer utcHour, Integer utcMinute, Integer slotOffset) {
		super(syncState);
		this.slotTimeout = slotTimeout;
		this.numberOfReceivedStations = numberOfReceivedStations;
		this.slotNumber = slotNumber;
		this.utcHour = utcHour;
		this.utcMinute = utcMinute;
		this.slotOffset = slotOffset;
	}

	public static SOTDMACommunicationState fromBitString(String bitString) {
		requireNonNull(bitString);
		bitString = bitString.trim();

		if (bitString.length() != 19 || !bitString.matches("(0|1)*"))
			return null;

		SyncState syncState = SyncState.fromInteger(UNSIGNED_INTEGER_DECODER.apply(bitString.substring(0, 2)));
		final int slotTimeout = UNSIGNED_INTEGER_DECODER.apply(bitString.substring(2, 5));
		Integer numberOfReceivedStations=null, slotNumber=null, utcHour=null, utcMinute=null, slotOffset=null;

		if (slotTimeout == 3 || slotTimeout == 5 || slotTimeout == 7) {
			numberOfReceivedStations = UNSIGNED_INTEGER_DECODER.apply(bitString.substring(5, 19));
			if (numberOfReceivedStations > 16383)
				//LOG.log(WARNING,"numberOfReceivedStations: " + numberOfReceivedStations + ": Out of range.");
				log.warn("numberOfReceivedStations: " + numberOfReceivedStations + ": Out of range.");
		} else if (slotTimeout == 2 || slotTimeout == 4 || slotTimeout == 6) {
			slotNumber = UNSIGNED_INTEGER_DECODER.apply(bitString.substring(5, 19));
			if (slotNumber > 2249)
				log.warn("slotNumber: " + slotNumber + ": Out of range.");
		}  else if (slotTimeout == 1) {
			utcHour = UNSIGNED_INTEGER_DECODER.apply(bitString.substring(5, 10));
			if (utcHour > 23)
				log.warn("utcHour: " + utcHour + ": Out of range.");
			utcMinute = UNSIGNED_INTEGER_DECODER.apply(bitString.substring(10, 17));
			if (utcMinute > 59)
				log.warn("utcMinute: " + utcMinute + ": Out of range.");
		}  else if (slotTimeout == 0) {
			slotOffset = UNSIGNED_INTEGER_DECODER.apply(bitString.substring(5, 19));
		}

		return new SOTDMACommunicationState(syncState, slotTimeout, numberOfReceivedStations, slotNumber, utcHour, utcMinute, slotOffset);
	}

	@SuppressWarnings("unused")
	public Integer getSlotTimeout() {
		return slotTimeout;
	}

	@SuppressWarnings("unused")
	public Integer getNumberOfReceivedStations() {
		return numberOfReceivedStations;
	}

	@SuppressWarnings("unused")
	public Integer getSlotNumber() {
		return slotNumber;
	}

	@SuppressWarnings("unused")
	public Integer getUtcHour() {
		return utcHour;
	}

	@SuppressWarnings("unused")
	public Integer getUtcMinute() {
		return utcMinute;
	}

	@SuppressWarnings("unused")
	public Integer getSlotOffset() {
		return slotOffset;
	}

	private Integer slotTimeout;
	private Integer numberOfReceivedStations;
	private Integer slotNumber;
	private Integer utcHour;
	private Integer utcMinute;
	private Integer slotOffset;
}
