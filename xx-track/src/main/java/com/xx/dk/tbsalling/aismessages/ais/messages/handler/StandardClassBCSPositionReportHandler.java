package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.StandardClassBCSPositionReport;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 18 标准B级CS位置报告
 * @author: xx
 * @Date 2023/7/14 17:43
 * @version: 1.0
 */
@Component
@RequiredArgsConstructor
public class StandardClassBCSPositionReportHandler  implements AISMessageHandler {
    private final DynamicDataReportHandler dynamicDataReportHandler;
    @Override
    public void handle(AISMessage message) {
        StandardClassBCSPositionReport data = (StandardClassBCSPositionReport) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
        dynamicDataReportHandler.extendedDynamicDataReportHandler(data);
    }
}
