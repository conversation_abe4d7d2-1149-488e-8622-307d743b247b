package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.GNSSBinaryBroadcastMessage;
import org.springframework.stereotype.Component;

/**
 * @description: 17 DGNSS二进制广播消息
 * @author: xx
 * @Date 2023/7/14 17:43
 * @version: 1.0
 */
@Component
public class GNSSBinaryBroadcastMessageHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        GNSSBinaryBroadcastMessage data = (GNSSBinaryBroadcastMessage) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
