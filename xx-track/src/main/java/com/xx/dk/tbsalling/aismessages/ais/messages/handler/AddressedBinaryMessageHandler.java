package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.AddressedBinaryMessage;
import org.springframework.stereotype.Component;

/**
 * @description: 06 二进制地址的消息
 * @author: xx
 * @Date 2023/7/14 17:36
 * @version: 1.0
 */
@Component
public class AddressedBinaryMessageHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        AddressedBinaryMessage data = (AddressedBinaryMessage) message;
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
