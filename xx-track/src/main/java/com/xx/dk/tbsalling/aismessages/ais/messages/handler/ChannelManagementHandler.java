package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.ChannelManagement;
import org.springframework.stereotype.Component;

/**
 * @description: 22 渠道管理
 * @author: xx
 * @Date 2023/7/14 17:47
 * @version: 1.0
 */
@Component
public class ChannelManagementHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        ChannelManagement data = (ChannelManagement) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
