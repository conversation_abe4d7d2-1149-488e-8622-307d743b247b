package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.DataLinkManagement;
import org.springframework.stereotype.Component;

/**
 * @description: 20 数据链接管理
 * @author: xx
 * @Date 2023/7/14 17:46
 * @version: 1.0
 */
@Component
public class DataLinkManagementHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        DataLinkManagement data = (DataLinkManagement) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
