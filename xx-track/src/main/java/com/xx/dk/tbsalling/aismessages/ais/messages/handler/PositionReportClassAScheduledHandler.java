package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReportClassAScheduled;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 01 位置报告A类
 * @author: xx
 * @Date 2023/7/6
 */
@Component
@RequiredArgsConstructor
public class PositionReportClassAScheduledHandler implements AISMessageHandler {

    private final DynamicDataReportHandler dynamicDataReportHandler;

    @Override
    public void handle(AISMessage message) {
        PositionReportClassAScheduled data = (PositionReportClassAScheduled) message;
        ////System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
        dynamicDataReportHandler.positionReportDispose(data);
    }
}
