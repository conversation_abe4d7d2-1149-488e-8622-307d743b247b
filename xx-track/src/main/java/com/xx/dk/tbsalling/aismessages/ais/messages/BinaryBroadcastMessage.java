/*
 * AISMessages
 * - a java-based library for decoding of AIS messages from digital VHF radio traffic related
 * to maritime navigation and safety in compliance with ITU 1371.
 *
 * (C) Copyright 2011- by S-Consult ApS, VAT no. **********, Denmark.
 *
 * Released under the Creative Commons Attribution-NonCommercial-ShareAlike 3.0 Unported License.
 * For details of this license see the nearby LICENCE-full file, visit http://creativecommons.org/licenses/by-nc-sa/3.0/
 * or send a letter to Creative Commons, 171 Second Street, Suite 300, San Francisco, California, 94105, USA.
 *
 * NOT FOR COMMERCIAL USE!
 * Contact <PERSON> <<EMAIL>> to obtain a commercially licensed version of this software.
 *
 */

package com.xx.dk.tbsalling.aismessages.ais.messages;

import com.xx.dk.tbsalling.aismessages.ais.messages.asm.ApplicationSpecificMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.types.AISMessageType;
import com.xx.dk.tbsalling.aismessages.nmea.exceptions.InvalidMessage;
import com.xx.dk.tbsalling.aismessages.nmea.messages.NMEAMessage;

import java.lang.ref.WeakReference;

import static com.xx.dk.tbsalling.aismessages.ais.Decoders.BIT_DECODER;
import static com.xx.dk.tbsalling.aismessages.ais.Decoders.UNSIGNED_INTEGER_DECODER;
import static java.lang.String.format;

/**
 * 具有未指定二进制有效负载的广播消息。圣劳伦斯航道
 * AIS 系统和 USG PAWSS 系统使用此负载进行本地扩展
 *消息。它的长度可变，最大为 1008 位（最多 5 位
 * AIVDM 句子有效载荷）。
 *
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class BinaryBroadcastMessage extends AISMessage {

    public BinaryBroadcastMessage(NMEAMessage[] nmeaMessages) {
        super(nmeaMessages);
    }

    protected BinaryBroadcastMessage(NMEAMessage[] nmeaMessages, String bitString) {
        super(nmeaMessages, bitString);
    }

    @Override
    protected void checkAISMessage() {
        super.checkAISMessage();

        final StringBuffer errorMessage = new StringBuffer();

        final int numberOfBits = getNumberOfBits();

        if (numberOfBits <= 56) {
            errorMessage.append(String.format("Message of type %s should be at least 56 bits long; not %d.", getMessageType(), numberOfBits));

            if (numberOfBits >= 40)
                errorMessage.append(format(" Unparseable binary payload: \"%s\".", getBits(40, numberOfBits)));
        } else if (numberOfBits > 1008)
            errorMessage.append(String.format("Message of type %s should be at least 56 bits long; not %d.", getMessageType(), numberOfBits));

        if (errorMessage.length() > 0) {
            if (numberOfBits >= 38)
                errorMessage.append(String.format(" Assumed sourceMmsi: %d.", getSourceMmsi().getMMSI()));

            throw new InvalidMessage(errorMessage.toString());
        }
    }

    public final AISMessageType getMessageType() {
        return AISMessageType.BinaryBroadcastMessage;
    }

    @SuppressWarnings("unused")
	public Integer getSpare() {
        return getDecodedValue(() -> spare, ref -> spare = ref, () -> Boolean.TRUE, () -> UNSIGNED_INTEGER_DECODER.apply(getBits(38, 40)));
	}

    @SuppressWarnings("unused")
	public Integer getDesignatedAreaCode() {
        return getDecodedValue(() -> designatedAreaCode, value -> designatedAreaCode = value, () -> Boolean.TRUE, () -> UNSIGNED_INTEGER_DECODER.apply(getBits(40, 50)));
	}

    @SuppressWarnings("unused")
	public Integer getFunctionalId() {
        return getDecodedValue(() -> functionalId, value -> functionalId = value, () -> Boolean.TRUE, () -> UNSIGNED_INTEGER_DECODER.apply(getBits(50, 56)));
	}

    @SuppressWarnings("unused")
	public String getBinaryData() {
        return getDecodedValueByWeakReference(() -> binaryData, value -> binaryData = value, () -> Boolean.TRUE, () -> BIT_DECODER.apply(getBits(56, getNumberOfBits())));
	}

    @SuppressWarnings("unused")
    public ApplicationSpecificMessage getApplicationSpecificMessage() {
        ApplicationSpecificMessage asm = this.applicationSpecificMessage == null ? null : this.applicationSpecificMessage.get();
        if (asm == null) {
            asm = ApplicationSpecificMessage.create(getDesignatedAreaCode(), getFunctionalId(), getBinaryData());
            applicationSpecificMessage = new WeakReference<>(asm);
        }

        if (asm.getDesignatedAreaCode() != this.getDesignatedAreaCode().intValue())
            throw new IllegalStateException("Implementation error: DAC of AISMessage does not match ASM: " + asm.getDesignatedAreaCode() + " " + this.getDesignatedAreaCode());

        if (asm.getFunctionalId() != this.getFunctionalId().intValue())
            throw new IllegalStateException("Implementation error: FI of AISMessage does not match ASM: " + asm.getFunctionalId() + " " + this.getFunctionalId());

        return asm;
    }

    @Override
    public String toString() {
        return "BinaryBroadcastMessage{" +
                "messageType=" + getMessageType() +
                ", spare=" + getSpare() +
                ", designatedAreaCode=" + getDesignatedAreaCode() +
                ", functionalId=" + getFunctionalId() +
                ", binaryData='" + getBinaryData() + '\'' +
                "} " + super.toString();
    }

    private transient Integer spare;
    private transient Integer designatedAreaCode;
	private transient Integer functionalId;
    private transient WeakReference<String> binaryData;
	private transient WeakReference<ApplicationSpecificMessage> applicationSpecificMessage;
}
