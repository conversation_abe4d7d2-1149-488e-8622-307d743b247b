package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.BinaryAcknowledge;
import org.springframework.stereotype.Component;

/**
 * @description: 07 二进制确认
 * @author: xx
 * @Date 2023/7/14 17:36
 * @version: 1.0
 */
@Component
public class BinaryAcknowledgeHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        BinaryAcknowledge data = (BinaryAcknowledge) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
