package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.BinaryBroadcastMessage;
import org.springframework.stereotype.Component;

/**
 * @description: 08 二进制广播消息
 * @author: xx
 * @Date 2023/7/14 17:37
 * @version: 1.0
 */
@Component
public class BinaryBroadcastMessageHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        BinaryBroadcastMessage data = (BinaryBroadcastMessage) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
