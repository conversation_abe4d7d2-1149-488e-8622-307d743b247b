package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.SafetyRelatedAcknowledge;
import org.springframework.stereotype.Component;

/**
 * @description: 13 安全相关确认
 * @author: xx
 * @Date 2023/7/14 17:40
 * @version: 1.0
 */
@Component
public class SafetyRelatedAcknowledgeHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        SafetyRelatedAcknowledge data = (SafetyRelatedAcknowledge) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
