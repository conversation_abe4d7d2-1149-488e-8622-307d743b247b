package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.BinaryMessageSingleSlot;
import org.springframework.stereotype.Component;

/**
 * @description: 25 单插槽二进制消息
 * @author: xx
 * @Date 2023/7/14 17:50
 * @version: 1.0
 */
@Component
public class BinaryMessageSingleSlotHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        BinaryMessageSingleSlot data = (BinaryMessageSingleSlot) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
