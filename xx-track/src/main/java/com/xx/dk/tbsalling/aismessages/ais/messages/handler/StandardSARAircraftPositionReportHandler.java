package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.StandardSARAircraftPositionReport;
import org.springframework.stereotype.Component;

/**
 * @description: 09 标准SAR飞机位置报告
 * @author: xx
 * @Date 2023/7/14 17:38
 * @version: 1.0
 */
@Component
public class StandardSARAircraftPositionReportHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        StandardSARAircraftPositionReport data = (StandardSARAircraftPositionReport) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
