package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.types.AISMessageType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: initialize AIS handler map
 * @author: xx
 * @Date 2023/7/15 10:47
 * @version: 1.0
 */
@Component
@RequiredArgsConstructor
public class AISMessageHandlerConfig {

    private final ShipAndVoyageDataHandler shipAndVoyageDataHandler;

    private final PositionReportClassAScheduledHandler positionReportClassAScheduledHandler;
    private final PositionReportClassAAssignedScheduleHandler positionReportClassAAssignedScheduleHandler;
    private final PositionReportClassAResponseToInterrogationHandler positionReportClassAResponseToInterrogationHandler;
    private final BaseStationReportHandler baseStationReportHandler;
    private final AddressedBinaryMessageHandler addressedBinaryMessageHandler;
    private final BinaryAcknowledgeHandler binaryAcknowledgeHandler;
    private final BinaryBroadcastMessageHandler binaryBroadcastMessageHandler;
    private final StandardSARAircraftPositionReportHandler standardSARAircraftPositionReportHandler;
    private final UTCAndDateInquiryHandler uTCAndDateInquiryHandler;
    private final UTCAndDateResponseHandler uTCAndDateResponseHandler;
    private final AddressedSafetyRelatedMessageHandler addressedSafetyRelatedMessageHandler;
    private final SafetyRelatedAcknowledgeHandler safetyRelatedAcknowledgeHandler;
    private final SafetyRelatedBroadcastMessageHandler safetyRelatedBroadcastMessageHandler;
    private final InterrogationHandler interrogationHandler;
    private final AssignedModeCommandHandler assignedModeCommandHandler;
    private final GNSSBinaryBroadcastMessageHandler gNSSBinaryBroadcastMessageHandler;
    private final StandardClassBCSPositionReportHandler standardClassBCSPositionReportHandler;
    private final ExtendedClassBEquipmentPositionReportHandler extendedClassBEquipmentPositionReportHandler;
    private final DataLinkManagementHandler dataLinkManagementHandler;
    private final AidToNavigationReportHandler aidToNavigationReportHandler;
    private final ChannelManagementHandler channelManagementHandler;
    private final GroupAssignmentCommandHandler groupAssignmentCommandHandler;
    private final ClassBCSStaticDataReportHandler classBCSStaticDataReportHandler;
    private final BinaryMessageSingleSlotHandler binaryMessageSingleSlotHandler;
    private final BinaryMessageMultipleSlotHandler binaryMessageMultipleSlotHandler;
    private final LongRangeBroadcastMessageHandler longRangeBroadcastMessageHandler;
    private final ErrorHandler errorHandler;

    public static Map<AISMessageType, AISMessageHandler> handlers = new HashMap<>();

    @PostConstruct
    public void init() {
        handlers.put(AISMessageType.ShipAndVoyageRelatedData, shipAndVoyageDataHandler);
        handlers.put(AISMessageType.PositionReportClassAScheduled, positionReportClassAScheduledHandler);
        handlers.put(AISMessageType.PositionReportClassAAssignedSchedule,
                positionReportClassAAssignedScheduleHandler);
        handlers.put(AISMessageType.PositionReportClassAResponseToInterrogation,
                positionReportClassAResponseToInterrogationHandler);
        handlers.put(AISMessageType.BaseStationReport, baseStationReportHandler);
        handlers.put(AISMessageType.AddressedBinaryMessage, addressedBinaryMessageHandler);
        handlers.put(AISMessageType.BinaryAcknowledge, binaryAcknowledgeHandler);
        handlers.put(AISMessageType.BinaryBroadcastMessage, binaryBroadcastMessageHandler);
        handlers.put(AISMessageType.StandardSARAircraftPositionReport, standardSARAircraftPositionReportHandler);
        handlers.put(AISMessageType.UTCAndDateInquiry, uTCAndDateInquiryHandler);
        handlers.put(AISMessageType.UTCAndDateResponse, uTCAndDateResponseHandler);
        handlers.put(AISMessageType.AddressedSafetyRelatedMessage, addressedSafetyRelatedMessageHandler);
        handlers.put(AISMessageType.SafetyRelatedAcknowledge, safetyRelatedAcknowledgeHandler);
        handlers.put(AISMessageType.SafetyRelatedBroadcastMessage, safetyRelatedBroadcastMessageHandler);
        handlers.put(AISMessageType.Interrogation, interrogationHandler);
        handlers.put(AISMessageType.AssignedModeCommand, assignedModeCommandHandler);
        handlers.put(AISMessageType.GNSSBinaryBroadcastMessage, gNSSBinaryBroadcastMessageHandler);
        handlers.put(AISMessageType.StandardClassBCSPositionReport, standardClassBCSPositionReportHandler);
        handlers.put(AISMessageType.ExtendedClassBEquipmentPositionReport,
                extendedClassBEquipmentPositionReportHandler);
        handlers.put(AISMessageType.DataLinkManagement, dataLinkManagementHandler);
        handlers.put(AISMessageType.AidToNavigationReport, aidToNavigationReportHandler);
        handlers.put(AISMessageType.ChannelManagement, channelManagementHandler);
        handlers.put(AISMessageType.GroupAssignmentCommand, groupAssignmentCommandHandler);
        handlers.put(AISMessageType.ClassBCSStaticDataReport, classBCSStaticDataReportHandler);
        handlers.put(AISMessageType.BinaryMessageSingleSlot, binaryMessageSingleSlotHandler);
        handlers.put(AISMessageType.BinaryMessageMultipleSlot, binaryMessageMultipleSlotHandler);
        handlers.put(AISMessageType.LongRangeBroadcastMessage, longRangeBroadcastMessageHandler);
        handlers.put(AISMessageType.Error, errorHandler);
    }
}
