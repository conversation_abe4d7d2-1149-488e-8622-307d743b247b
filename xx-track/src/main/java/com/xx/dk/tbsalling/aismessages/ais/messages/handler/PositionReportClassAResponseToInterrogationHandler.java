package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReportClassAResponseToInterrogation;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 03 位置报告A类（对审讯的回应）
 * @author: xx
 * @Date 2023/7/14 17:32
 * @version: 1.0
 */
@Component
@RequiredArgsConstructor
public class PositionReportClassAResponseToInterrogationHandler  implements AISMessageHandler {

    private final DynamicDataReportHandler dynamicDataReportHandler;
    @Override
    public void handle(AISMessage message) {
        PositionReportClassAResponseToInterrogation data = (PositionReportClassAResponseToInterrogation) message;
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
        dynamicDataReportHandler.positionReportDispose(data);
    }
}
