package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.Interrogation;
import org.springframework.stereotype.Component;

/**
 * @description: 15 审讯
 * @author: xx
 * @Date 2023/7/14 17:42
 * @version: 1.0
 */
@Component
public class InterrogationHandler  implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        Interrogation data = (Interrogation) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
