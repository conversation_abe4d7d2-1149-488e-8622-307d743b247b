/*
 * AIS常见问题
 * - 一个基于 Java 的库，用于解码来自数字 VHF 无线电业务相关的 AIS 消息
 * 根据 ITU 1371 对海上航行和安全的要求。
 *
 * （C） 版权所有 2011- S-Consult ApS，增值税号。**********，丹麦。
 *
 * 根据 Creative Commons 署名-非商业性使用-相同方式共享 3.0 未本地化版本许可发布。
 * 有关此许可证的详细信息，请参阅附近的 LICENCE-full 文件，请访问 http://creativecommons.org/licenses/by-nc-sa/3.0/
 * 或写信至 Creative Commons， 171 Second Street， Suite 300， San Francisco， California， 94105， USA。
 *
 * 不得用于商业用途！
 * 请联系 <PERSON> <<EMAIL>>以获取此软件的商业许可版本。
 * **
 */
package com.xx.dk.tbsalling.aismessages;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.demo.SimpleDemoApp;
import com.xx.dk.tbsalling.aismessages.nmea.NMEAMessageHandler;
import com.xx.dk.tbsalling.aismessages.nmea.NMEAMessageInputStreamReader;

import java.io.InputStream;
import java.util.List;
import java.util.function.Consumer;

/**
 * AISMessageInputStreamReader 是 AISMessages 应用程序循环的主要入口点。
 * 为其提供一个 NMEA 消息流，每当从 NMEA 流中提取出解码后的消息时，就会调用你的 AIS 消息消费者（用于处理消息的逻辑部分）。
 * @see SimpleDemoApp
 */
public class AISInputStreamReader {

    /**
     * 构造函数，使用字符串队列作为输入。
     * @param stringQueue 包含 NMEA 消息的字符串队列。
     * @param aisMessageConsumer 处理解码后的 AIS 消息的消费者。
     */
    public AISInputStreamReader(List<String> stringQueue, Consumer<? super AISMessage> aisMessageConsumer) {
        this.nmeaMessageHandler = new NMEAMessageHandler("SRC", aisMessageConsumer);
        this.nmeaMessageInputStreamReader = new NMEAMessageInputStreamReader(stringQueue, this.nmeaMessageHandler::accept);
    }

    /**
     * 构造函数，使用输入流作为输入。
     * @param inputStream 包含 NMEA 消息的输入流。
     * @param aisMessageConsumer 处理解码后的 AIS 消息的消费者。
     */
    public AISInputStreamReader(InputStream inputStream, Consumer<? super AISMessage> aisMessageConsumer) {
        this.nmeaMessageHandler = new NMEAMessageHandler("SRC", aisMessageConsumer);
        this.nmeaMessageInputStreamReader = new NMEAMessageInputStreamReader(inputStream, this.nmeaMessageHandler::accept);
    }

    /**
     * 请求停止读取和处理 NMEA 消息。
     */
    public final void requestStop() {
        this.nmeaMessageInputStreamReader.requestStop();
    }

    /**
     * 检查是否已经请求停止读取和处理 NMEA 消息。
     * @return 如果已经请求停止，则返回 true；否则返回 false。
     */
    public final boolean isStopRequested() {
        return this.nmeaMessageInputStreamReader.isStopRequested();
    }

    /**
     * 开始读取和处理 NMEA 消息。
     */
    public void run() {
        this.nmeaMessageInputStreamReader.run();
    }

    // NMEA 消息处理器，用于处理解码后的 AIS 消息。
    private final NMEAMessageHandler nmeaMessageHandler;
    // NMEA 消息输入流读取器，用于从输入流或字符串队列中读取 NMEA 消息。
	private final NMEAMessageInputStreamReader nmeaMessageInputStreamReader;
}
