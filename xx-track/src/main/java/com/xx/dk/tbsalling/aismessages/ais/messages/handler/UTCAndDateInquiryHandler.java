package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.UTCAndDateInquiry;
import org.springframework.stereotype.Component;

/**
 * @description: 10 UTC和日期查询
 * @author: xx
 * @Date 2023/7/14 17:38
 * @version: 1.0
 */
@Component
public class UTCAndDateInquiryHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        UTCAndDateInquiry data = (UTCAndDateInquiry) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
