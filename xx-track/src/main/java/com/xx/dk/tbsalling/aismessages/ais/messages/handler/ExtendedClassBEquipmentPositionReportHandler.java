package com.xx.dk.tbsalling.aismessages.ais.messages.handler;


import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.ExtendedClassBEquipmentPositionReport;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 19 扩展的B类设备位置报告
 * @author: xx
 * @Date 2023/7/14 17:44
 * @version: 1.0
 */
@Component
@RequiredArgsConstructor
public class ExtendedClassBEquipmentPositionReportHandler  implements AISMessageHandler {
    private final DynamicDataReportHandler dynamicDataReportHandler;
    @Override
    public void handle(AISMessage message) {
        ExtendedClassBEquipmentPositionReport data = (ExtendedClassBEquipmentPositionReport) message;
        
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
        dynamicDataReportHandler.extendedDynamicDataReportHandler(data);
    }
}
