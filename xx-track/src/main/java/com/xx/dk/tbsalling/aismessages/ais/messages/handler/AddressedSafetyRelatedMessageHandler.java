package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.AddressedSafetyRelatedMessage;
import org.springframework.stereotype.Component;

/**
 * @description: 12 已解决的安全相关消息
 * @author: xx
 * @Date 2023/7/14 17:40
 * @version: 1.0
 */
@Component
public class AddressedSafetyRelatedMessageHandler implements AISMessageHandler {

    @Override
    public void handle(AISMessage message) {
        AddressedSafetyRelatedMessage data = (AddressedSafetyRelatedMessage) message;
        //System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
    }
}
