/*
 * AIS常见问题
 * - 一个基于 Java 的库，用于解码来自数字 VHF 无线电业务相关的 AIS 消息
 * 根据 ITU 1371 对海上航行和安全的要求。
 *
 * （C） 版权所有 2011- S-Consult ApS，增值税号。**********，丹麦。
 *
 * 根据 Creative Commons 署名-非商业性使用-相同方式共享 3.0 未本地化版本许可发布。
 * 有关此许可证的详细信息，请参阅附近的 LICENCE-full 文件，请访问 http://creativecommons.org/licenses/by-nc-sa/3.0/
 * 或写信至 Creative Commons， 171 Second Street， Suite 300， San Francisco， California， 94105， USA。
 *
 * 不得用于商业用途！
 * 请联系 <PERSON> <<EMAIL>>以获取此软件的商业许可版本。
 *
 */
package com.xx.dk.tbsalling.aismessages.ais.messages;

import com.xx.dk.tbsalling.aismessages.ais.messages.types.AISMessageType;
import com.xx.dk.tbsalling.aismessages.nmea.messages.NMEAMessage;

@SuppressWarnings("serial")
public class PositionReportClassAAssignedSchedule extends PositionReport {
    public PositionReportClassAAssignedSchedule(NMEAMessage[] nmeaMessages) {
        super(nmeaMessages);
    }

    protected PositionReportClassAAssignedSchedule(NMEAMessage[] nmeaMessages, String bitString) {
        super(nmeaMessages, bitString);
    }

    @Override
    public String toString() {
        return "PositionReportClassAAssignedSchedule{" +
                "messageType=" + getMessageType() +
                "} " + super.toString();
    }

    @Override
    public AISMessageType getMessageType() {
        return AISMessageType.PositionReportClassAAssignedSchedule;
    }
}
