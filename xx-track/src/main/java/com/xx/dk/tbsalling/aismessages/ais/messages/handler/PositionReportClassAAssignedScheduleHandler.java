package com.xx.dk.tbsalling.aismessages.ais.messages.handler;

import com.xx.dk.tbsalling.aismessages.ais.messages.AISMessage;
import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReportClassAAssignedSchedule;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 02 位置报告A类（已分配时间表）
 * @author: xx
 * @Date 2023/7/14 17:31
 * @version: 1.0
 */
@Component
@RequiredArgsConstructor
public class PositionReportClassAAssignedScheduleHandler implements AISMessageHandler {

    private final DynamicDataReportHandler dynamicDataReportHandler;


    @Override
    public void handle(AISMessage message) {
        PositionReportClassAAssignedSchedule data = (PositionReportClassAAssignedSchedule) message;
        ////System.out.println(data.getMessageType().getCode() +"-----" + data.toString());
        dynamicDataReportHandler.positionReportDispose(data);
    }




}
