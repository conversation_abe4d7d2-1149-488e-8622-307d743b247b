package com.xx.util;

import com.xx.dk.tbsalling.aismessages.ais.messages.ExtendedClassBEquipmentPositionReport;
import com.xx.dk.tbsalling.aismessages.ais.messages.ExtendedDynamicDataReport;
import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReport;
import com.xx.dk.tbsalling.aismessages.ais.messages.StandardClassBCSPositionReport;
import com.xx.web.domain.MgTrack;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 17:47
 * @version: 1.0
 */
public class AisToTrackUtil {


    public static void setTrack(PositionReport position, MgTrack track) {
        track.setMmsi(position.getSourceMmsi().getMMSI());
        track.setStatus(position.getNavigationStatus().getCode());
        track.setRot(position.getRateOfTurn().floatValue());
        track.setSog(position.getSpeedOverGround());
        track.setLat(position.getLatitude().doubleValue());
        track.setLon(position.getLongitude().doubleValue());
        track.setCog(position.getCourseOverGround().floatValue());
        track.setHeading(position.getTrueHeading().floatValue());
        track.setTime(position.getMetadata().getReceived().getEpochSecond());
    }

    public static Integer setTrack(ExtendedDynamicDataReport report, MgTrack track) {
        Integer mmsi = 0;
        if (report instanceof StandardClassBCSPositionReport) {
            StandardClassBCSPositionReport data = (StandardClassBCSPositionReport) report;
            mmsi = data.getSourceMmsi().getMMSI();
            track.setTime(data.getMetadata().getReceived().getEpochSecond());
        } else if (report instanceof ExtendedClassBEquipmentPositionReport) {
            ExtendedClassBEquipmentPositionReport data = (ExtendedClassBEquipmentPositionReport) report;
            mmsi = data.getSourceMmsi().getMMSI();
            track.setTime(data.getMetadata().getReceived().getEpochSecond());
        }
        if (mmsi == 0) {
            return mmsi;
        }
        track.setMmsi(mmsi);
        track.setSog(report.getSpeedOverGround());
        track.setLat(report.getLatitude().doubleValue());
        track.setLon(report.getLongitude().doubleValue());
        track.setCog(report.getCourseOverGround().floatValue());
        track.setHeading(report.getTrueHeading().floatValue());
        return mmsi;
    }
}
