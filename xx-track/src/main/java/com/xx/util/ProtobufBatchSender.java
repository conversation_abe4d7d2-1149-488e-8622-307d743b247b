package com.xx.util;

import com.xx.web.proto.ShipOuterClass;
import com.xx.websocket.server.TrackPushServer;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * websocket推送protubuf数据
 */
public class ProtobufBatchSender {

    public static ByteBuffer sendProtobufBatch(Collection<ShipOuterClass.Ship> messages) {
        byte[] batchBytes = getBytes(messages);
        // Send the protobuf batch bytes to the WebSocket client
       return ByteBuffer.wrap(batchBytes);
    }

    private static byte[] getBytes(Collection<ShipOuterClass.Ship> messages) {
        List<byte[]> messageBytesList = new ArrayList<>();
        int totalLength = 0;
        // Serialize each protobuf message and add it to the list
        for (ShipOuterClass.Ship message : messages) {
            byte[] messageBytes = message.toByteArray();
            messageBytesList.add(messageBytes);
            totalLength += messageBytes.length;
        }
        // Combine all the protobuf message bytes into a single byte array
        byte[] batchBytes = new byte[totalLength + 4 * messages.size()];
        int offset = 0;
        for (byte[] messageBytes : messageBytesList) {
            byte[] bytes = int2BytesH(messageBytes.length);
            System.arraycopy(bytes, 0, batchBytes, offset, bytes.length);
            offset += bytes.length;
            System.arraycopy(messageBytes, 0, batchBytes, offset, messageBytes.length);
            offset += messageBytes.length;
        }
        return batchBytes;
    }

    public static void sendProtobufBytesBatch(List<byte[]> messageBytesList) {
        int totalLength = 0;
        // Serialize each protobuf message and add it to the list
        for (byte[] messageBytes : messageBytesList) {
            totalLength += messageBytes.length;
        }
        // Combine all the protobuf message bytes into a single byte array
        byte[] batchBytes = new byte[totalLength + 4 * messageBytesList.size()];
        int offset = 0;
        for (byte[] messageBytes : messageBytesList) {
            byte[] bytes = int2BytesH(messageBytes.length);
            System.arraycopy(bytes, 0, batchBytes, offset, bytes.length);
            offset += bytes.length;
            System.arraycopy(messageBytes, 0, batchBytes, offset, messageBytes.length);
            offset += messageBytes.length;
        }
        // Send the protobuf batch bytes to the WebSocket client
        ByteBuffer buffer = ByteBuffer.wrap(batchBytes);
        TrackPushServer.sendAllMessage(buffer);
    }

    //低位在前
    public static byte[] int2BytesL(int integer)
    {
        byte[] bytes=new byte[4];
        bytes[3]=(byte) (integer>>24);
        bytes[2]=(byte) (integer>>16);
        bytes[1]=(byte) (integer>>8);
        bytes[0]=(byte) integer;
        return bytes;
    }
    //高位在前
    public static byte[] int2BytesH(int integer)
    {
        byte[] bytes=new byte[4];
        bytes[0]=(byte) (integer>>24);
        bytes[1]=(byte) (integer>>16);
        bytes[2]=(byte) (integer>>8);
        bytes[3]=(byte) integer;
        return bytes;
    }

    public static int bytes2Int(byte[] bytes )
    {
        //如果不与0xff进行按位与操作，转换结果将出错
        int int1=bytes[0]&0xff;
        int int2=(bytes[1]&0xff)<<8;
        int int3=(bytes[2]&0xff)<<16;
        int int4=(bytes[3]&0xff)<<24;
        return int1|int2|int3|int4;
    }
    //byte[] 转 int 低字节在前（低字节序）
    public static int toIntL(byte[] b){
        int res = 0;
        for(int i=0;i<b.length;i++){
            res += (b[i] & 0xff) << (i*8);
        }
        return res;
    }
    //byte[] 转 int 高字节在前（高字节序）
    public static int toIntH(byte[] b){
        int res = 0;
        for(int i=0;i<b.length;i++){
            res += (b[i] & 0xff) << ((3-i)*8);
        }
        return res;
    }
}
