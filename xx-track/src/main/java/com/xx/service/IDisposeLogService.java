package com.xx.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xx.domain.dto.DisposeLogQuery;
import com.xx.domain.entity.DisposeLog;

import java.util.List;

/**
* <p>
    * 处置日志 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-07-22
*/
public interface IDisposeLogService extends IService<DisposeLog> {

    /**
    * 处置日志分页列表
    * @param disposeLog 根据需要进行传值
    * @return
    */
    IPage<DisposeLog> page(DisposeLogQuery disposeLog);

    /**
    * 处置日志详情
    * @param id
    * @return
    */
    DisposeLog info(Long id);

    /**
    * 处置日志新增
    * @param disposeLog 根据需要进行传值
    * @return
    */
    boolean add(DisposeLog disposeLog);

    /**
    * 处置日志修改
    * @param disposeLog 根据需要进行传值
    * @return
    */
    boolean modify(DisposeLog disposeLog);

    /**
    * 处置日志删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
