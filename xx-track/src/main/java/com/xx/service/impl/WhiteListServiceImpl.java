package com.xx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.constant.Constants;
import com.xx.mapper.WhiteListMapper;
import com.xx.service.IWhiteListService;
import com.xx.utils.base.MPageUtils;
import com.xx.utils.redis.RedisUtil;
import com.xx.web.domain.entity.WhiteList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 白名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Service
@RequiredArgsConstructor
public class WhiteListServiceImpl extends ServiceImpl<WhiteListMapper, WhiteList> implements IWhiteListService {

    private final WhiteListMapper mapper;
    private final RedisUtil redisUtil;

    /**
     * 白名单分页列表
     *
     * @param whiteList 根据需要进行传值
     * @return
     */
    @Override
    public IPage<WhiteList> page(WhiteList whiteList) {

        QueryWrapper<WhiteList> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // 白名单记录ID
                .eq(whiteList.getId() != null, WhiteList::getId, whiteList.getId())
                // 船名
                .eq(!StringUtils.isEmpty(whiteList.getShipName()), WhiteList::getShipName, whiteList.getShipName())
                // 船舶MMSI号码
                .eq(whiteList.getMmsi() != null, WhiteList::getMmsi, whiteList.getMmsi())
                // 0其他 1 自有船只 2工作船只
                .eq(whiteList.getType() != null, WhiteList::getType, whiteList.getType())
                // 白名单生效时间
                .eq(whiteList.getStartTime() != null, WhiteList::getStartTime, whiteList.getStartTime())
                // 白名单失效时间
                .eq(whiteList.getEndTime() != null, WhiteList::getEndTime, whiteList.getEndTime())
                // 创建时间
                .eq(whiteList.getGmtCreate() != null, WhiteList::getGmtCreate, whiteList.getGmtCreate())
                // 更新时间
                .eq(whiteList.getGmtModified() != null, WhiteList::getGmtModified, whiteList.getGmtModified());

        IPage<WhiteList> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * 白名单详情
     *
     * @param id
     * @return
     */
    @Override
    public WhiteList info(Integer id) {
        return getOne(Wrappers.<WhiteList>lambdaQuery().eq(WhiteList::getMmsi, id));
    }

    /**
     * 白名单新增
     *
     * @param whiteList 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(WhiteList whiteList) {
        WhiteList info = info(whiteList.getMmsi());
        boolean b;
        if (info != null) {
            whiteList.setId(info.getId());
            b = modify(whiteList);
        } else {
            b = save(whiteList);

        }
        cacheWhiteList(b);
        return b;
    }

    /**
     * 白名单修改
     *
     * @param whiteList 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(WhiteList whiteList) {
        boolean b = updateById(whiteList);
        cacheWhiteList(b);
        return b;
    }

    /**
     * 白名单删除(单个条目)
     *
     * @param id
     * @return
     */
    @Override
    public boolean remove(Integer id) {
        boolean b = removeById(id);
        cacheWhiteList(b);
        return b;
    }

    /**
     * 白名单删除(多个条目)
     *
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Integer> ids) {
        boolean b = removeByIds(ids);
        cacheWhiteList(b);
        return b;
    }

    private void cacheWhiteList(boolean b) {
        if (b) {
            redisUtil.del(Constants.WHITE_LIST);
            whiteList(LocalDateTime.now());
        }
    }


    public void whiteList(LocalDateTime now) {
        List<WhiteList> whiteLists = list(Wrappers.<WhiteList>lambdaQuery()
                .ge(WhiteList::getEndTime, now));
        whiteLists.forEach(w ->
                redisUtil.hmSet(Constants.WHITE_LIST, w.getMmsi().toString(), w)
        );
    }
}
