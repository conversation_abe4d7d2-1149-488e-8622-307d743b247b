package com.xx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xx.common.constant.Constants;
import com.xx.mapper.DisposeRuleMapper;
import com.xx.service.IDisposeRuleService;
import com.xx.utils.base.MPageUtils;
import com.xx.utils.redis.RedisUtil;
import com.xx.web.domain.entity.DisposeRule;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 处置规则 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Service
@RequiredArgsConstructor
public class DisposeRuleServiceImpl extends MPJBaseServiceImpl<DisposeRuleMapper, DisposeRule> implements IDisposeRuleService {

    private final DisposeRuleMapper mapper;
    private final RedisUtil redisUtil;

    /**
     * 处置规则分页列表
     *
     * @param disposeRule 根据需要进行传值
     * @return
     */
    @Override
    public IPage<DisposeRule> page(DisposeRule disposeRule) {
        QueryWrapper<DisposeRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // 处置规则ID
                .eq(disposeRule.getId() != null, DisposeRule::getId, disposeRule.getId())
                // 0不预警 1预警
                .eq(disposeRule.getWarning() != null, DisposeRule::getWarning, disposeRule.getWarning())
                // 0VHF不喊话 1喊话
                .eq(disposeRule.getVhf() != null, DisposeRule::getVhf, disposeRule.getVhf())
                // VHF喊话内容
                .eq(!StringUtils.isEmpty(disposeRule.getVhfContent()), DisposeRule::getVhfContent,
                        disposeRule.getVhfContent())
                // 0喇叭不喊话 1喊话
                .eq(disposeRule.getLoudspeaker() != null, DisposeRule::getLoudspeaker, disposeRule.getLoudspeaker())
                // 喇叭喊话内容
                .eq(!StringUtils.isEmpty(disposeRule.getLoudspeakerContent()), DisposeRule::getLoudspeakerContent,
                        disposeRule.getLoudspeakerContent())
                // 0探照灯启动 1不启动
                .eq(disposeRule.getSearchlight() != null, DisposeRule::getSearchlight, disposeRule.getSearchlight())
                // 开始时间
                .eq(disposeRule.getSearchlightStart() != null, DisposeRule::getSearchlightStart,
                        disposeRule.getSearchlightStart())
                // 结束时间
                .eq(disposeRule.getSearchlightEnd() != null, DisposeRule::getSearchlightEnd,
                        disposeRule.getSearchlightEnd())
                // 0ais报文 1不报文
                .eq(disposeRule.getAis() != null, DisposeRule::getAis, disposeRule.getAis())
                // ais报文内容
                .eq(!StringUtils.isEmpty(disposeRule.getAisContent()), DisposeRule::getAisContent,
                        disposeRule.getAisContent())
                // 0不发送短信 1发送短信
                .eq(disposeRule.getSms() != null, DisposeRule::getSms, disposeRule.getSms())
                // 短信模板
                .eq(!StringUtils.isEmpty(disposeRule.getSmsTemplate()), DisposeRule::getSmsTemplate,
                        disposeRule.getSmsTemplate())
                // 0不移交 1移交
                .eq(disposeRule.getHandOver() != null, DisposeRule::getHandOver, disposeRule.getHandOver())
                // 0事件不记录 1记录
                .eq(disposeRule.getEventRecord() != null, DisposeRule::getEventRecord, disposeRule.getEventRecord())
                // 0航迹不记录 1记录
                .eq(disposeRule.getTrackRecord() != null, DisposeRule::getTrackRecord, disposeRule.getTrackRecord())
                // 0视频不记录 1记录
                .eq(disposeRule.getVideoRecord() != null, DisposeRule::getVideoRecord, disposeRule.getVideoRecord())
                // 创建时间
                .eq(disposeRule.getGmtCreate() != null, DisposeRule::getGmtCreate, disposeRule.getGmtCreate())
                // 更新时间
                .eq(disposeRule.getGmtModified() != null, DisposeRule::getGmtModified, disposeRule.getGmtModified())
                .eq(disposeRule.getEnvType() != null, DisposeRule::getEnvType, disposeRule.getEnvType());

        IPage<DisposeRule> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * 处置规则详情
     *
     * @param id
     * @return
     */
    @Override
    public DisposeRule info(Long id) {
        return getById(id);
    }

    /**
     * 处置规则新增
     *
     * @param disposeRule 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(DisposeRule disposeRule) {
        boolean b = save(disposeRule);
        cacheDisposeRule(b);
        return b;
    }

    /**
     * 处置规则修改
     *
     * @param disposeRule 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(DisposeRule disposeRule) {
        boolean b = updateById(disposeRule);
        cacheDisposeRule(b);
        return b;
    }

    /**
     * 处置规则删除(单个条目)
     *
     * @param id
     * @return
     */
    @Override
    public boolean remove(Integer id) {
        boolean b = removeById(id);
        cacheDisposeRule(b);
        return b;
    }

    /**
     * 处置规则删除(多个条目)
     *
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Integer> ids) {
        boolean b = removeByIds(ids);
        cacheDisposeRule(b);
        return b;
    }



    private void cacheDisposeRule(boolean b) {
        if (b) {
            redisUtil.del(Constants.DISPOSE_RULE);
            disposeRules();
        }
    }

    @Override
    public void disposeRules() {
        //List<DisposeRule> disposeRules = list(Wrappers.<DisposeRule>lambdaQuery()
        //        .eq(DisposeRule::getStatus, Boolean.TRUE));
        List<DisposeRule> disposeRules = list();
        Map<String, DisposeRule> disposeRuleMap = disposeRules.stream().collect(Collectors
                .toMap(DisposeRule::getEnvType, Function.identity()));

        Map<String, Object> objDisposeRuleMap = disposeRuleMap.entrySet().stream().collect(
                Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        redisUtil.hPutAll(Constants.DISPOSE_RULE, objDisposeRuleMap);
    }
}
