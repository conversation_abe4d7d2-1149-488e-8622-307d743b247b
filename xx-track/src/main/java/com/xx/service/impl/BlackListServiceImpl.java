package com.xx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.mapper.BlackListMapper;
import com.xx.service.IBlackListService;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.BlackList;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 黑名单 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-24
*/
@Service
@RequiredArgsConstructor
public class BlackListServiceImpl extends ServiceImpl<BlackListMapper, BlackList> implements IBlackListService {

    private final BlackListMapper mapper;

/**
* 黑名单分页列表
* @param blackList 根据需要进行传值
* @return
*/
    @Override
    public IPage<BlackList> page(BlackList blackList) {

        QueryWrapper<BlackList> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 黑名单记录ID
            .eq(blackList.getId() != null, BlackList::getId, blackList.getId())
            // 船名
            .eq(!StringUtils.isEmpty(blackList.getShipName()), BlackList::getShipName, blackList.getShipName())
            // 船舶MMSI号码
            .eq(!StringUtils.isEmpty(blackList.getMmsi()), BlackList::getMmsi, blackList.getMmsi())
            // 类型1有过历史违规行径（历史违规行为，并经过人工确认）2三无船（未开启AIS船舶）3异常行为船4移锚走锚船 5人工设置黑名单船舶（人工设置风险船舶）0其他
            .eq(blackList.getType() != null, BlackList::getType, blackList.getType())
            // 黑名单生效时间
            .eq(blackList.getStartTime() != null, BlackList::getStartTime, blackList.getStartTime())
            // 黑名单失效时间
            .eq(blackList.getEndTime() != null, BlackList::getEndTime, blackList.getEndTime())
            // 创建时间
            .eq(blackList.getGmtCreate() != null, BlackList::getGmtCreate, blackList.getGmtCreate())
            // 更新时间
            .eq(blackList.getGmtModified() != null, BlackList::getGmtModified, blackList.getGmtModified());

        IPage<BlackList> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 黑名单详情
    * @param id
    * @return
    */
    @Override
    public BlackList info(Long id) {
        return getById(id);
    }

    /**
    * 黑名单新增
    * @param blackList 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(BlackList blackList) {
        return save(blackList);
    }

    /**
    * 黑名单修改
    * @param blackList 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(BlackList blackList) {
        return updateById(blackList);
    }

    /**
    * 黑名单删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
    * 黑名单删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

}
