package com.xx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xx.mapper.DeviceMapper;
import com.xx.service.IDeviceService;
import com.xx.utils.base.MPageUtils;
import com.xx.web.component.convert.DeviceConvert;
import com.xx.web.domain.dto.DeviceDTO;
import com.xx.web.domain.entity.Device;
import com.xx.web.domain.query.DeviceQuery;
import com.xx.web.domain.vo.DeviceVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Service
@RequiredArgsConstructor
public class DeviceServiceImpl extends MPJBaseServiceImpl<DeviceMapper, Device> implements IDeviceService {
    private final DeviceConvert INSTANCE = DeviceConvert.INSTANCE;
    private final DeviceMapper mapper;

    /**
     * 分页列表
     *
     * @param device 根据需要进行传值
     * @return
     */
    @Override
    public IPage<DeviceVO> page(DeviceQuery device) {

        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // id
                .eq(device.getId() != null, Device::getId, device.getId())
                // 设备名称
                .eq(StringUtils.hasLength(device.getName()), Device::getName, device.getName())
                // 0未知,1雷达,2广播,3光照,4vhf,5摄像头
                .eq(device.getType() != null, Device::getType, device.getType())
                // IP地址
                .eq(StringUtils.hasLength(device.getIp()), Device::getIp, device.getIp())
                // 端口
                .eq(device.getPort() != null, Device::getPort, device.getPort())
                // 0无需连接 1 netty
                .eq(device.getConnType() != null, Device::getConnType, device.getConnType())
                // 连接类型 1client,2server,0其他
                .eq(device.getContype() != null, Device::getContype, device.getContype())
                // 1正常,0关闭
                .eq(device.getStatus() != null, Device::getStatus, device.getStatus())
                // 1删除,0正常
                .eq(device.getDel() != null, Device::getDel, device.getDel())
                // 经度
                .eq(device.getLon() != null, Device::getLon, device.getLon())
                // 纬度
                .eq(device.getLat() != null, Device::getLat, device.getLat())
                // 设备特有属性，可以使用JSON格式存储。例如，对于摄像头，可以存储广角、清晰度等属性；对于喇叭，可以存储声音等属性；对于探照灯，可以存储照明距离和范围等属性。
                .eq(StringUtils.hasLength(device.getOwnAttr()), Device::getOwnAttr, device.getOwnAttr())
                // 创建时间
                .eq(device.getGmtCreate() != null, Device::getGmtCreate, device.getGmtCreate())
                // 更新时间
                .eq(device.getGmtModified() != null, Device::getGmtModified, device.getGmtModified());

        IPage<Device> page = page(MPageUtils.mpPage(), queryWrapper);
        List<DeviceVO> resultList = INSTANCE.toVOS(page.getRecords());
        return new Page<DeviceVO>().setTotal(page.getTotal()).setRecords(resultList);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public DeviceVO info(Long id) {
        return INSTANCE.toVO(getById(id));
    }

    /**
     * 新增
     *
     * @param deviceDTO 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(DeviceDTO deviceDTO) {
        Device device = INSTANCE.toEntity(deviceDTO);
        return save(device);
    }

    /**
     * 修改
     *
     * @param deviceDTO 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(DeviceDTO deviceDTO) {
        Device device = INSTANCE.toEntity(deviceDTO);
        return updateById(device);
    }

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

}
