package com.xx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xx.mapper.ShipRorMapper;
import com.xx.service.IShipRorService;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.ShipRor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 船舶留档记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-29
*/
@Service
@RequiredArgsConstructor
public class ShipRorServiceImpl extends MPJBaseServiceImpl<ShipRorMapper, ShipRor> implements IShipRorService {

    private final ShipRorMapper mapper;

/**
* 船舶留档记录分页列表
* @param shipRor 根据需要进行传值
* @return
*/
    @Override
    public IPage<ShipRor> page(ShipRor shipRor) {

        QueryWrapper<ShipRor> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 船舶留档记录id
            .eq(shipRor.getId() != null, ShipRor::getId, shipRor.getId())
            // 船舶mmsi
            .eq(shipRor.getMmsi() != null, ShipRor::getMmsi, shipRor.getMmsi())
            // 船舶名称
            .eq(!StringUtils.isEmpty(shipRor.getName()), ShipRor::getName, shipRor.getName())
            // 预警类型
            .eq(!StringUtils.isEmpty(shipRor.getEnvType()), ShipRor::getEnvType, shipRor.getEnvType())
            // 经度
            .eq(shipRor.getLon() != null, ShipRor::getLon, shipRor.getLon())
            // 纬度
            .eq(shipRor.getLat() != null, ShipRor::getLat, shipRor.getLat())
            // 触发预警时的航迹
            .eq(!StringUtils.isEmpty(shipRor.getTrack()), ShipRor::getTrack, shipRor.getTrack())
            // 触发时间
            .eq(shipRor.getTriggerTime() != null, ShipRor::getTriggerTime, shipRor.getTriggerTime())
            // 创建时间
            .eq(shipRor.getGmtCreate() != null, ShipRor::getGmtCreate, shipRor.getGmtCreate());

        IPage<ShipRor> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 船舶留档记录详情
    * @param id
    * @return
    */
    @Override
    public ShipRor info(Long id) {
        return getById(id);
    }

    /**
    * 船舶留档记录新增
    * @param shipRor 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(ShipRor shipRor) {
        return save(shipRor);
    }

    /**
    * 船舶留档记录修改
    * @param shipRor 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(ShipRor shipRor) {
        return updateById(shipRor);
    }

    /**
    * 船舶留档记录删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
    * 船舶留档记录删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

}
