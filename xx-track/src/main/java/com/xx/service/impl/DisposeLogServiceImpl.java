package com.xx.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.domain.dto.DisposeLogQuery;
import com.xx.domain.entity.DisposeLog;
import com.xx.mapper.DisposeLogMapper;
import com.xx.service.IDisposeLogService;
import com.xx.utils.base.MPageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 处置日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-22
*/
@Service
@RequiredArgsConstructor
public class DisposeLogServiceImpl extends ServiceImpl<DisposeLogMapper, DisposeLog> implements IDisposeLogService {

    private final DisposeLogMapper mapper;

/**
* 处置日志分页列表
* @param disposeLog 根据需要进行传值
* @return
*/
    @Override
    public IPage<DisposeLog> page(DisposeLogQuery disposeLog) {

        QueryWrapper<DisposeLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 处置规则ID
            .eq(disposeLog.getId() != null, DisposeLog::getId, disposeLog.getId())
            // 短信模板
            .eq(!StringUtils.isEmpty(disposeLog.getSmsTemplate()), DisposeLog::getSmsTemplate, disposeLog.getSmsTemplate())
            // 创建时间
            .ge(disposeLog.getStartDate() != null, DisposeLog::getGmtCreate, disposeLog.getStartDate())
            .le(disposeLog.getEndDate() != null, DisposeLog::getGmtCreate, disposeLog.getEndDate())
            // 通航环境类型
            .eq(!StringUtils.isEmpty(disposeLog.getEnvType()), DisposeLog::getEnvType, disposeLog.getEnvType())
            // 电子围栏id
            .eq(disposeLog.getEnvId() != null, DisposeLog::getEnvId, disposeLog.getEnvId())
            // mmsi
            .eq(disposeLog.getMmsi() != null, DisposeLog::getMmsi, disposeLog.getMmsi());

        IPage<DisposeLog> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 处置日志详情
    * @param id
    * @return
    */
    @Override
    public DisposeLog info(Long id) {
        return getById(id);
    }

    /**
    * 处置日志新增
    * @param disposeLog 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(DisposeLog disposeLog) {
        return save(disposeLog);
    }

    /**
    * 处置日志修改
    * @param disposeLog 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(DisposeLog disposeLog) {
        return updateById(disposeLog);
    }

    /**
    * 处置日志删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
    * 处置日志删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

}
