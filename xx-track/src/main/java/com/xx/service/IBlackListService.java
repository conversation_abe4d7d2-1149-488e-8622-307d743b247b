package com.xx.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xx.web.domain.entity.BlackList;

import java.util.List;

/**
* <p>
    * 黑名单 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-24
*/
public interface IBlackListService extends IService<BlackList> {

    /**
    * 黑名单分页列表
    * @param blackList 根据需要进行传值
    * @return
    */
    IPage<BlackList> page(BlackList blackList);

    /**
    * 黑名单详情
    * @param id
    * @return
    */
    BlackList info(Long id);

    /**
    * 黑名单新增
    * @param blackList 根据需要进行传值
    * @return
    */
    boolean add(BlackList blackList);

    /**
    * 黑名单修改
    * @param blackList 根据需要进行传值
    * @return
    */
    boolean modify(BlackList blackList);

    /**
    * 黑名单删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
