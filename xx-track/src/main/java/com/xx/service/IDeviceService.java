package com.xx.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseService;
import com.xx.web.domain.dto.DeviceDTO;
import com.xx.web.domain.entity.Device;
import com.xx.web.domain.query.DeviceQuery;
import com.xx.web.domain.vo.DeviceVO;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
public interface IDeviceService extends MPJBaseService<Device> {

    /**
     * 分页列表
     * @param deviceQuery 根据需要进行传值
     * @return
     */
    IPage<DeviceVO> page(DeviceQuery deviceQuery);

    /**
     * 详情
     * @param id
     * @return
     */
    DeviceVO info(Long id);

    /**
     * 新增
     * @param deviceDTO 根据需要进行传值
     * @return
     */
    boolean add(DeviceDTO deviceDTO);

    /**
     * 修改
     * @param deviceDTO 根据需要进行传值
     * @return
     */
    boolean modify(DeviceDTO deviceDTO);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
