package com.xx.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xx.web.domain.entity.WhiteList;

import java.time.LocalDateTime;
import java.util.List;

/**
* <p>
    * 白名单 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-24
*/
public interface IWhiteListService extends IService<WhiteList> {

    /**
    * 白名单分页列表
    * @param whiteList 根据需要进行传值
    * @return
    */
    IPage<WhiteList> page(WhiteList whiteList);

    /**
    * 白名单详情
    * @param id
    * @return
    */
    WhiteList info(Integer id);

    /**
    * 白名单新增
    * @param whiteList 根据需要进行传值
    * @return
    */
    boolean add(WhiteList whiteList);

    /**
    * 白名单修改
    * @param whiteList 根据需要进行传值
    * @return
    */
    boolean modify(WhiteList whiteList);

    /**
    * 白名单删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);


    public void whiteList(LocalDateTime now);
        }
