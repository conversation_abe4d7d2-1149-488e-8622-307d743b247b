package com.xx.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseService;
import com.xx.web.domain.entity.ShipRor;

import java.util.List;

/**
* <p>
    * 船舶留档记录 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-29
*/
public interface IShipRorService extends MPJBaseService<ShipRor> {

    /**
    * 船舶留档记录分页列表
    * @param shipRor 根据需要进行传值
    * @return
    */
    IPage<ShipRor> page(ShipRor shipRor);

    /**
    * 船舶留档记录详情
    * @param id
    * @return
    */
    ShipRor info(Long id);

    /**
    * 船舶留档记录新增
    * @param shipRor 根据需要进行传值
    * @return
    */
    boolean add(ShipRor shipRor);

    /**
    * 船舶留档记录修改
    * @param shipRor 根据需要进行传值
    * @return
    */
    boolean modify(ShipRor shipRor);

    /**
    * 船舶留档记录删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
