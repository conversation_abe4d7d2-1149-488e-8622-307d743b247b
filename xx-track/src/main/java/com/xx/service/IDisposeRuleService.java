package com.xx.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseService;
import com.xx.web.domain.entity.DisposeRule;

import java.util.List;

/**
* <p>
    * 处置规则 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-30
*/
public interface IDisposeRuleService extends MPJBaseService<DisposeRule> {

    /**
    * 处置规则分页列表
    * @param disposeRule 根据需要进行传值
    * @return
    */
    IPage<DisposeRule> page(DisposeRule disposeRule);

    /**
    * 处置规则详情
    * @param id
    * @return
    */
    DisposeRule info(Long id);

    /**
    * 处置规则新增
    * @param disposeRule 根据需要进行传值
    * @return
    */
    boolean add(DisposeRule disposeRule);

    /**
    * 处置规则修改
    * @param disposeRule 根据需要进行传值
    * @return
    */
    boolean modify(DisposeRule disposeRule);

    /**
    * 处置规则删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);

    public void disposeRules();
}
