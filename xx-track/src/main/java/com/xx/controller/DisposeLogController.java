package com.xx.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.domain.dto.DisposeLogQuery;
import com.xx.domain.entity.DisposeLog;
import com.xx.service.IDisposeLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 处置日志 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-22
*/
@Api(tags = "处置日志")
@RequiredArgsConstructor
@RestController
@RequestMapping("/dispose-log")
public class DisposeLogController extends MPBaseController{

    private final IDisposeLogService disposeLogService;

    @ApiOperation(value = "处置日志列表", response = DisposeLog.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @PostMapping(value = "/page")
    public TableDataInfo page(@RequestBody DisposeLogQuery disposeLog) {
        IPage<DisposeLog> data = disposeLogService.page(disposeLog);
        return getDataTable(data);
    }

    @ApiOperation(value = "处置日志详情", response = DisposeLog.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        DisposeLog data = disposeLogService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "处置日志新增")
    @Log(title = "处置日志", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody DisposeLog disposeLog) {
        return R.trBool(disposeLogService.add(disposeLog));
    }

    @ApiOperation(value = "处置日志修改")
    @Log(title = "处置日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated @RequestBody DisposeLog disposeLog) {
        return R.trBool(disposeLogService.modify(disposeLog));
    }

    @ApiOperation(value = "处置日志删除(单个条目)")
    @Log(title = "处置日志", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(disposeLogService.remove(id));
    }

    @ApiOperation(value = "处置日志删除(多个条目)")
    @Log(title = "处置日志", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(disposeLogService.removes(ids));
    }

}

