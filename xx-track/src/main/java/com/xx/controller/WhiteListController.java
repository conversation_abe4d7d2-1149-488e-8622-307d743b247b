package com.xx.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.service.IWhiteListService;
import com.xx.web.domain.entity.WhiteList;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 14:02
 * @version: 1.0
 */
@Api(tags = "白名单")
@RequiredArgsConstructor
@RestController
@RequestMapping("/whiteList")
public class WhiteListController extends MPBaseController {

    private final IWhiteListService whiteListService;

    @ApiOperation(value = "列表", response = WhiteList.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(WhiteList whiteList) {
        IPage<WhiteList> data = whiteListService.page(whiteList);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = WhiteList.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Integer id) {
        WhiteList data = whiteListService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @PostMapping
    public R add(@Validated @RequestBody WhiteList whiteList) {
        return R.trBool(whiteListService.add(whiteList));
    }

    @ApiOperation(value = "修改")
    @PutMapping
    public R modify(@Validated @RequestBody WhiteList whiteList) {
        return R.trBool(whiteListService.modify(whiteList));
    }

    @ApiOperation(value = "删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(whiteListService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(whiteListService.removes(ids));
    }
}
