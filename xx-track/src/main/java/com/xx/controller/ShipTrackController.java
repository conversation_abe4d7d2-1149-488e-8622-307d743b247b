package com.xx.controller;

import com.xx.common.core.domain.R;
import com.xx.web.service.IShipTrackService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: ShipTrackController
 * @Description:
 * @Date: 2023/4/21 17:13
 * @since JDK 1.8
 */

@RequiredArgsConstructor
@RestController
@RequestMapping("/track/ship-track")
public class ShipTrackController {

    private final IShipTrackService shipTrackService;

    /**
     * 历史航迹
     *
     * @return
     */
    @GetMapping(value = "/history")
    public R history(String shipId, Double hours) {
        return R.ok(shipTrackService.history(shipId, hours));
    }


    @PostMapping(value = "/history")
    public R historyStatistics(Integer days) {
        return R.ok(shipTrackService.historyStatistics(days));
    }
}
