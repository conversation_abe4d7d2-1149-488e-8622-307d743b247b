package com.xx.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.service.IDisposeRuleService;
import com.xx.web.domain.entity.DisposeRule;
import com.xx.web.domain.vo.DeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/19 12:37
 * @version: 1.0
 */
@Api(tags = "处置规则")
@RequiredArgsConstructor
@RestController
@RequestMapping("/track/disposeRule")
public class DisposeRuleController extends MPBaseController {

    private final IDisposeRuleService ruleService;

    @ApiOperation(value = "列表", response = DeviceVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(DisposeRule disposeRule) {
        IPage<DisposeRule> data = ruleService.page(disposeRule);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = DisposeRule.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        DisposeRule data = ruleService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @PostMapping
    public R add(@Validated @RequestBody DisposeRule disposeRule) {
        return R.trBool(ruleService.add(disposeRule));
    }

    @ApiOperation(value = "修改")
    @PutMapping
    public R modify(@Validated @RequestBody DisposeRule disposeRule) {
        return R.trBool(ruleService.modify(disposeRule));
    }

    @ApiOperation(value = "删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(ruleService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(ruleService.removes(ids));
    }

}
