package com.xx.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.service.IDeviceService;
import com.xx.web.domain.dto.DeviceDTO;
import com.xx.web.domain.entity.Device;
import com.xx.web.domain.query.DeviceQuery;
import com.xx.web.domain.vo.DeviceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Api(tags = "")
@RequiredArgsConstructor
@RestController
@RequestMapping("/device")
public class DeviceController extends MPBaseController {

    private final IDeviceService deviceService;

    @ApiOperation(value = "列表", response = DeviceVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(DeviceQuery deviceQuery) {
        IPage<DeviceVO> data = deviceService.page(deviceQuery);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = DeviceVO.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        Device data = deviceService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @PostMapping
    public R add(@Validated @RequestBody DeviceDTO deviceDTO) {
        return R.trBool(deviceService.add(deviceDTO));
    }

    @ApiOperation(value = "修改")
    @PutMapping
    public R modify(@Validated @RequestBody DeviceDTO deviceDTO) {
        return R.trBool(deviceService.modify(deviceDTO));
    }

    @ApiOperation(value = "删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(deviceService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(deviceService.removes(ids));
    }

}

