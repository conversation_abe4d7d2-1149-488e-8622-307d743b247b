package com.xx.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
* <p>
* 处置日志
* </p>
* <AUTHOR>
* @since 2023-07-22
*/
@Data
@Accessors(chain = true)
@TableName("dispose_log")
@ApiModel(value = "DisposeLog对象", description = "处置日志")
public class DisposeLog {

    @ApiModelProperty("处置日志ID")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("VHF喊话内容")
    @TableField("`vhf_content`")
    private String vhfContent;
    @ApiModelProperty("喇叭喊话内容")
    @TableField("`loudspeaker_content`")
    private String loudspeakerContent;
    @ApiModelProperty("ais报文内容")
    @TableField("`ais_content`")
    private String aisContent;
    @ApiModelProperty("短信模板")
    @TableField("`sms_template`")
    private String smsTemplate;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("通航环境类型")
    @TableField("`env_type`")
    private String envType;
    @ApiModelProperty("电子围栏id")
    @TableField("`env_id`")
    private Integer envId;
    @ApiModelProperty("mmsi")
    @TableField("`mmsi`")
    private Integer mmsi;
    @ApiModelProperty("预警记录id")
    @TableField("`warning_id`")
    private Integer warningId;
    @TableField(exist = false)
    private boolean save = false;


}
