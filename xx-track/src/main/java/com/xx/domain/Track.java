package com.xx.domain;

import com.xx.dk.tbsalling.aismessages.ais.messages.ExtendedDynamicDataReport;
import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReport;
import com.xx.web.domain.MgTrack;
import lombok.Data;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/15 19:12
 * @version: 1.0
 */
@Data
public class Track extends ShipBaseInfo {
    /**
     * 航行状态
     */
    //private String navigationStatus;
    private String id;

    private Integer status;

    /**
     * 转向率
     */
    private Float rot;
    /**
     * 对地航速
     */
    private Float sog;
    /**
     * 位置精准
     */
    //private Boolean positionAccuracy;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 精度
     */
    private Double lon;
    /**
     * 对地航向
     */
    private Float cog;
    /**
     * 艏向
     */
    private Float heading;
    /**
     * 秒数
     */
    //private Integer second;
    /**
     * 专用机动指示器
     */
    //private String specialManeuverIndicator;
    /**
     * 旗帜
     */
    //private Boolean raimFlag;
    /**
     * 通信状态
     */
    //private String communicationState;

    private Long time;

    public Track() {
    }

    public Track(ShipBaseInfo info) {
        super(info);
    }

    public Track(PositionReport report) {
        super(report);
    }

    public void setTrack(PositionReport position) {
        this.id = position.getSourceMmsi().getMMSI().toString();
        this.status = position.getNavigationStatus().getCode();
        this.rot = position.getRateOfTurn().floatValue();
        this.sog = position.getSpeedOverGround();
        //this.positionAccuracy = position.getPositionAccuracy();
        this.lat = position.getLatitude().doubleValue();
        this.lon = position.getLongitude().doubleValue();
        this.cog = position.getCourseOverGround().floatValue();
        this.heading = position.getTrueHeading().floatValue();
        //this.second = position.getSecond();
        //this.specialManeuverIndicator = position.getSpecialManeuverIndicator().getValue();
        //this.raimFlag = position.getRaimFlag();
        //this.communicationState = position.getCommunicationState().getSyncState().getValue();
        this.time = position.getMetadata().getReceived().getEpochSecond();
    }

    public void setTrack(MgTrack position) {
        this.id = position.getMmsi().toString();
        this.status = position.getStatus();
        this.rot = position.getRot();
        this.sog = position.getSog();
        this.lat = position.getLat();
        this.lon = position.getLon();
        this.cog = position.getCog();
        this.heading = position.getHeading();
        this.time = position.getTime();
    }

    public void setTrack(ExtendedDynamicDataReport position,Integer mmsi) {
        this.id = mmsi.toString();
        this.sog = position.getSpeedOverGround();
        this.lat = position.getLatitude().doubleValue();
        this.lon = position.getLongitude().doubleValue();
        this.cog = position.getCourseOverGround().floatValue();
        this.heading = position.getTrueHeading().floatValue();
    }
}
