package com.xx.domain;

import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReport;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/15 19:12
 * @version: 1.0
 */
@Data
public class Tracks implements Serializable {

    /**
     * 航行状态
     */
    private Integer status;
    /**
     * 转向率
     */
    private Float rot;
    /**
     * 对地航速
     */
    private Float sog;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 精度
     */
    private Double lon;
    /**
     * 对地航向
     */
    private Float cog;
    /**
     * 艏向
     */
    private Float heading;

    private Long time;
    public Tracks() {
    }


    public void setTrack(PositionReport position) {
        this.status = position.getNavigationStatus().getCode();
        this.rot = position.getRateOfTurn().floatValue();
        this.sog = position.getSpeedOverGround();
        this.lat = position.getLatitude().doubleValue();
        this.lon = position.getLongitude().doubleValue();
        this.cog = position.getCourseOverGround().floatValue();
        this.heading = position.getTrueHeading().floatValue();
        this.time = position.getMetadata().getReceived().getEpochSecond();
    }
}
