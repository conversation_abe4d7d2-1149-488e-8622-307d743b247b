package com.xx.domain;

import com.xx.dk.tbsalling.aismessages.ais.messages.ClassBCSStaticDataReport;
import com.xx.dk.tbsalling.aismessages.ais.messages.PositionReport;
import com.xx.dk.tbsalling.aismessages.ais.messages.ShipAndVoyageData;
import com.xx.web.domain.entity.ZlShip;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/15 19:13
 * @version: 1.0
 */
@Data
public class ShipBaseInfo implements Serializable {

    /**imo*/
    private Integer imo;
    /**mmsi*/
    private Integer mmsi;
    /**国旗*/
    private String region;
    /**呼号*/
    private String callsign;
    /**船名*/
    private String name;
    /**中文名*/
    private String chineseName;
    /**船类型*/
    private Integer shipType;
    /**船台到船艏距离，单位：米*/
    private Float toBow;
    /**船台到船尾距离*/
    private Float toStern;
    /**船台到右舷距离*/
    private Float toStarboard;
    /**船台到左舷距离*/
    private Float toPort;
/*    *//**船宽*//*
    private Float width;
    *//**船长*//*
    private Float length;*/
    /**吃水*/
    private Float draught;
    /**目的地*/
    private String destination;
    /**预计抵达时间*/
    private Long eta;
    /**定位设备*/
    //private String positionFixingDevice;

    public ShipBaseInfo() {
    }

    public ShipBaseInfo(ShipBaseInfo info) {
        if (info != null) {
            this.imo = info.imo;
            this.region = info.region;
            this.mmsi = info.mmsi;
            this.callsign = info.callsign;
            this.name = info.name;
            this.chineseName = info.chineseName;
            this.shipType = info.shipType;
            this.toBow = info.toBow;
            this.toStern = info.toStern;
            this.toStarboard = info.toStarboard;
            this.toPort = info.toPort;
            this.destination = info.destination;
            this.draught = info.draught;
            this.eta = info.eta;
            //this.positionFixingDevice = info.positionFixingDevice;
        }
    }


    public ShipBaseInfo(ShipAndVoyageData data) {
        this.mmsi = data.getSourceMmsi().getMMSI();
        this.name = data.getShipName();
        this.imo = data.getImo().getIMO();
        this.callsign = data.getCallsign();
        if (shipType != null) {
            this.shipType = data.getShipType().getCode();
        }

        if(data.getToBow() != null)
            this.toBow = data.getToBow().floatValue();
        if(data.getToPort() != null)
            this.toPort = data.getToPort().floatValue();
        if(data.getToStarboard() != null)
            this.toStarboard = data.getToStarboard().floatValue();
        if(data.getToStern() != null)
            this.toStern = data.getToStern().floatValue();
        this.draught = data.getDraught();
        this.destination = data.getDestination();
        if (data.getEtaMonth() != 0 && data.getEtaDay() != 0 && data.getEtaHour() != 24) {
            this.eta = LocalDateTime.now()
                    .withMonth(data.getEtaMonth())
                    .withDayOfMonth(data.getEtaDay())
                    .withHour(data.getEtaHour())
                    .withMinute(data.getEtaMinute()).toEpochSecond(ZoneOffset.of("+8"));
        }

        //this.positionFixingDevice = data.getPositionFixingDevice().getValue();
    }
    public ShipBaseInfo(PositionReport data) {
        this.mmsi = data.getSourceMmsi().getMMSI();
    }
    public ShipBaseInfo(ClassBCSStaticDataReport data) {
        this.mmsi = data.getSourceMmsi().getMMSI();
        this.name = data.getShipName();
        this.callsign = data.getCallsign();
        if (shipType != null) {
            this.shipType = data.getShipType().getCode();
        }
        if(data.getToBow() != null)
        this.toBow = data.getToBow().floatValue();
        if(data.getToPort() != null)
        this.toPort = data.getToPort().floatValue();
        if(data.getToStarboard() != null)
        this.toStarboard = data.getToStarboard().floatValue();
        if(data.getToStern() != null)
        this.toStern = data.getToStern().floatValue();
    }

    public void setZlShip(ZlShip zlShip) {
        if (zlShip != null) {
            this.setChineseName(zlShip != null ? zlShip.getLocalName() : "");
            this.setRegion(zlShip.getFlagCode());
/*            this.setWidth(zlShip.getBm().floatValue());
            this.setLength(zlShip.getLoa().floatValue());*/
        }
    }
}
