package com.xx.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xx.domain.entity.DisposeLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: DisposeLogQuery
 * @Description:
 * @Date: 2023/8/1
 * @since JDK 1.8
 */
@Data
public class DisposeLogQuery extends DisposeLog {

    @ApiModelProperty("搜索开始时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime startDate;
    @ApiModelProperty("搜索结束时间")
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime endDate;
}
