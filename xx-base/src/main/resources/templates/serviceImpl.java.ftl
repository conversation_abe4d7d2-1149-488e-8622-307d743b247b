package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
import com.xx.utils.base.MPageUtils;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * ${table.comment!} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
*/
@Service
@RequiredArgsConstructor
<#if kotlin>
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
<#else>
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

<#--    @Autowired-->
    private final ${table.mapperName} mapper;

/**
* ${table.comment!}分页列表
* @param ${entity?uncap_first} 根据需要进行传值
* @return
*/
    @Override
    public IPage<${entity}> page(${entity} ${entity?uncap_first}) {

        QueryWrapper<${entity}> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
<#list table.fields as field>
            // ${field.comment}
    <#if !entityLombokModel>
        <#if field.propertyType == "Boolean">
            <#assign getprefix="is"/>
        <#else>
            <#assign getprefix="get"/>
        </#if>
        <#if field.propertyType == "String">
            .eq(!StringUtils.isEmpty(${entity?uncap_first}.${getprefix}${field.capitalName}()), ${entity}::${getprefix}${field.capitalName}, ${entity?uncap_first}.${getprefix}${field.capitalName}())<#if table.fields?size == field_index + 1>;</#if>
        <#else>
            .eq(${entity?uncap_first}.${getprefix}${field.capitalName}() != null, ${entity}::${getprefix}${field.capitalName}, ${entity?uncap_first}.${getprefix}${field.capitalName}())<#if (table.fields?size == field_index + 1) >;</#if>
        </#if>
    <#else>
        <#if field.propertyType == "String">
            .eq(!StringUtils.isEmpty(${entity?uncap_first}.get${field.capitalName}()), ${entity}::get${field.capitalName}, ${entity?uncap_first}.get${field.capitalName}())<#if (table.fields?size == field_index + 1) >;</#if>
        <#else>
            .eq(${entity?uncap_first}.get${field.capitalName}() != null, ${entity}::get${field.capitalName}, ${entity?uncap_first}.get${field.capitalName}())<#if (table.fields?size == field_index + 1) >;</#if>
        </#if>
    </#if>
</#list>

        IPage<${entity}> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * ${table.comment!}详情
    * @param id
    * @return
    */
    @Override
    public ${entity} info(Long id) {
        return getById(id);
    }

    /**
    * ${table.comment!}新增
    * @param ${entity?uncap_first} 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(${entity} ${entity?uncap_first}) {
        return save(${entity?uncap_first});
    }

    /**
    * ${table.comment!}修改
    * @param ${entity?uncap_first} 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(${entity} ${entity?uncap_first}) {
        return updateById(${entity?uncap_first});
    }

    /**
    * ${table.comment!}删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(<#list table.fields as field><#if field.keyFlag>${field.propertyType}<#break/></#if></#list> id) {
        return removeById(id);
    }

    /**
    * ${table.comment!}删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<<#list table.fields as field><#if field.keyFlag>${field.propertyType}<#break/></#if></#list>> ids) {
        return removeByIds(ids);
    }

}
    </#if>
