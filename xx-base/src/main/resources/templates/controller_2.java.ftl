package ${package.Controller};

import com.baomidou.mybatisplus.core.metadata.IPage;
import ${package.Entity}.${entity};
import ${package.Service}.${table.serviceName};
import com.xx.core.controller.MPBaseController;
import com.xx.common.core.domain.R;
import com.xx.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

<#if restControllerStyle>
<#else>
    import org.springframework.stereotype.Controller;
</#if>
<#if superControllerClassPackage??>
    import ${superControllerClassPackage};
</#if>


import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * ${table.comment} 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
*/
@Api(tags = "${table.comment}")
@RequiredArgsConstructor
<#if restControllerStyle>
@RestController
<#else>
    @Controller
</#if>
@RequestMapping("<#if package.ModuleName?? && package.ModuleName != "">/${package.ModuleName}</#if>/<#if controllerMappingHyphenStyle??>${controllerMappingHyphen}<#else>${table.entityPath}</#if>")
<#if kotlin>
class ${table.controllerName}<#if superControllerClass??> : ${superControllerClass}()</#if>
<#else>
<#if superControllerClass??>
public class ${table.controllerName} extends ${superControllerClass} {
<#else>
public class ${table.controllerName} extends MPBaseController{
</#if>

<#--    @Autowired-->
    private final ${table.serviceName} ${entity?uncap_first}Service;

    @ApiOperation(value = "${table.comment}列表", response = ${entity}.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(${entity} ${entity?uncap_first}) {
        IPage<${entity}> data = ${entity?uncap_first}Service.page(${entity?uncap_first});
        return getDataTable(data);
    }

    @ApiOperation(value = "${table.comment}详情", response = ${entity}.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        ${entity} data = ${entity?uncap_first}Service.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "${table.comment}新增")
    @PostMapping
    public R add(@Validated @RequestBody ${entity} ${entity?uncap_first}) {
        return R.trBool(${entity?uncap_first}Service.add(${entity?uncap_first}));
    }

    @ApiOperation(value = "${table.comment}修改")
    @PutMapping
    public R modify(@Validated @RequestBody ${entity} ${entity?uncap_first}) {
        return R.trBool(${entity?uncap_first}Service.modify(${entity?uncap_first}));
    }

    @ApiOperation(value = "${table.comment}删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(${entity?uncap_first}Service.remove(id));
    }

    @ApiOperation(value = "${table.comment}删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(${entity?uncap_first}Service.removes(ids));
    }

}
    </#if>

