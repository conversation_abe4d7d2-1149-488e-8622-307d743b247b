package ${package.Service};

import ${package.Entity}.${entity};
import ${superServiceClassPackage};
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    * ${table.comment!} 服务类
    * </p>
*
* <AUTHOR>
* @since ${date}
*/
<#if kotlin>
interface ${table.serviceName} : ${superServiceClass}<${entity}>
<#else>
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {

    /**
    * ${table.comment!}分页列表
    * @param ${entity?uncap_first} 根据需要进行传值
    * @return
    */
    IPage<${entity}> page(${entity} ${entity?uncap_first});

    /**
    * ${table.comment!}详情
    * @param id
    * @return
    */
    ${entity} info(Long id);

    /**
    * ${table.comment!}新增
    * @param ${entity?uncap_first} 根据需要进行传值
    * @return
    */
    boolean add(${entity} ${entity?uncap_first});

    /**
    * ${table.comment!}修改
    * @param ${entity?uncap_first} 根据需要进行传值
    * @return
    */
    boolean modify(${entity} ${entity?uncap_first});

    /**
    * ${table.comment!}删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(<#list table.fields as field><#if field.keyFlag>${field.propertyType}<#break/></#if></#list> id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<<#list table.fields as field><#if field.keyFlag>${field.propertyType}<#break/></#if></#list>> ids);
        }
    </#if>
