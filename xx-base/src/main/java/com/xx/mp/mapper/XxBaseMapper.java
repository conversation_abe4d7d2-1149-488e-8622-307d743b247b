package com.xx.mp.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface XxBaseMapper<T> extends MPJBaseMapper<T> {

    /**
     * 根据map条件查询数据，并忽略逻辑删除
     *
     * @param columnMap 查询条件
     * @return 结果信息
     */
    List<T> selectIgnoreLogicDeleteByMap(@Param("cm") Map<String, Object> columnMap);

    /**
     * 根据条件查询数据，并忽略逻辑删除
     *
     * @param queryWrapper 查询条件
     * @return 结果信息
     */
    List<T> selectIgnoreLogicDelete(@Param("ew") Wrapper<T> queryWrapper);
}
