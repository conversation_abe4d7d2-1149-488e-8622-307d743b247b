package com.xx.exception;

import java.util.LinkedHashMap;

public class ApiErrorResult extends LinkedHashMap<String,Object> {
    private static final String DATA = "data";
    private static final String CODE_KEY = "code";
    private static final String MESSAGE_KEY =  "msg";

    public ApiErrorResult(Object data, String code, String message) {
        this.put(DATA,data);
        this.put(CODE_KEY,code);
        this.put(MESSAGE_KEY,message);
    }
}
