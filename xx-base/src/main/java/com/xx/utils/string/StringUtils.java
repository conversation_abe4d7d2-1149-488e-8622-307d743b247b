package com.xx.utils.string;

import com.xx.common.exception.ServiceException;
import com.xx.utils.collections.CollectionUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: StringUtils
 * @Description:
 * @Date: 2023/6/13
 * @since JDK 1.8
 */
public class StringUtils {


    /**
     * @Description: 版本号格式化
     * @author: xx
     * @Date: 2023/6/13
     * @param0: version 需要格式化的版本号
     * @param1: split 版本号分隔符
     * @param2: join 格式化后拼接的分隔符
     * @param3: args 每段版本号的长度
     * @paramTypes: [java.lang.String, java.lang.String, java.lang.Integer...]
     * @return: java.lang.String
     */
    public static String versionFormat(String version, String split,String join, Integer... args) {
        String[] strs = version.split(split);
        if (CollectionUtils.isEmpty(args)) {
            args = new Integer[]{3, 3, 3};
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < strs.length; i++) {
            if (strs[i].length() > args[i]) {
                throw new ServiceException("版本号格式错误!");
            }
            sb.append(String.format("%1$" + args[i] + "s", strs[i]));
            if (i < strs.length - 1) {
                sb.append(join);
            }
        }
        return sb.toString().replaceAll(" ","0");
    }

    public static int compare(String str1, String str2) {
        // 计算两个字符串的长度
        int len1 = str1.length();
        int len2 = str2.length();

        // 将两个字符串转换为相同的长度，不足的部分在前面补 '0'
        if (len1 < len2) {
            str1 = String.format("%0" + len2 + "d", Integer.parseInt(str1));
        } else if (len1 > len2) {
            str2 = String.format("%0" + len1 + "d", Integer.parseInt(str2));
        }

        // 使用 compareTo() 函数比较字符串的大小
        return str1.compareTo(str2);
    }
}
