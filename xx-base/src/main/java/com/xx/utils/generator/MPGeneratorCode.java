package com.xx.utils.generator;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: MPGeneratorCode
 * @Description:
 * @Date: 2022/12/7 13:56
 * @since JDK 1.8
 */
public class MPGeneratorCode {

    public static void main(String[] args) {
        //根据表名生成代码
//        gen("anchor_shop",true);
        gen("dispose_log", true, "com.xx", "xx-track", true);

    }

    /**
     * @Description:
     * @author: xx
     * @Date: 2023/2/4 9:58
     * @param0: tableName 根据表名生成代码
     * @param1: createController 是否生成controller
     * @paramTypes: [java.lang.String, boolean]
     * @return: void
     */
    public static void gen(String tableName, boolean createController, String parentUrl, String module, boolean fileOverride) {
        if (StringUtils.isBlank(tableName)) {
            System.out.println("请输入表名.....");
            return;
        }
        Map<String, String> map = CreateTableUtils.map;

        String url = map.get("url");
        String userName = map.get("userName");
        String password = map.get("password");
        //获取当前模块所在的路径
        String propath = System.getProperty("user.dir");
        System.out.println(propath);
        String src = "/" + module + "/src/main";
        FastAutoGenerator.create(url
                        , userName, password)
                .globalConfig(builder -> {
                    builder.author("xx") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
//                            .fileOverride() // 覆盖已生成文件 已过时 移到策略配置
                            //.outputDir(propath+"/mybatis-plus-generator/src/main/java"); // 指定输出目录
                            .outputDir(propath + src + "/java") // 指定输出目录
                            .commentDate("yyyy-MM-dd")   //注释日期
                            .disableOpenDir();   //禁止打开输出目录，默认:true
                })

                .packageConfig(builder -> {
                    builder.parent(parentUrl) // 设置父包名
                            //.moduleName("live") // 设置父包模块名
                            .entity("domain.entity")
                            //指定xml生成的路径
                            .pathInfo(Collections.singletonMap(OutputFile.xml, propath + src + "\\resources\\mapper\\"));
                })
                .strategyConfig(builder -> {
                    if (fileOverride) {
                        builder.entityBuilder().enableFileOverride()
                                .mapperBuilder().enableFileOverride()
                                .serviceBuilder().enableFileOverride()
                                .controllerBuilder().enableFileOverride();
                    }
                    builder.addInclude(tableName) // 设置需要生成的表名
                            .addTablePrefix("t_", "c_")
                            //自定义配置继承父包 HmBaseMapper  此类为我们公司扩展类
                            .mapperBuilder()
                            .build()
                            //实体类配置
                            .entityBuilder()
                            .enableLombok()
                            .enableChainModel()
                            .enableTableFieldAnnotation()
                            // 逻辑删除字段名(数据库)
                            .logicDeleteColumnName("del")
                            // 逻辑删除属性名(实体)
                            .logicDeletePropertyName("del")
                            .disableSerialVersionUID()
                            // 乐观锁字段名(数据库)
                            .versionColumnName("version")
                            // 乐观锁属性名(实体)
                            .versionPropertyName("version")
                            // 	数据库表映射到实体的命名策略 -- 下划线转驼峰命名
                            .naming(NamingStrategy.underline_to_camel)
                            // 数据库表字段映射到实体的命名策略 -- 下划线转驼峰命名
                            .columnNaming(NamingStrategy.underline_to_camel)
                            // 阿里巴巴开发规范之创建时间、更新时间 交由mybatis-plus处理，如若交给数据库处理，则取消此设置
                            .addTableFills(new Column("gmt_create", FieldFill.INSERT), new Column("gmt_modified", FieldFill.INSERT_UPDATE),
                                    new Column("create_by", FieldFill.INSERT), new Column("update_by", FieldFill.INSERT_UPDATE))
                            .build()
                            //service配置
                            .serviceBuilder()
                            .controllerBuilder()
//                            .enableHyphenStyle()  // 开启驼峰转连字符
                            .enableRestStyle() // 开启生成@RestController 控制器

                            .build();
                })
                .templateConfig(builder -> {
                    // 实体类使用我们自定义模板 -- 模板位置
                    builder.entity("templates/myEntity.java");
                })
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                //设置自定义模板路径
                .templateConfig(builder -> {
                    //是否创建controller
                    if (createController) {
                        builder.controller("/templates/controller.java");
                    } else {
                        builder.controller("");
                    }
                    builder.service("/templates/service.java");
                    builder.serviceImpl("/templates/serviceImpl.java");
                    builder.mapper("/templates/mapper.java");
                    builder.xml("/templates/mapper.xml");
                })
                .execute();

    }
}
