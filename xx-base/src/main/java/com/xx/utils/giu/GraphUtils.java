package com.xx.utils.giu;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;


/**
 * 用于点与多边形位置关系的判断
 */
public class GraphUtils {

    /**
     * 判断点是否在多边形内(基本思路是用交点法)
     *
     * @param point
     * @param boundaryPoints
     * @return
     */
    public static boolean isPointInPolygon(BmapPoint point, BmapPoint[] boundaryPoints) {
        // 防止第一个点与最后一个点相同
        if (boundaryPoints != null && boundaryPoints.length > 0
                && boundaryPoints[boundaryPoints.length - 1].equals(boundaryPoints[0])) {
            boundaryPoints = Arrays.copyOf(boundaryPoints, boundaryPoints.length - 1);
        }
        int pointCount = boundaryPoints.length;

        // 首先判断点是否在多边形的外包矩形内，若是在，则进一步判断，不然返回false
        if (!isPointInRectangle(point, boundaryPoints)) {
            return false;
        }

        // 若是点与多边形的其中一个顶点重合，那么直接返回true
        for (int i = 0; i < pointCount; i++) {
            if (point.equals(boundaryPoints[i])) {
                return true;
            }
        }

        /**
         * 基本思想是利用X轴射线法，计算射线与多边形各边的交点，若是是偶数，则点在多边形外，不然在多边形内。还会考虑一些特殊状况，如点在多边形顶点上，
         *  点在多边形边上等特殊状况。
         */
        // X轴射线与多边形的交点数
        int intersectPointCount = 0;
        // X轴射线与多边形的交点权值
        float intersectPointWeights = 0;
        // 浮点类型计算时候与0比较时候的容差
        double precision = 2e-10;
        // 边P1P2的两个端点
        BmapPoint point1 = boundaryPoints[0], point2;
        // 循环判断全部的边
        for (int i = 1; i <= pointCount; i++) {
            point2 = boundaryPoints[i % pointCount];

            /**
             * 若是点的y坐标在边P1P2的y坐标开区间范围以外，那么不相交。
             */
            if (point.getLat() < Math.min(point1.getLat(), point2.getLat())
                    || point.getLat() > Math.max(point1.getLat(), point2.getLat())) {
                point1 = point2;
                continue;
            }

            /**
             * 此处判断射线与边相交
             */
            if (point.getLat() > Math.min(point1.getLat(), point2.getLat())
                    && point.getLat() < Math.max(point1.getLat(), point2.getLat())) {// 若是点的y坐标在边P1P2的y坐标开区间内
                if (point1.getLng() == point2.getLng()) {// 若边P1P2是垂直的
                    if (point.getLng() == point1.getLng()) {
                        // 若点在垂直的边P1P2上，则点在多边形内
                        return true;
                    } else if (point.getLng() < point1.getLng()) {
                        // 若点在在垂直的边P1P2左边，则点与该边必然有交点
                        ++intersectPointCount;
                    }
                } else {// 若边P1P2是斜线
                    if (point.getLng() <= Math.min(point1.getLng(), point2.getLng())) {// 点point的x坐标在点P1和P2的左侧
                        ++intersectPointCount;
                    } else if (point.getLng() > Math.min(point1.getLng(), point2.getLng())
                            && point.getLng() < Math.max(point1.getLng(), point2.getLng())) {// 点point的x坐标在点P1和P2的x坐标中间
                        double slopeDiff = 0.0d;
                        if (point1.getLat() > point2.getLat()) {
                            slopeDiff = (point.getLat() - point2.getLat()) / (point.getLng() - point2.getLng())
                                    - (point1.getLat() - point2.getLat()) / (point1.getLng() - point2.getLng());
                        } else {
                            slopeDiff = (point.getLat() - point1.getLat()) / (point.getLng() - point1.getLng())
                                    - (point2.getLat() - point1.getLat()) / (point2.getLng() - point1.getLng());
                        }
                        if (slopeDiff > 0) {
                            if (slopeDiff < precision) {// 因为double精度在计算时会有损失，故匹配必定的容差。经试验，坐标经度能够达到0.0001
                                // 点在斜线P1P2上
                                return true;
                            } else {
                                // 点与斜线P1P2有交点
                                intersectPointCount++;
                            }
                        }
                    }
                }
            } else {
                // 边P1P2水平
                if (point1.getLat() == point2.getLat()) {
                    if (point.getLng() <= Math.max(point1.getLng(), point2.getLng())
                            && point.getLng() >= Math.min(point1.getLng(), point2.getLng())) {
                        // 若点在水平的边P1P2上，则点在多边形内
                        return true;
                    }
                }
                /**
                 * 判断点经过多边形顶点
                 */
                if (((point.getLat() == point1.getLat() && point.getLng() < point1.getLng()))
                        || (point.getLat() == point2.getLat() && point.getLng() < point2.getLng())) {
                    if (point2.getLat() < point1.getLat()) {
                        intersectPointWeights += -0.5;
                    } else if (point2.getLat() > point1.getLat()) {
                        intersectPointWeights += 0.5;
                    }
                }
            }
            point1 = point2;
        }

        if ((intersectPointCount + Math.abs(intersectPointWeights)) % 2 == 0) {// 偶数在多边形外
            return false;
        } else { // 奇数在多边形内
            return true;
        }
    }

    public static boolean isPointInPolygons(BmapPoint point, List<List<BmapPoint>> boundaryPoints) {
        for (List<BmapPoint> boundaryPoint : boundaryPoints) {
            if (isPointInPolygon(point, boundaryPoint)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isPointInPolygon(BmapPoint point, List<BmapPoint> boundaryPoints) {
        // 防止第一个点与最后一个点相同
        if (boundaryPoints != null && boundaryPoints.size() > 0 && boundaryPoints.get(boundaryPoints.size() - 1).equals(boundaryPoints.get(0))) {
            boundaryPoints = boundaryPoints.subList(0, boundaryPoints.size() - 1);
        }
        int pointCount = boundaryPoints.size();

        // 首先判断点是否在多边形的外包矩形内，若是在，则进一步判断，不然返回false
        if (!isPointInRectangle(point, boundaryPoints)) {
            return false;
        }

        // 若是点与多边形的其中一个顶点重合，那么直接返回true
        for (int i = 0; i < pointCount; i++) {
            if (point.equals(boundaryPoints.get(i))) {
                return true;
            }
        }

        /**
         * 基本思想是利用X轴射线法，计算射线与多边形各边的交点，若是是偶数，则点在多边形外，不然在多边形内。还会考虑一些特殊状况，如点在多边形顶点上，点在多边形边上等特殊状况。
         */
        // X轴射线与多边形的交点数
        int intersectPointCount = 0;
        // X轴射线与多边形的交点权值
        float intersectPointWeights = 0;
        // 浮点类型计算时候与0比较时候的容差
        double precision = 2e-10;
        // 边P1P2的两个端点
        BmapPoint point1 = boundaryPoints.get(0), point2;
        // 循环判断全部的边
        for (int i = 1; i <= pointCount; i++) {
            point2 = boundaryPoints.get(i % pointCount);

            /**
             * 若是点的y坐标在边P1P2的y坐标开区间范围以外，那么不相交。
             */
            if (point.getLat() < Math.min(point1.getLat(), point2.getLat())
                    || point.getLat() > Math.max(point1.getLat(), point2.getLat())) {
                point1 = point2;
                continue;
            }

            /**
             * 此处判断射线与边相交
             */
            if (point.getLat() > Math.min(point1.getLat(), point2.getLat())
                    && point.getLat() < Math.max(point1.getLat(), point2.getLat())) {// 若是点的y坐标在边P1P2的y坐标开区间内
                if (point1.getLng() == point2.getLng()) {// 若边P1P2是垂直的
                    if (point.getLng() == point1.getLng()) {
                        // 若点在垂直的边P1P2上，则点在多边形内
                        return true;
                    } else if (point.getLng() < point1.getLng()) {
                        // 若点在在垂直的边P1P2左边，则点与该边必然有交点
                        ++intersectPointCount;
                    }
                } else {// 若边P1P2是斜线
                    if (point.getLng() <= Math.min(point1.getLng(), point2.getLng())) {// 点point的x坐标在点P1和P2的左侧
                        ++intersectPointCount;
                    } else if (point.getLng() > Math.min(point1.getLng(), point2.getLng())
                            && point.getLng() < Math.max(point1.getLng(), point2.getLng())) {// 点point的x坐标在点P1和P2的x坐标中间
                        double slopeDiff = 0.0d;
                        if (point1.getLat() > point2.getLat()) {
                            slopeDiff = (point.getLat() - point2.getLat()) / (point.getLng() - point2.getLng())
                                    - (point1.getLat() - point2.getLat()) / (point1.getLng() - point2.getLng());
                        } else {
                            slopeDiff = (point.getLat() - point1.getLat()) / (point.getLng() - point1.getLng())
                                    - (point2.getLat() - point1.getLat()) / (point2.getLng() - point1.getLng());
                        }
                        if (slopeDiff > 0) {
                            if (slopeDiff < precision) {// 因为double精度在计算时会有损失，故匹配必定的容差。经试验，坐标经度能够达到0.0001
                                // 点在斜线P1P2上
                                return true;
                            } else {
                                // 点与斜线P1P2有交点
                                intersectPointCount++;
                            }
                        }
                    }
                }
            } else {
                // 边P1P2水平
                if (point1.getLat() == point2.getLat()) {
                    if (point.getLng() <= Math.max(point1.getLng(), point2.getLng())
                            && point.getLng() >= Math.min(point1.getLng(), point2.getLng())) {
                        // 若点在水平的边P1P2上，则点在多边形内
                        return true;
                    }
                }
                /**
                 * 判断点经过多边形顶点
                 */
                if (((point.getLat() == point1.getLat() && point.getLng() < point1.getLng()))
                        || (point.getLat() == point2.getLat() && point.getLng() < point2.getLng())) {
                    if (point2.getLat() < point1.getLat()) {
                        intersectPointWeights += -0.5;
                    } else if (point2.getLat() > point1.getLat()) {
                        intersectPointWeights += 0.5;
                    }
                }
            }
            point1 = point2;
        }

        if ((intersectPointCount + Math.abs(intersectPointWeights)) % 2 == 0) {// 偶数在多边形外
            return false;
        } else { // 奇数在多边形内
            return true;
        }
    }
    /**
     * 判断点是否在矩形内在矩形边界上，也算在矩形内(根据这些点，构造一个外包矩形)
     *
     * @param point          点对象
     * @param boundaryPoints 矩形边界点
     * @return
     */
    public static boolean isPointInRectangle(BmapPoint point, BmapPoint[] boundaryPoints) {
        BmapPoint southWestPoint = getSouthWestPoint(boundaryPoints); // 西南角点
        BmapPoint northEastPoint = getNorthEastPoint(boundaryPoints); // 东北角点
        return (point.getLng() >= southWestPoint.getLng() && point.getLng() <= northEastPoint.getLng()
                && point.getLat() >= southWestPoint.getLat() && point.getLat() <= northEastPoint.getLat());

    }

    public static boolean isPointInRectangle(BmapPoint point, List<BmapPoint> boundaryPoints) {
        BmapPoint southWestPoint = getSouthWestPoint(boundaryPoints); // 西南角点
        BmapPoint northEastPoint = getNorthEastPoint(boundaryPoints); // 东北角点
        return (point.getLng() >= southWestPoint.getLng() && point.getLng() <= northEastPoint.getLng()
                && point.getLat() >= southWestPoint.getLat() && point.getLat() <= northEastPoint.getLat());

    }
    /**
     * 根据这组坐标，画一个矩形，而后获得这个矩形西南角的顶点坐标
     *
     * @param vertexs
     * @return
     */
    private static BmapPoint getSouthWestPoint(BmapPoint[] vertexs) {
        double minLng = vertexs[0].getLng(), minLat = vertexs[0].getLat();
        for (BmapPoint bmapPoint : vertexs) {
            double lng = bmapPoint.getLng();
            double lat = bmapPoint.getLat();
            if (lng < minLng) {
                minLng = lng;
            }
            if (lat < minLat) {
                minLat = lat;
            }
        }
        return new BmapPoint(minLng, minLat);
    }

    private static BmapPoint getSouthWestPoint(List<BmapPoint> vertexs) {
        double minLng = vertexs.get(0).getLng(), minLat = vertexs.get(0).getLat();
        for (BmapPoint bmapPoint : vertexs) {
            double lng = bmapPoint.getLng();
            double lat = bmapPoint.getLat();
            if (lng < minLng) {
                minLng = lng;
            }
            if (lat < minLat) {
                minLat = lat;
            }
        }
        return new BmapPoint(minLng, minLat);
    }
    /**
     * 根据这组坐标，画一个矩形，而后获得这个矩形东北角的顶点坐标
     *
     * @param vertexs
     * @return
     */
    private static BmapPoint getNorthEastPoint(BmapPoint[] vertexs) {
        double maxLng = 0.0d, maxLat = 0.0d;
        for (BmapPoint bmapPoint : vertexs) {
            double lng = bmapPoint.getLng();
            double lat = bmapPoint.getLat();
            if (lng > maxLng) {
                maxLng = lng;
            }
            if (lat > maxLat) {
                maxLat = lat;
            }
        }
        return new BmapPoint(maxLng, maxLat);
    }
    private static BmapPoint getNorthEastPoint(List<BmapPoint> vertexs) {
        double maxLng = 0.0d, maxLat = 0.0d;
        for (BmapPoint bmapPoint : vertexs) {
            double lng = bmapPoint.getLng();
            double lat = bmapPoint.getLat();
            if (lng > maxLng) {
                maxLng = lng;
            }
            if (lat > maxLat) {
                maxLat = lat;
            }
        }
        return new BmapPoint(maxLng, maxLat);
    }

    /**
     * @Description: 计算最大最小经纬度
     * @author: xx
     * @Date: 2023/2/9 15:56
     * @param0: dto
     * @param1: jsonArray
     * @paramTypes: [com.xx.utils.bean.AcmeLonAndLat, com.alibaba.fastjson2.JSONArray]
     * @return: com.xx.utils.bean.AcmeLonAndLat
     */
    public static AcmeLonAndLat calcLongitudeAndLatitude(AcmeLonAndLat dto, JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Double lat = jsonObject.getDouble("lat");
            Double lon = jsonObject.getDouble("lon");
            if (i == 0) {
                dto.setMaxLat(lat);
                dto.setMinLat(lat);
                dto.setMaxLon(lon);
                dto.setMinLon(lon);
            } else {
                if (lat > dto.getMaxLat()) {
                    dto.setMaxLat(lat);
                }
                if (lat < dto.getMinLat()) {
                    dto.setMinLat(lat);
                }
                if (lon > dto.getMaxLon()) {
                    dto.setMaxLon(lon);
                }
                if (lon < dto.getMinLon()) {
                    dto.setMinLon(lon);
                }
            }
        }
        return dto;
    }

    /**
     * 计算最大最小经纬度
     * @param dto
     * @param jsonArray
     * @return
     */
    public static AcmeLonAndLatBigDecimal calcLongitudeAndLatitude(AcmeLonAndLatBigDecimal dto, JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            BigDecimal lat = new BigDecimal(jsonObject.getString("lat"));
            BigDecimal lon = new BigDecimal(jsonObject.getString("lon"));
            if (i == 0) {
                dto.setMaxLat(lat);
                dto.setMinLat(lat);
                dto.setMaxLon(lon);
                dto.setMinLon(lon);
            } else {
                if (lat.compareTo(dto.getMaxLat()) > 0) {
                    dto.setMaxLat(lat);
                }
                if (lat.compareTo(dto.getMinLat()) < 0) {
                    dto.setMinLat(lat);
                }
                if (lon.compareTo(dto.getMaxLon()) > 0) {
                    dto.setMaxLon(lon);
                }
                if (lon.compareTo(dto.getMinLon()) < 0) {
                    dto.setMinLon(lon);
                }
            }
        }
        return dto;
    }

    /**
     * 批量计算
     * @param dto
     * @param points
     * @return
     */
    public static AcmeLonAndLat calcLongitudeAndLatitude(AcmeLonAndLat dto, List<BmapPoint> points) {
        if (CollectionUtils.isEmpty(points)) {
            return dto;
        }
        for (int i = 0; i < points.size(); i++) {
            BmapPoint point = points.get(i);
            Double lat = point.getLat();
            Double lon = point.getLng();
            if (i == 0) {
                dto.setMaxLat(lat);
                dto.setMinLat(lat);
                dto.setMaxLon(lon);
                dto.setMinLon(lon);
            } else {
                if (lat > dto.getMaxLat()) {
                    dto.setMaxLat(lat);
                }
                if (lat < dto.getMinLat()) {
                    dto.setMinLat(lat);
                }
                if (lon > dto.getMaxLon()) {
                    dto.setMaxLon(lon);
                }
                if (lon < dto.getMinLon()) {
                    dto.setMinLon(lon);
                }
            }
        }
        return dto;
    }

    public static AcmeLonAndLatBigDecimal calcLongitudeAndLatitude(AcmeLonAndLatBigDecimal dto, List<BmapPointBigDecimal> points) {
        if (CollectionUtils.isEmpty(points)) {
            return dto;
        }
        for (int i = 0; i < points.size(); i++) {
            BmapPointBigDecimal point = points.get(i);
            BigDecimal lat = point.getLat();
            BigDecimal lon = point.getLon();
            if (i == 0) {
                dto.setMaxLat(lat);
                dto.setMinLat(lat);
                dto.setMaxLon(lon);
                dto.setMinLon(lon);
            } else {
                if (lat.compareTo(dto.getMaxLat()) > 0) {
                    dto.setMaxLat(lat);
                }
                if (lat.compareTo(dto.getMinLat()) < 0) {
                    dto.setMinLat(lat);
                }
                if (lon.compareTo(dto.getMaxLon()) > 0) {
                    dto.setMaxLon(lon);
                }
                if (lon.compareTo(dto.getMinLon()) < 0) {
                    dto.setMinLon(lon);
                }
            }
        }
        return dto;
    }
}
