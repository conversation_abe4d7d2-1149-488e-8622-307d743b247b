package com.xx.utils.giu;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用于计算多个经纬度内的中心点
 */
public class randomGetDown {

    /**
     *  根据输入的地点坐标计算中心点
     * @param
     * @return
     */
    public static Map<String,Double> getCenterPoint(String str) {
        String[] arr = str.split(";");
        int total = arr.length;
        double X = 0, Y = 0, Z = 0;
        for(int i=0;i<arr.length;i++){
            double lat, lon, x, y, z;
            lon = Double.parseDouble(arr[i].split(",")[0]) * Math.PI / 180;
            lat = Double.parseDouble(arr[i].split(",")[1]) * Math.PI / 180;
            x = Math.cos(lat) * Math.cos(lon);
            y = Math.cos(lat) * Math.sin(lon);
            z = Math.sin(lat);
            X += x;
            Y += y;
            Z += z;
        }

        X = X / total;
        Y = Y / total;
        Z = Z / total;
        double Lon = Math.atan2(Y, X);
        double Hyp = Math.sqrt(X * X + Y * Y);
        double Lat = Math.atan2(Z, Hyp);

        Map<String,Double> map = new HashMap<String,Double>();
        map.put("lng", Lon * 180 / Math.PI);
        map.put("lat", Lat * 180 / Math.PI);
        return map;
    }

    public static void main(String[] args) {
        String lng_lat = "118.778076,30.905645;118.780764,30.914549;118.782928,30.9186;118.785621,30.92202;118.790005,30.919169;118.790796,30.917311;118.791029,"
                + "30.915204;118.791137,30.914693;118.790436,30.905754;118.790005,30.904762;118.786663,30.901354;118.781543,30.903151";
        Map <String,Double>  map = getCenterPoint(lng_lat);
        System.out.println(map.get("lng") +","+ map.get("lat"));//30.589848801178064,118.65923827037791
        String[] split = lng_lat.split(";");
        List<GeoCoordinate> list = new ArrayList<>();
        for (int i = 0; i < split.length; i++) {
            GeoCoordinate shipPosition = new GeoCoordinate();
            String[] split1 = split[i].split(",");
            shipPosition.setLatitude(Double.parseDouble(split1[1]));
            shipPosition.setLongitude(Double.parseDouble(split1[0]));
            list.add(shipPosition);
        }

        List<ShipPosition> ships = new ArrayList<ShipPosition>();

 /*       ships.add( new ShipPosition(118.79199,30.91149)); //1
        ships.add( new ShipPosition(118.79379,30.91209)); //2
        ships.add( new ShipPosition(118.79491,30.91372)); //3*/

/*        ships.add( new ShipPosition(118.78881,30.9087)); //3
        ships.add( new ShipPosition(118.78667,30.90776)); //2
        ships.add( new ShipPosition(118.78487,30.90579)); //1*/

        ships.add( new ShipPosition(118.79169,30.91875)); //3
        ships.add( new ShipPosition(118.78942,30.91707)); //2
        ships.add( new ShipPosition(118.78813,30.91548)); //1
        System.out.println(ShipTrajectoryAnalyzer.isShipEnteringArea(ships, list));;
    }


}






