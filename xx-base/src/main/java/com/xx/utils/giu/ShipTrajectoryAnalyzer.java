package com.xx.utils.giu;

import java.util.List;
import java.util.Map;

public class ShipTrajectoryAnalyzer {
    // 判断船舶是否进入某个区域
    public static boolean isShipEnteringArea(List<ShipPosition> positions, List<GeoCoordinate> geoCoordinateList) {
        if (positions == null || positions.isEmpty() || geoCoordinateList == null) {
            return false;
        }
        // 计算船舶的移动方向
        double direction = calcShipDirection(positions);
        Map<String, Double> centerPoint = GetCenterPointFromListOfCoordinates.getCenterPoint(geoCoordinateList);
        ShipPosition shipPosition = new ShipPosition();
        shipPosition.setLon(centerPoint.get("lon"));
        shipPosition.setLat(centerPoint.get("lat"));
        // 计算船舶当前位置与区域中心点的连线方向
        double centerDirection = calcDirection(positions.get(positions.size() - 1), shipPosition);

        // 计算船舶移动方向与连线方向之间的夹角
        double angle = Math.abs(direction - centerDirection);
        if (angle > 180) {
            angle = 360 - angle;
        }

        // 如果夹角小于90度，则表明船舶正在进入该区域
        return angle < 90;
    }

    // 计算船舶的移动方向
    private static double calcShipDirection(List<ShipPosition> positions) {
        int size = positions.size();
        if (size < 2) {
            return 0;
        }

        ShipPosition lastPos = positions.get(size - 1);
        ShipPosition prevPos = positions.get(size - 2);
        double deltaX = lastPos.getLon() - prevPos.getLon();
        double deltaY = lastPos.getLat() - prevPos.getLat();
        double direction = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
        if (direction < 0) {
            direction += 360;
        }
        return direction;
    }

    // 计算两个位置点之间的方向
    private static double calcDirection(ShipPosition fromPos, ShipPosition toPos) {
        double deltaX = toPos.getLon() - fromPos.getLon();
        double deltaY = toPos.getLat() - fromPos.getLat();
        double direction = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
        if (direction < 0) {
            direction += 360;
        }
        return direction;
    }
}
