package com.xx.utils.giu;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.math.BigDecimal;

/**
 * 用于构造百度地图中的经纬度点
 */
public class BmapPointBigDecimal {
    private BigDecimal lon;// 经度
    private BigDecimal lat;// 纬度

    public BmapPointBigDecimal() {

    }

    public BmapPointBigDecimal(BigDecimal lon, BigDecimal lat) {
        this.lon = lon;
        this.lat = lat;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof BmapPointBigDecimal) {
            BmapPointBigDecimal bmapPoint = (BmapPointBigDecimal) obj;
            return (bmapPoint.getLon().compareTo(lon) == 0 && bmapPoint.getLat().compareTo(lat) == 0) ? true : false;
        } else {
            return false;
        }
    }

    public BigDecimal getLon() {
        return lon;
    }

    public void setLon(BigDecimal lon) {
        this.lon = lon;
    }

    public BigDecimal getLat() {
        return lat;
    }

    public void setLat(BigDecimal lat) {
        this.lat = lat;
    }

/*    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE)
                .append("lon", lon)
                .append("lat", lat)
                .toString();
    }*/
    @Override
    public String toString() {
        return new ToStringBuilder(this)
                .append("lon", lon)
                .append("lat", lat)
                .toString();
    }
}
