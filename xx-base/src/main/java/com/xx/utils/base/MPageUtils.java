package com.xx.utils.base;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xx.common.utils.PageUtils;
import com.xx.common.utils.sql.SqlUtil;
import com.xx.core.page.MPageDomain;
import com.xx.core.page.MPTableSupport;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: MPPageUtils
 * @Description:
 * @Date: 2023/3/8 18:27
 * @since JDK 1.8
 */
public class MPageUtils extends PageUtils {
    /**
     * 设置MybatisPlus请求分页数据 默认排序
     */
    public static Page mpPage() {
        return mpPage(true, null,true);
    }

    /**
     * 设置MybatisPlus请求分页数据
     */
    public static Page mpPage(boolean orderBool) {
        return mpPage(orderBool, null,true);
    }

    /**
     * 设置MybatisPlus请求分页数据
     */
    public static Page mpPage(boolean orderBool, String column, boolean count) {
        MPageDomain pageDomain = MPTableSupport.buildMPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        Page page = new Page(pageNum, pageSize);
        page.setSearchCount(count);
        if (StringUtils.isNotBlank(column)) {
            page.setCountId(column);
        }
        if (orderBool) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrder());
            page.addOrder(new OrderItem(orderBy, pageDomain.getIsAsc().equals("asc")));
        }
        return page;
    }
}
