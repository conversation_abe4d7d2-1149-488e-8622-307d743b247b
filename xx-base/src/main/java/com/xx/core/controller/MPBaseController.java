package com.xx.core.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.pagehelper.PageInfo;
import com.xx.common.constant.HttpStatus;
import com.xx.common.core.controller.BaseController;
import com.xx.common.core.page.TableDataInfo;

import java.util.List;

/**
 * @ClassName: MPBaseController
 * @Description:
 * @Date: 2023/3/8 18:24
 * <AUTHOR>
 * @version 1.0
 * @since JDK 1.8
 */

public class MPBaseController extends BaseController {


    protected TableDataInfo getDataTable(IPage page)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(page.getRecords());
        rspData.setTotal(page.getTotal());
        return rspData;
    }
    public static TableDataInfo getDataTable(List<?> list, PageInfo info)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(info.getTotal());
        return rspData;
    }

    public static TableDataInfo getDataTableError(String msg)
    {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.ERROR);
        rspData.setMsg(msg);
        rspData.setRows(null);
        rspData.setTotal(0);
        return rspData;
    }
}
