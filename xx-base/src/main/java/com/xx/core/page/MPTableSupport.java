package com.xx.core.page;

import com.xx.common.core.page.TableSupport;
import com.xx.common.core.text.Convert;
import com.xx.common.utils.ServletUtils;

/**
 * 表格数据处理
 *
 * <AUTHOR>
 */
public class MPTableSupport extends TableSupport
{



    public static MPageDomain buildMPageRequest()
    {
        return getMPageDomain();
    }

    public static MPageDomain getMPageDomain()
    {
        MPageDomain pageDomain = new MPageDomain();
        pageDomain.setPageNum(Convert.toInt(ServletUtils.getParameter(PAGE_NUM), 1));
        pageDomain.setPageSize(Convert.toInt(ServletUtils.getParameter(PAGE_SIZE), 10));
        pageDomain.setOrderByColumn(ServletUtils.getParameter(ORDER_BY_COLUMN));
        pageDomain.setIsAsc(ServletUtils.getParameter(IS_ASC));
        pageDomain.setReasonable(ServletUtils.getParameterToBool(REASONABLE));
        return pageDomain;
    }
}
