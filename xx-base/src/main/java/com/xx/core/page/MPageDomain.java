package com.xx.core.page;

import com.xx.common.core.page.PageDomain;
import com.xx.common.utils.StringUtils;

/**
 * 分页数据
 *
 * <AUTHOR>
 */
public class MPageDomain extends PageDomain
{

    public String getOrder()
    {
        if (StringUtils.isEmpty(super.getOrderByColumn()))
        {
            return "";
        }
        return StringUtils.toUnderScoreCase(super.getOrderByColumn());
    }

}
