package com.xx.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

public enum DeviceEnum {

    RADAR(1, "雷达"),
    BROADCAST(2, "广播"),
    SEARCHLIGHT(3, "探照灯"),
    VHF(4, "VHF"),
    CAMERA(5, "摄像头");

    @EnumValue
    private Integer key;

    @JsonValue
    private String display;

    DeviceEnum(Integer key, String display) {
        this.key = key;
        this.display = display;
    }

    public Integer getKey() {
        return key;
    }

    public String getDisplay() {
        return display;
    }
}
