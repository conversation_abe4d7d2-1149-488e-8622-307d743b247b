package ${packageName}.service.impl;

import java.util.List;
#foreach ($column in $columns)
#if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
import com.ruoyi.common.utils.DateUtils;
#break
#end
#end
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.StringUtils;
#if($table.sub)
import java.util.Arrays;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
import ${packageName}.mapper.${subClassName}Mapper;
#end
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;

/**
 * ${functionName}Service业务层处理
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}> implements I${ClassName}Service {
    @Autowired
    private ${ClassName}Mapper ${className}Mapper;
#if($table.sub)

    @Autowired
    private ${subClassName}Mapper ${subclassName}Mapper;
#end        
    
    /**
     * 查询${functionName}列表
     * 
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}> select${ClassName}List(${ClassName} ${className}) {
    	LambdaQueryWrapper<${ClassName}> queryWrapper = Wrappers.lambdaQuery();
#foreach($column in $columns)
#set($queryType=$column.queryType)
#set($javaField=$column.javaField)
#set($javaType=$column.javaType)
#set($columnName=$column.columnName)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#if($column.query)
#if($column.queryType == "EQ")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end   
			queryWrapper.eq(${ClassName}::get$AttrName, ${className}.get$AttrName());
		}
#elseif($queryType == "NE")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end
			queryWrapper.ne(${ClassName}::get$AttrName, ${className}.get$AttrName());
		}
#elseif($queryType == "GT")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end
			queryWrapper.gt(${ClassName}::get$AttrName, ${className}.get$AttrName());
		}
#elseif($queryType == "GTE")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end
			queryWrapper.ge(${ClassName}::get$AttrName, ${className}.get$AttrName());
		}
#elseif($queryType == "LT")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end
			queryWrapper.lt(${ClassName}::get$AttrName, ${className}.get$AttrName());
		}
#elseif($queryType == "LTE")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end
			queryWrapper.le(${ClassName}::get$AttrName, ${className}.get$AttrName()));
		}
#elseif($queryType == "LIKE")
#if($javaType == 'String')
        if (StringUtils.isNotEmpty(${className}.get$AttrName())) {
#else
        if (StringUtils.isNotNull(${className}.get$AttrName())) {
#end
			queryWrapper.like(${ClassName}::get$AttrName, ${className}.get$AttrName());
		}
#elseif($queryType == "BETWEEN")
        if (StringUtils.isNotEmpty(params.begin$AttrName) and StringUtils.isNotEmpty(params.end$AttrName)) {
			queryWrapper.between(${ClassName}::get$AttrName, #{params.begin$AttrName}, #{params.end$AttrName});
		}
#end
#end
#end    
        return ${className}Mapper.selectList(queryWrapper);
    }
#if($table.sub)

    /**
     * 查询${functionName}
     * 
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    @Override
    public ${ClassName} select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField}) {
        return ${className}Mapper.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }

    /**
     * 新增${functionName}
     * 
     * @param ${className} ${functionName}
     * @return 结果
     */
#if($table.sub)
    @Transactional
#end
    @Override
    public int insert${ClassName}(${ClassName} ${className}) {
#foreach ($column in $columns)
#if($column.javaField == 'createTime')
        ${className}.setCreateTime(DateUtils.getNowDate());
#end
#end
#if($table.sub)
        int rows = ${className}Mapper.insert(${className});
        insert${subClassName}(${className});
        return rows;
#else
        return ${className}Mapper.insert(${className});
#end
    }

    /**
     * 修改${functionName}
     * 
     * @param ${className} ${functionName}
     * @return 结果
     */
#if($table.sub)
    @Transactional
#end
    @Override
    public int update${ClassName}(${ClassName} ${className}) {
#foreach ($column in $columns)
#if($column.javaField == 'updateTime')
        ${className}.setUpdateTime(DateUtils.getNowDate());
#end
#end
#if($table.sub)
        LambdaQueryWrapper<${subClassName}> queryWrapper = Wrappers.lambdaQuery();
     	queryWrapper.eq(${subClassName}::get${pkColumn.capJavaField}, ${className}.get${pkColumn.capJavaField}());
     	${subclassName}Mapper.delete(queryWrapper);
        
        insert${subClassName}(${className});
#end
        return ${className}Mapper.updateById(${className});
    }

    /**
     * 批量删除${functionName}
     * 
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
#if($table.sub)
    @Transactional
#end
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s) {
#if($table.sub)
        LambdaQueryWrapper<${subClassName}> queryWrapper = Wrappers.lambdaQuery();
     	queryWrapper.in(${subClassName}::get${pkColumn.capJavaField}, Arrays.asList(${pkColumn.javaField}s));
     	${subclassName}Mapper.delete(queryWrapper);
#end
        return ${className}Mapper.deleteBatchIds(Arrays.asList(${pkColumn.javaField}s));
    }

    /**
     * 删除${functionName}信息
     * 
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */
#if($table.sub)
    @Transactional
#end
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField}) {
#if($table.sub)        
        LambdaQueryWrapper<${subClassName}> queryWrapper = Wrappers.lambdaQuery();
     	queryWrapper.eq(${subClassName}::get${pkColumn.capJavaField}, ${pkColumn.javaField});
     	${subclassName}Mapper.delete(queryWrapper);
#end
        return ${className}Mapper.deleteById(${pkColumn.javaField});
    }
    
#if($table.sub)
    /**
     * 新增${subTable.functionName}信息
     * 
     * @param ${className} ${functionName}对象
     */
    public void insert${subClassName}(${ClassName} ${className}) {
        List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
        ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
        if (StringUtils.isNotNull(${subclassName}List)) {
            for (${subClassName} ${subclassName} : ${subclassName}List) {
                ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                ${subclassName}Mapper.insert(${subclassName});
            }
        }
    }
#end

#end
}
