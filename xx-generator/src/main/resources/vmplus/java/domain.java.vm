package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
#if($table.crud || $table.sub)
import com.ruoyi.common.core.domain.BasePlusEntity;
#elseif($table.tree)
import com.ruoyi.common.core.domain.TreePlusEntity;
#end
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BasePlusEntity")
#elseif($table.tree)
#set($Entity="TreePlusEntity")
#end
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "${tableName}")
public class ${ClassName} extends ${Entity} {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    #if($column.columnType == 'date')
    @JsonFormat(pattern = "yyyy/MM/dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
    #else
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
    #end
    #elseif($column.javaType == 'LocalDate')
    @JsonFormat(pattern = "yyyy/MM/dd")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
    #elseif($column.javaType == 'LocalDateTime')
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Excel(name = "${comment}", width = 35, dateFormat = "yyyy-MM-dd HH:mm:ss")
    #else
    @Excel(name = "${comment}")
#end
#end
#if($column.isPk==1)
    @TableId(type = IdType.AUTO)
    #elseif($column.javaType == 'String')
    @TableId(value = "$column.columnName", type = IdType.ASSIGN_UUID)
    #elseif($column.javaType == 'Long')
    @TableId(value = "$column.columnName")
#end
    private $column.javaType $column.javaField;

#end
#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    @TableField(exist = false)
    private List<${subClassName}> ${subclassName}List;

#end
}
