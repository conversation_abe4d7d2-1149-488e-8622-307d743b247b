package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.yjs.common.annotation.Excel;
import java.io.Serializable;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end
import lombok.Data;
#if($table.crud || $table.sub)
#elseif($table.tree)
#end

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="MPBaseEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("${tableName}")
@ApiModel(value = "${ClassName}", description = "${tableComment}")
public class ${ClassName} extends ${Entity} implements Serializable{
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#else
    @Excel(name = "${comment}")
#end
#end
#if($column.isPk() == true)
    #if($column.isIncrement() == true)
    @TableId(value = "$column.columnName", type = IdType.AUTO)
    #elseif($column.javaType == 'String')
    @TableId(value = "$column.columnName", type = IdType.ASSIGN_UUID)
    #elseif($column.javaType == 'Long')
    @TableId(value = "$column.columnName")
    #end
#end
    #if($column.isRequired() == true)
    @ApiModelProperty(name = "${column.javaField}", value = "${comment}", dataType = "${column.javaType}", required = true)
    #else
    @ApiModelProperty(name = "${column.javaField}", value = "${comment}", dataType = "${column.javaType}")
    #end
    #if($column.javaType == 'Date')
    #if($column.columnType == 'date')
    @JsonFormat(pattern = "yyyy/MM/dd")
    #else
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    #end
    #end
    private $column.javaType $column.javaField;
#end
#end
}
