package ${packageName}.service;

import ${packageName}.domain.${ClassName};
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IService<${ClassName}> {

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}集合
     */
    List<${ClassName}> select${ClassName}List(${ClassName} ${className});

}
