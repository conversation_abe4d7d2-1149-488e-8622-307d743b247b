
#!/bin/bash
#export JAVA_HOME=/app/jdk/jdk1.8.0_25
#export PATH=$PATH:$JAVA_HOME/bin
 
#java虚拟机启动参数  
#JAVA_OPTS="-ms1024m -mx1024m -Xmn256m -XX:MaxPermSize=128m"
JAVA_OPTS="-Xms512M -Xmx2048M -Xmn512m"
 
#需要启动的Java主程序（main方法类）  
APP_MAINCLASS=cloud_live_streaming.jar
 
#进行jar包路径的添加
#if test -n "$2" 
#        then    $APP_MAINCLASS=$2
#fi
 
#echo "The path of jar is $APP_MAINCLASS"
 
#初始化psid变量（全局）  
psid=0
 
checkpid() {  
   javaps=`ps -ef | grep $APP_MAINCLASS | grep -v grep`  
   echo "javaps===========$javaps"
   if [ -n "$javaps" ]; then
      psid=`echo $javaps | awk '{print $2}'` 
	echo "psid===========$psid"	  
   else
      psid=0  
	  echo "psid===========0000000"	
   fi  
}  
 
start() {  
   checkpid  
   
   if [ $psid -ne 0 ]; then  
      echo "info: $APP_MAINCLASS 启动着! 请使用重启命令 (pid=$psid)"  
   else  
      echo -n "$APP_MAINCLASS 开始启动 ..."  
      `nohup java -Dspring.profiles.active=beta $JAVA_OPTS -jar $APP_MAINCLASS >log/c.log 2>&1 &`
      checkpid  
      if [ $psid -ne 0 ]; then  
         echo "$APP_MAINCLASS 启动成功."  
      else  
         echo "启动失败 :$APP_MAINCLASS"  
      fi  
   fi  
}  
 
stop() {  
	echo "关闭所有 $APP_MAINCLASS 进程."  
	ps -ef | grep $APP_MAINCLASS | grep -v grep | awk '{print $2}' | xargs kill -9
	echo "关闭完成"  
}
 
 
case "$1" in  
   'start')  
      start  
      ;;  
   'stop')  
     stop  
     ;;  
   'restart')  
     stop  
     start  
     ;;  
  *)  
echo "Usage: $0 {start|stop|restart}"  
exit 1  
esac   
exit 0 