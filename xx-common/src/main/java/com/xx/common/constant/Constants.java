package com.xx.common.constant;

import io.jsonwebtoken.Claims;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants
{

    /**
     * 生产环境标识
     */
    public static final String PROD_ENV = "prod";

    /**
     * 航迹redis key前缀
     */
    public static final String SHIP_TRACK_KEY = "ship_track:";



    public static final String SHIP_CHINESE_NAME = "ship_chinese_name:";

    /**
     * 预警redis key前缀
     */
    public static final String TRACK_EARLY_KEY = "track_early:";

    /**
     * 黑名单redis key前缀
     */
    public static final String BLACK_LIST = "black_list";

    /**
     * 黑白名单redis key前缀
     */
    public static final String WHITE_LIST = "white_list";

    /**
     * warning area, waterway, monitoring area,
     * 通航环境 redis key前缀
     */
    public static final String ENV_AREA = "env_area:";

    /**
     * 预警列表 redis key
     */
    public static final String WARNING_LIST = "warning_list:";

    /**
     * 预警规则列表 redis key
     */
    public static final String WARNING_RULES = "warning_rules";

    /**
     * 处置规则列表 redis key
     */
    public static final String DISPOSE_RULE = "dispose_rule";
    /**
     * 一级预警区
     */
    public static final String ONE_WARNING = "one_warning";

    /**
     * 二级预警区
     */
    public static final String TWO_WARNING = "two_warning";
    /**
     * 三级预警区
     */
    public static final String THREE_WARNING = "three_warning";

    /**
     * 航道
     */
    public static final String SHIPPING_LANE = "shipping_lane";

    /**
     * 监控区
     */
    public static final String MONITORING = "monitoring";
    /**
     * 港口
     */
    public static final String HARBOR = "harbor";
    /**
     * 港口
     */
    public static final String ANCHORAGE_GROUND = "anchorage_ground";
    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";

    /**
     * www主域
     */
    public static final String WWW = "www.";

    /**
     * http请求
     */
    public static final String HTTP = "http://";

    /**
     * https请求
     */
    public static final String HTTPS = "https://";

    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";

    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";

    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";

    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";

    /**
     * 注册
     */
    public static final String REGISTER = "Register";

    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";

    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;

    /**
     * 令牌
     */
    public static final String TOKEN = "token";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";

    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";

    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = Claims.SUBJECT;

    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";

    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";

    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";

    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";

    /**
     * RMI 远程方法调用
     */
    public static final String LOOKUP_RMI = "rmi:";

    /**
     * LDAP 远程方法调用
     */
    public static final String LOOKUP_LDAP = "ldap:";

    /**
     * LDAPS 远程方法调用
     */
    public static final String LOOKUP_LDAPS = "ldaps:";

    /**
     * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
     */
    public static final String[] JOB_WHITELIST_STR = { "com.xx" };

    /**
     * 定时任务违规的字符
     */
    public static final String[] JOB_ERROR_STR = { "java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
            "org.springframework", "org.apache", "com.xx.common.utils.file", "com.xx.common.config" };
}
