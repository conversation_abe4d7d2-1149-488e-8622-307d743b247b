//package com.xx;
//
//import com.xx.web.domain.MgTrack;
//import com.xx.web.service.MgTrackService;
//import com.xx.utils.IdWorker;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
///**
// * @description:
// * @author: xx
// * @Date 2023/7/22 11:37
// * @version: 1.0
// */
//@SpringBootTest(classes = XxApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class MongodbTest {
//
//    @Autowired
//    private  MgTrackService mgTrackService;
//    @Autowired
//    private  IdWorker idWorker;
//    @Test
//    public void testBatchSave(){
//        List<MgTrack> tracks = new ArrayList<>();
//        MgTrack mgTrack = new MgTrack();
//        mgTrack.setId(idWorker.nextIdStr());
//        mgTrack.setMmsi(123456789);
//        mgTrack.setTime(new Date().getTime());
//        try {
//            Thread.sleep(2000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//
//        MgTrack mgTrack1 = new MgTrack();
//        mgTrack1.setId(idWorker.nextIdStr());
//        mgTrack1.setMmsi(456789200);
//        mgTrack1.setTime(new Date().getTime());
//        tracks.add(mgTrack);
//        tracks.add(mgTrack1);
//        mgTrackService.batchSave(tracks);
//    }
//
//}
