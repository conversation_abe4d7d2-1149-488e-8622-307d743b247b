package com.xx;

import com.xx.common.constant.Constants;
import com.xx.dk.tbsalling.aismessages.utils.LimitedSizeQueue;
import com.xx.utils.redis.RedisUtil;
import com.xx.web.domain.entity.WhiteList;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/24 14:30
 * @version: 1.0
 */
@SpringBootTest(classes = XxApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT )
public class LimitedSizeQueueTest {

    @Autowired
    private RedisUtil redisUtil;
    //@Test
    public void testFixedSizeQueue() throws IOException {

        LimitedSizeQueue<Integer> queue = new LimitedSizeQueue<>(10);

        new Thread(() -> {
            int i = 0;
            while (true) {
                try {
                    TimeUnit.MILLISECONDS.sleep(200);
                    queue.add(i++);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                queue.add(i++);
            }
        }).start();

        new Thread(() -> {
            int i = 0;
            while (true) {
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                    queue.add(i++);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                //queue.add(i++);
            }
        }).start();

        new Thread(() -> {
            while (true) {
                try {
                    TimeUnit.SECONDS.sleep(2);
                    System.out.println(queue);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();

        System.in.read();
    }

    @Test
    public void Test11() {
        System.out.println(judgeWhiteList());
    }

    public boolean judgeWhiteList() {
        //判断白名单 在白名单就不需要设备联动, 记录留档即可
        WhiteList whiteList = redisUtil.hmGet(Constants.WHITE_LIST, 1 + "");
        if (whiteList != null) {
            LocalDateTime now = LocalDateTime.now();
            if (whiteList.getEndTime().isAfter(now) && whiteList.getStartTime().isBefore(now)) {
                //在白名单内
                //留档 retention of records
                //ShipRor shipRor = ShipConverter.INSTANCE.source2Target(ship);
                //shipRor.setName(StringUtils.isEmpty(ship.getChineseName()) ? ship.getName() : ship.getChineseName());
                //// TODO 触发预警时的航迹 shipRor.setTrack();
                //// shipRor.setTrack(...);
                //shipRor.setEnvType(warningLevel);
                //shipRorService.save(shipRor);
                return true;
            }

        }
        return false;

    }
}
