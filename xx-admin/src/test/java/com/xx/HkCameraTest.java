package com.xx;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.TextFormat;
import com.xx.common.exception.ServiceException;
import com.xx.utils.redis.RedisUtil;
import com.xx.web.proto.ShipOuterClass;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: HkCameraTest
 * @Description: https://blog.csdn.net/mx_wrc/article/details/111905029
 * @Date: 2023/4/20 21:40
 * @since JDK 1.8
 */

//@SpringBootTest(classes = XxApplication.class)
public class HkCameraTest {

//    @Autowired
    private RestTemplate restTemplate;

    private static String appKey = "7814641c3d184452b6a3da429d3e240a";
    private static String appSecret = "85b86ff73c4472fd8d524ae201e05905";

    private static String accessToken = "at.3asv9py4ak0x13a20j0mqewg8kt0l79z-6t6ilohyo3-09t4ldm-kk0fusrvr"; //20230420获取 7天后过期

    /**
     * 获取萤石云AccessToken
     */
    //@Test
    public void testGetAccessToken() {

        String url = "https://open.ys7.com/api/lapp/token/get";
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("appKey", appKey);
        params.add("appSecret", appSecret);
//        String result = HttpClientUtil.doPost(url, params);
        ResponseEntity<String> response =  post(url, params);
        System.out.println(response.getStatusCode());
        System.out.println(response.getStatusCodeValue());
        System.out.println(response.getHeaders());
        System.out.println(response.getBody());
        //200 OK
        //200
        //[Server:"Tengine", Date:"Thu, 20 Apr 2023 14:22:11 GMT", Content-Type:"application/json;charset=UTF-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Access-Control-Allow-Origin:"*", Access-Control-Allow-Methods:"*", X-Powered-By:"PS", Vary:"Accept-Encoding"]
        //{"msg":"操作成功!","code":"200","data":{"accessToken":"at.3asv9py4ak0x13a20j0mqewg8kt0l79z-6t6ilohyo3-09t4ldm-kk0fusrvr","expireTime":1682605331907}}

        //控制台: at.9002zfdo3tc46cbe56q9b2swcosc1bfb-709qh1ap8o-0cu1rn6-owla9njxc
    }

    /**
     * 获取用户下直播视频列表
     */
    //@Test
    public void testGetVideoList() {
        String url = "https://open.ys7.com/api/lapp/live/video/list";
        //accessparams
        // ken	String	授权过程获取的access_token	Y
        //pageStart	int	分页起始页，从0开始	N
        //pageSize	int	分页大小，默认为10，最大为50	N
        MultiValueMap<String,Object> params = new LinkedMultiValueMap<>();
        params.add("accessToken",accessToken);
        params.add("pageStart",0);
        params.add("pageSize",50);
        ResponseEntity<String> response = post(url, params);
        System.out.println(response.getStatusCode());
        System.out.println(response.getStatusCodeValue());
        System.out.println(response.getHeaders());
        System.out.println(response.getBody());

        checkResponse(response);
        //200 OK
        //200
        //[Server:"Tengine", Date:"Thu, 20 Apr 2023 14:42:48 GMT", Content-Type:"application/json;charset=UTF-8", Transfer-Encoding:"chunked", Connection:"keep-alive", Access-Control-Allow-Origin:"*", Access-Control-Allow-Methods:"POST, GET, DELETE, OPTIONS", Access-Control-Allow-Headers:"Content-Type, x-requested-with, X-Custom-Header"]
        /*
        {"msg":"操作成功","code":"200","data":
        [{"deviceSerial":"C54451760","channelNo":5,"deviceName":"铁塔监控 册子段","liveAddress":"http://hls01open.ys7.com/openlive/7f84d95d6b2a482782ae0117fea83554.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/7f84d95d6b2a482782ae0117fea83554.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/7f84d95d6b2a482782ae0117fea83554","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/7f84d95d6b2a482782ae0117fea83554.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/7f84d95d6b2a482782ae0117fea83554.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/7f84d95d6b2a482782ae0117fea83554.hd.flv","status":1,"exception":0,"beginTime":1677479424000,"endTime":1677479424000},
        {"deviceSerial":"C54451760","channelNo":4,"deviceName":"铁塔监控 册子段","liveAddress":"http://hls01open.ys7.com/openlive/2eb18e06433b4c9f81d6bf1f11ba94ed.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/2eb18e06433b4c9f81d6bf1f11ba94ed.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/2eb18e06433b4c9f81d6bf1f11ba94ed","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/2eb18e06433b4c9f81d6bf1f11ba94ed.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/2eb18e06433b4c9f81d6bf1f11ba94ed.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/2eb18e06433b4c9f81d6bf1f11ba94ed.hd.flv","status":1,"exception":0,"beginTime":1677479412000,"endTime":1677479412000},
        {"deviceSerial":"C54451760","channelNo":3,"deviceName":"铁塔监控 册子段","liveAddress":"http://hls01open.ys7.com/openlive/62f6ea137bd0402f9dc7f5d0513ec55d.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/62f6ea137bd0402f9dc7f5d0513ec55d.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/62f6ea137bd0402f9dc7f5d0513ec55d","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/62f6ea137bd0402f9dc7f5d0513ec55d.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/62f6ea137bd0402f9dc7f5d0513ec55d.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/62f6ea137bd0402f9dc7f5d0513ec55d.hd.flv","status":1,"exception":0,"beginTime":1677479405000,"endTime":1677479405000},
        {"deviceSerial":"C54451760","channelNo":2,"deviceName":"铁塔监控 册子段","liveAddress":"http://hls01open.ys7.com/openlive/3dd80686edf24df5b3fd2483963f4946.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/3dd80686edf24df5b3fd2483963f4946.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/3dd80686edf24df5b3fd2483963f4946","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/3dd80686edf24df5b3fd2483963f4946.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/3dd80686edf24df5b3fd2483963f4946.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/3dd80686edf24df5b3fd2483963f4946.hd.flv","status":1,"exception":0,"beginTime":1677479397000,"endTime":1677479397000},
        {"deviceSerial":"C54451760","channelNo":1,"deviceName":"铁塔监控 册子段","liveAddress":"http://hls01open.ys7.com/openlive/c4295c0389674292afccb7188d458363.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/c4295c0389674292afccb7188d458363.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/c4295c0389674292afccb7188d458363","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/c4295c0389674292afccb7188d458363.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/c4295c0389674292afccb7188d458363.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/c4295c0389674292afccb7188d458363.hd.flv","status":1,"exception":0,"beginTime":1677479389000,"endTime":1677479389000},
        {"deviceSerial":"L20848388","channelNo":1,"deviceName":"5号平台浮运","liveAddress":"http://hls01open.ys7.com/openlive/9ed6ee94eeb44513a9c832a8409df50f.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/9ed6ee94eeb44513a9c832a8409df50f.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/9ed6ee94eeb44513a9c832a8409df50f","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/9ed6ee94eeb44513a9c832a8409df50f.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/9ed6ee94eeb44513a9c832a8409df50f.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/9ed6ee94eeb44513a9c832a8409df50f.hd.flv","status":1,"exception":1,"beginTime":1676359975000,"endTime":1676359975000},
        {"deviceSerial":"K90332138","channelNo":1,"deviceName":"大桥雪浪","liveAddress":"http://hls01open.ys7.com/openlive/073f7332dbee436a955a19b1e1cdace9.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/073f7332dbee436a955a19b1e1cdace9.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/073f7332dbee436a955a19b1e1cdace9","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/073f7332dbee436a955a19b1e1cdace9.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/073f7332dbee436a955a19b1e1cdace9.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/073f7332dbee436a955a19b1e1cdace9.hd.flv","status":1,"exception":1,"beginTime":1672018386000,"endTime":1672018386000},
        {"deviceSerial":"K88614035","channelNo":1,"deviceName":"正力2200","liveAddress":"http://hls01open.ys7.com/openlive/5b628dc46cd84d8987e828fe65e4f69b.m3u8","hdAddress":"http://hls01open.ys7.com/openlive/5b628dc46cd84d8987e828fe65e4f69b.hd.m3u8","rtmp":"rtmp://rtmp01open.ys7.com/openlive/5b628dc46cd84d8987e828fe65e4f69b","rtmpHd":"rtmp://rtmp01open.ys7.com/openlive/5b628dc46cd84d8987e828fe65e4f69b.hd","flvAddress":"https://flvopen.ys7.com:9188/openlive/5b628dc46cd84d8987e828fe65e4f69b.flv","hdFlvAddress":"https://flvopen.ys7.com:9188/openlive/5b628dc46cd84d8987e828fe65e4f69b.hd.flv","status":1,"exception":0,"beginTime":1670413948000,"endTime":1670413948000},
        {"deviceSerial":"D79050745","channelNo":2,"deviceName":"","liveAddress":null,"hdAddress":null,"rtmp":null,"rtmpHd":null,"flvAddress":null,"hdFlvAddress":null,"status":1,"exception":3,"beginTime":1670141113000,"endTime":1670141113000},
        {"deviceSerial":"D79050745","channelNo":1,"deviceName":"","liveAddress":null,"hdAddress":null,"rtmp":null,"rtmpHd":null,"flvAddress":null,"hdFlvAddress":null,"status":1,"exception":3,"beginTime":1670141097000,"endTime":1670141097000}],
        "page":{"total":10,"size":50,"page":0}}
         */
    }


    private ResponseEntity<String> post(String url, MultiValueMap<String, Object> params) {
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(params,new HttpHeaders());
        ResponseEntity<String> response = restTemplate.postForEntity(url, httpEntity, String.class);
      return response;
    }

    private JSONObject checkResponse(ResponseEntity<String> stringResponseEntity) {
        if (!stringResponseEntity.getStatusCode().is2xxSuccessful()) {
            //请求失败
            throw new ServiceException("status:" + stringResponseEntity.getStatusCodeValue() + " " + stringResponseEntity.getBody());
        }
        JSONObject result = JSON.parseObject(stringResponseEntity.getBody());
/*        if (result.containsKey("errorCode") && result.getString("errorCode") != null && !result.getBooleanValue("success")) {
            //业务错误
//            log.error("wx登录失败,{}",result);
            throw new ServiceException("登录失败", HttpStatus.UNAUTHORIZED.value());
        }*/

    /*    if (!Constant.SUCCESS.equals(result.get("code"))) {

        }*/
        return result;
    }
    //@Test
    public void test() {
        System.out.println(new Date(1682605331907L));


        }

    //@Autowired
    private RedisUtil redisUtil;
    //@Test
    public void test6() {


        ArrayList<String> strings = new ArrayList<>();
        strings.add("sys_dict:*");
        strings.add("sys_dict");
        strings.add("dispose_rule");
        Set<String> stringLongMap = redisUtil.keys("sys_dict:*");
        System.out.println(stringLongMap);
    }

    //@Test
    public void test7() {
        //        System.out.println(Arrays.toString(message));
        byte[] message = new byte[]{0, 0, 0, 121, 18, 12, 87, 73, 78, 78, 73, 78, 71, 32, 82, 79, 65, 68, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, 32, -19, -117, 94, 64, 49, 0, 0, 0, 0, -101, -118, 62, 64, 61, -102, -103, 97, 65, 69, -51, -52, -110, 66, 85, 0, 0, -128, 66, 88, -32, -85, -62, -78, 1, 96, -30, -48, -45, 4, 106, 5, 51, 70, 73, 71, 52, 122, 12, -24, -125, -100, -27, -120, -87, -28, -71, -117, -24, -73, -81, -128, 1, 80, -99, 1, 0, 0, -102, 66, -91, 1, 0, 0, -80, 65, -83, 1, 0, 0, 96, 65, -75, 1, 0, 0, 64, 64, -38, 1, 6, 75, 82, 32, 84, 83, 78, 0, 0, 0, 107, 18, 12, 83, 72, 69, 78, 71, 67, 72, 65, 78, 71, 55, 55, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, 0, 70, 122, 94, 64, 49, 0, 0, 0, 64, -2, 16, 62, 64, 61, 0, 0, 56, 65, 69, 0, 0, 8, 67, 77, 0, -64, 54, -60, 85, 0, 0, -48, 66, 88, -28, -1, -123, -59, 1, 106, 5, 66, 79, 72, 74, 57, -128, 1, 70, -99, 1, 0, 0, 32, 65, -91, 1, 0, 0, -66, 66, -83, 1, 0, 0, 64, 65, -75, 1, 0, 0, 0, 65, -38, 1, 6, 78, 73, 78, 71, 66, 79, 0, 0, 0, 116, 18, 10, 72, 65, 73, 88, 85, 78, 48, 55, 49, 53, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, 32, -57, 114, 94, 64, 49, 0, 0, 0, -32, -55, 0, 62, 64, 61, 102, 102, 102, 63, 69, 102, 102, 56, 66, 77, 0, -64, 54, -60, 85, 0, -128, -1, 67, 88, -50, -48, -68, -60, 1, 106, 4, 66, 83, 74, 65, -128, 1, 55, -99, 1, 0, 0, -96, 65, -91, 1, 0, 0, 32, 66, -83, 1, 0, 0, 0, 65, -75, 1, 0, 0, -128, 64, -38, 1, 18, 88, 73, 65, 79, 77, 69, 78, 45, 45, 88, 73, 65, 90, 72, 73, 77, 69, 78, 0, 0, 0, 109, 18, 14, 80, 65, 67, 73, 70, 73, 67, 32, 68, 69, 66, 66, 73, 69, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, 0, -6, 122, 94, 64, 49, 0, 0, 0, 0, -4, 78, 62, 64, 69, -102, -103, -7, 65, 85, 0, 0, -14, 66, 88, -61, -82, -59, -128, 2, 96, -102, -72, -44, 4, 106, 5, 86, 55, 70, 82, 54, -128, 1, 80, -99, 1, 0, 0, 51, 67, -91, 1, 0, 0, 32, 66, -83, 1, 0, 0, -128, 65, -75, 1, 0, 0, -80, 65, -38, 1, 8, 90, 72, 79, 85, 83, 72, 65, 78, -32, 1, 5, 0, 0, 0, 106, 18, 15, 82, 85, 78, 32, 70, 65, 32, 66, 65, 79, 32, 90, 72, 79, 85, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, 0, 16, -127, 94, 64, 49, 0, 0, 0, 0, 13, -123, 62, 64, 61, -102, -103, 73, 65, 69, 51, 51, -96, 66, 85, 0, 0, -108, 66, 88, -80, -74, -124, -59, 1, 106, 4, 66, 82, 73, 75, -128, 1, 70, -99, 1, 0, 0, 32, 65, -91, 1, 0, 0, -10, 66, -83, 1, 0, 0, 48, 65, -75, 1, 0, 0, 80, 65, -38, 1, 8, 74, 73, 65, 78, 71, 89, 73, 78, 0, 0, 0, 110, 18, 13, 88, 73, 78, 71, 32, 84, 79, 78, 71, 32, 55, 51, 57, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, 96, -67, -122, 94, 64, 49, 0, 0, 0, -32, -57, -122, 62, 64, 61, 0, 0, 96, 65, 69, -51, -52, -96, 66, 85, 0, 0, -110, 66, 88, -40, -38, -121, -59, 1, 96, -97, -48, -36, 4, 106, 5, 66, 79, 78, 76, 52, -128, 1, 83, -99, 1, 0, 0, -28, 66, -91, 1, 0, 0, -56, 65, -83, 1, 0, 0, 64, 65, -75, 1, 0, 0, -32, 64, -38, 1, 8, 89, 65, 78, 71, 32, 75, 79, 85, 0, 0, 0, 110, 18, 12, 74, 73, 72, 65, 73, 90, 72, 73, 89, 85, 65, 78, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, -96, 114, -116, 94, 64, 49, 0, 0, 0, -64, -30, -97, 62, 64, 61, 51, 51, 3, 65, 69, 51, 51, 43, 66, 77, 0, -64, 54, -60, 85, 0, -128, -1, 67, 88, -42, -97, -47, -60, 1, 106, 4, 66, 80, 82, 83, -128, 1, 70, -99, 1, 0, 0, -96, 66, -91, 1, 0, 0, -96, 65, -83, 1, 0, 0, 16, 65, -75, 1, 0, 0, -32, 64, -38, 1, 10, 87, 65, 73, 71, 65, 73, 81, 73, 65, 79, 0, 0, 0, 109, 18, 13, 72, 65, 75, 79, 78, 69, 32, 71, 65, 76, 65, 88, 89, 24, -76, -66, -46, -53, 12, 41, 0, 0, 0, -32, 16, -127, 94, 64, 49, 0, 0, 0, 32, 28, 40, 62, 64, 61, -51, -52, 76, 62, 69, 102, -26, -107, 67, 85, 0, -128, -106, 67, 88, -68, -71, -66, -116, 2, 96, -61, -51, -43, 4, 106, 6, 57, 86, 53, 50, 48, 48, -128, 1, 82, -99, 1, 0, 0, 7, 67, -91, 1, 0, 0, -64, 65, -83, 1, 0, 0, -96, 65, -75, 1, 0, 0, -32, 64, -38, 1, 6, 67, 78, 32, 90, 79, 83, 0, 0, 0, 115, 18, 15, 67, 72, 65, 78, 71, 76, 73, 78, 72, 65, 73, 89, 65, 78, 71, 24, -74, -66, -46, -53, 12, 41, 0, 0, 0, 0, 0, -96, 102, 64, 49, 0, 0, 0, 0, 0, -64, 86, 64, 61, -102, -103, -52, 66, 69, 0, 0, -76, 67, 77, 0, -64, 54, -60, 85, 0, -128, -1, 67, 88, -48, -61, -123, -59, 1, 106, 4, 66, 69, 70, 82, -128, 1, 70, -99, 1, 0, 0, -76, 66, -91, 1, 0, 0, -96, 65, -83, 1, 0, 0, 80, 65, -75, 1, 0, 0, 96, 65, -38, 1, 9, 90, 72, 79, 85, 32, 83, 72, 65, 78, -32, 1, 1, 0, 0, 0, 114, 18, 12, 72, 65, 73, 32, 71, 65, 78, 71, 32, 49, 48, 53, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, -128, -52, -119, 94, 64, 49, 0, 0, 0, 96, 32, -109, 62, 64, 61, 51, 51, 3, 65, 69, 102, 38, -115, 67, 77, 0, -64, 54, -60, 85, 0, -128, -1, 67, 88, -102, -110, -114, -59, 1, 122, 9, -26, -75, -73, -26, -72, -81, 49, 48, 53, -128, 1, 52, -99, 1, 0, 0, 48, 65, -91, 1, 0, 0, -40, 65, -83, 1, 0, 0, -128, 64, -75, 1, 0, 0, -64, 64, -38, 1, 9, 89, 65, 78, 71, 32, 83, 72, 65, 78, 0, 0, 0, 109, 18, 13, 88, 73, 78, 71, 32, 84, 79, 78, 71, 32, 55, 56, 57, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, 96, 100, 123, 94, 64, 49, 0, 0, 0, -96, 12, 78, 62, 64, 69, -102, -103, -10, 66, 85, 0, 0, -103, 67, 88, -12, -56, -122, -59, 1, 96, -89, -96, -69, 4, 106, 5, 66, 79, 75, 78, 50, -128, 1, 82, -99, 1, 0, 0, 17, 67, -91, 1, 0, 0, 24, 66, -83, 1, 0, 0, -72, 65, -75, 1, 0, 0, 16, 65, -38, 1, 9, 90, 72, 79, 85, 32, 83, 72, 65, 78, -32, 1, 5, 0, 0, 0, 100, 18, 10, 72, 85, 65, 72, 65, 78, 71, 49, 54, 56, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, 0, -12, -120, 94, 64, 49, 0, 0, 0, -32, 101, -125, 62, 64, 61, 51, 51, 19, 65, 69, 51, 51, 99, 66, 77, 0, -64, 54, -60, 85, 0, -128, -1, 67, 88, -22, -18, -123, -59, 1, -128, 1, 70, -99, 1, 0, 0, -94, 66, -91, 1, 0, 0, -112, 65, -83, 1, 0, 0, 16, 65, -75, 1, 0, 0, 16, 65, -38, 1, 8, 77, 73, 78, 46, 72, 65, 78, 71, 0, 0, 0, 120, 18, 12, 74, 73, 65, 78, 32, 88, 73, 78, 71, 32, 49, 53, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, -32, 83, 118, 94, 64, 49, 0, 0, 0, -32, -39, 76, 62, 64, 61, -51, -52, -52, 61, 69, -102, -103, 56, 67, 85, 0, 0, -107, 67, 88, -76, -72, -109, -59, 1, 96, -33, -19, -104, 4, 106, 5, 66, 75, 86, 75, 54, 122, 8, -27, -69, -70, -27, -123, -76, 49, 53, -128, 1, 80, -99, 1, 0, 0, -100, 66, -91, 1, 0, 0, -112, 65, -83, 1, 0, 0, 16, 65, -75, 1, 0, 0, -64, 64, -38, 1, 6, 89, 85, 83, 72, 65, 78, -32, 1, 1, 0, 0, 0, 108, 18, 12, 77, 73, 78, 71, 32, 90, 72, 79, 85, 32, 54, 54, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, 32, 6, 120, 94, 64, 49, 0, 0, 0, 96, -48, 31, 62, 64, 61, -102, -103, 49, 65, 69, 0, 0, 25, 67, 85, 0, 0, 34, 67, 88, -14, -87, -112, -59, 1, 122, 8, -26, -104, -114, -27, -73, -98, 54, 54, -128, 1, 70, -99, 1, 0, 0, -36, 66, -91, 1, 0, 0, -40, 65, -83, 1, 0, 0, -96, 64, -75, 1, 0, 0, 112, 65, -38, 1, 9, 90, 72, 79, 85, 32, 83, 72, 65, 78, 0, 0, 0, 95, 18, 18, 72, 85, 67, 72, 79, 78, 71, 89, 85, 71, 79, 78, 71, 50, 50, 49, 48, 53, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, 0, 74, -122, 94, 64, 49, 0, 0, 0, -64, -39, -100, 62, 64, 69, 0, 0, -76, 67, 77, 0, -64, 54, -60, 85, 0, -128, -1, 67, 88, -36, -120, -47, -60, 1, 106, 1, 48, -128, 1, 55, -99, 1, 0, 0, -96, 65, -91, 1, 0, 0, 32, 65, -83, 1, 0, 0, -128, 64, -75, 1, 0, 0, 0, 64, 0, 0, 0, 111, 18, 15, 72, 65, 73, 32, 88, 73, 78, 32, 89, 79, 85, 32, 54, 49, 49, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, -64, -55, 107, 94, 64, 49, 0, 0, 0, 32, 82, 118, 62, 64, 61, 0, 0, 0, 65, 69, 0, -64, -118, 67, 85, 0, -128, -117, 67, 88, -102, -94, -43, -60, 1, 96, -64, -29, -61, 4, 106, 4, 66, 75, 65, 75, -128, 1, 80, -99, 1, 0, 0, -40, 66, -91, 1, 0, 0, -48, 65, -83, 1, 0, 0, 0, 65, -75, 1, 0, 0, 64, 65, -38, 1, 8, 74, 73, 78, 32, 83, 72, 65, 78, 0, 0, 0, 112, 18, 10, 87, 69, 73, 32, 67, 72, 65, 79, 32, 55, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, 96, -97, 110, 94, 64, 49, 0, 0, 0, -96, -17, 111, 62, 64, 61, -102, -103, 89, 65, 69, 51, 51, 22, 67, 85, 0, 0, 33, 67, 88, -14, -38, -47, -60, 1, 106, 5, 66, 73, 66, 75, 52, 122, 7, -28, -68, -97, -26, -67, -82, 55, -128, 1, 70, -99, 1, 0, 0, -10, 66, -91, 1, 0, 0, -56, 65, -83, 1, 0, 0, -96, 64, -75, 1, 0, 0, -128, 65, -38, 1, 9, 90, 72, 79, 85, 32, 83, 72, 65, 78, 0, 0, 0, 102, 18, 8, 77, 65, 78, 73, 76, 65, 32, 73, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, 0, -121, -127, 94, 64, 49, 0, 0, 0, -128, 116, 42, 62, 64, 69, 51, 51, -77, 67, 85, 0, -128, -91, 67, 88, -66, -60, -20, -89, 1, 96, -42, -115, -76, 4, 106, 6, 51, 69, 52, 57, 52, 55, -128, 1, 89, -99, 1, 0, 0, 22, 67, -91, 1, 0, 0, 4, 66, -83, 1, 0, 0, 64, 65, -75, 1, 0, 0, -96, 65, -38, 1, 6, 67, 78, 32, 90, 79, 83, -32, 1, 15, 0, 0, 0, 110, 18, 18, 75, 65, 78, 71, 32, 72, 69, 78, 71, 32, 84, 85, 65, 78, 32, 74, 73, 69, 24, -72, -66, -46, -53, 12, 41, 0, 0, 0, -32, 50, 120, 94, 64, 49, 0, 0, 0, 0, -21, -128, 62, 64, 61, 51, 51, -61, 64, 69, -51, -116, -79, 67, 85, 0, -128, -89, 67, 88, -96, -44, -47, -60, 1, 106, 4, 66, 89, 77, 88, -128, 1, 70, -99, 1, 0, 0, -96, 66, -91, 1, 0, 0, -120, 65, -83, 1, 0, 0, 0, 65, -75, 1, 0, 0, 0, 65, -38, 1, 9, 83, 72, 65, 78, 71, 32, 72, 65, 73};
        ByteBuffer buffer = ByteBuffer.wrap(message);
        int i = 0;
        System.out.println("start---------------------------");
        while (buffer.hasRemaining()) {
            int messageLength = buffer.getInt(); // 先读取消息长度
//            System.out.println(messageLength);
            byte[] messageBytes = new byte[messageLength];
            buffer.get(messageBytes); // 读取消息内容
            // 处理解析出来的消息
            try {
                ShipOuterClass.Ship ship = ShipOuterClass.Ship.parseFrom(messageBytes); // 解析消息
                System.out.println("client接收到消息: " + ++i);
                System.out.println("client ship: " + TextFormat.printToUnicodeString(ship));
            } catch (InvalidProtocolBufferException e) {
                throw new RuntimeException(e);
            }

        }
        System.out.println("end---------------------------");
    }



}
