package com.xx;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.filter.PropertyFilter;
import com.xx.web.domain.entity.DisposeRule;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/21 23:42
 * @version: 1.0
 */
public class TestFastJson {

    @Data
    static    class Parent {
        private int parentField;

        public Parent() {
            this.parentField = 1;
        }
    }
    @Data
    static   class Child extends Parent {
        private int childField;

        public Child() {
            this.childField = 2;
        }
    }

        public static void main(String[] args) {
            Child child = new Child();
            //child.setParentField(1);
            //child.setChildField(2);
            // 定义自定义的属性过滤器来排除父类的字段
            PropertyFilter propertyFilter = (source, name, value) -> !source.getClass().equals(Parent.class);

            // 应用自定义过滤器进行序列化
            String jsonString = JSON.toJSONString(child, propertyFilter);
            // 序列化时忽略父类的字段
            //String jsonString = JSON.toJSONString(child);
            System.out.println(jsonString);
        }

        //@Test
    public void test2() {
        DisposeRule disposeRule = new DisposeRule();
        disposeRule.setStartDate( LocalDateTime.of(2023, 6, 23, 15, 30, 0));
        disposeRule.setEndDate(LocalDateTime.of(2023, 7, 23, 18, 30, 0));
        disposeRule.setStartTimeDay(LocalTime.of(11, 30, 0));
        disposeRule.setEndTimeDay(LocalTime.of(17, 30, 0));
        LocalDateTime now = LocalDateTime.now();
        LocalTime nowTime = now.toLocalTime();
        boolean after = now.isAfter(disposeRule.getStartDate());
        boolean before = now.isBefore(disposeRule.getEndDate());
        boolean b = !nowTime.isBefore(disposeRule.getStartTimeDay());
        boolean b1 = !nowTime.isAfter(disposeRule.getEndTimeDay());
        System.out.println("after: " + after);
        System.out.println("before: " + before);
        System.out.println("b: " + b);
        System.out.println("b1: " + b1);
        if(after && before
                && b && b1){
            System.out.println("执行了");
        }else {
            System.out.println("未执行----");
        }
    }

}
