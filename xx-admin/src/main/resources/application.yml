# 项目相关配置
xx:
  # 名称
  name: xx
  # 版本
  version: 3.8.5
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/xx/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: E:/xx/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8081
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.xx: info
    org.springframework: info

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 100MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: **************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password: admin123SFKJ
    # 连接超时时间
    timeout: 100s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
#  main:
#    # 该属性设为true，就是允许循环引用存在，默认值为false
#    allow-bean-definition-overriding: true
#    allow-circular-references: true
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 360

# MyBatis配置
mybatis-plus:
  type-aliases-package: com.xx.**.domain
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    db-config:
      id-type: auto
      field-strategy: 2
      refresh-mapper: true
      db-type: mysql
      logic-delete-field: del # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  configuration:
    call-setters-on-nulls: true
    map-underscore-to-camel-case: true
    cache-enabled: false
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    config-location: classpath:mybatis/mybatis-config.xml
mybatis-plus-join:
  #开启副表逻辑删除 默认 true
  sub-table-logic: true
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
video:
  ysy:
    appKey: 7814641c3d184452b6a3da429d3e240a
    appSecret: 85b86ff73c4472fd8d524ae201e05905
ef:
  zmq:
    sub: tcp://127.0.0.1:7777
    topic: NMSHIP
    #预警过期时间 单位:秒
  ew-past: 300
#snowflake:
#  data-center-id: 1 # 数据中心ID，可以使用机器IP地址最后一段数字，范围为0-31
#  machine-id: 1 # 机器ID，可以使用服务器编号，范围为0-31
