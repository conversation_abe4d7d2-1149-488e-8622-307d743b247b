<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.system.mapper.AppVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="AppVersionResult" type="com.xx.system.domain.AppVersion">
                <id column="id" property="id"/>
                <result column="app_name" property="appName"/>
                <result column="app_version" property="appVersion"/>
                <result column="title" property="title"/>
                <result column="version_content" property="versionContent"/>
                <result column="is_mandatory" property="isMandatory"/>
                <result column="platform" property="platform"/>
                <result column="type" property="type"/>
                <result column="url" property="url"/>
                <result column="gmt_create" property="gmtCreate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectAppVersionVo">
        select `id`, `app_name`, `app_version`, `title`, `version_content`, `is_mandatory`, `platform`, `type`, `url`, `gmt_create` from app_version
    </sql>

</mapper>
