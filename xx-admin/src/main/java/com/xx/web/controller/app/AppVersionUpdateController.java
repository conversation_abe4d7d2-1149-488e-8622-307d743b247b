package com.xx.web.controller.app;

import com.xx.common.core.domain.R;
import com.xx.system.domain.dto.VersionCheckDto;
import com.xx.system.service.IAppVersionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: AppVersionUpdateController
 * @Description:
 * @Date: 2023/6/13
 * @since JDK 1.8
 */
@Api(tags = "app更新")
@RequiredArgsConstructor
@RequestMapping("app/version")
@RestController
public class AppVersionUpdateController {

    private final IAppVersionService appVersionService;

    @PostMapping("check")
    public R checkVersion(@RequestBody VersionCheckDto dto) {
        return R.ok(appVersionService.checkVersion(dto));
    }
}
