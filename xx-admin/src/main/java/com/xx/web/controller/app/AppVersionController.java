package com.xx.web.controller.app;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.config.XxConfig;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.common.utils.file.FileUploadUtils;
import com.xx.common.utils.file.MimeTypeUtils;
import com.xx.core.controller.MPBaseController;
import com.xx.interfaces.validatedGroups.Insert;
import com.xx.interfaces.validatedGroups.Update;
import com.xx.system.domain.AppVersion;
import com.xx.system.service.IAppVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-13
*/
@Api(tags = "app版本更新")
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/app-version")
public class AppVersionController extends MPBaseController{

    private final IAppVersionService appVersionService;

    @ApiOperation(value = "列表", response = AppVersion.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(AppVersion appVersion) {
        IPage<AppVersion> data = appVersionService.page(appVersion);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = AppVersion.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        AppVersion data = appVersionService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @Log(title = "app版本", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated(Insert.class) @RequestBody AppVersion appVersion) {
        return R.trBool(appVersionService.add(appVersion));
    }

    @ApiOperation(value = "修改")
    @Log(title = "app版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated(Update.class) @RequestBody AppVersion appVersion) {
        return R.trBool(appVersionService.modify(appVersion));
    }

    @ApiOperation(value = "删除(单个条目)")
    @Log(title = "app版本", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(appVersionService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @Log(title = "app版本", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(appVersionService.removes(ids));
    }


    /**
     * apk上传
     */
    @ApiOperation(value = "apk上传")
    @Log(title = "app版本", businessType = BusinessType.INSERT)
    @PostMapping("/apkfile")
    public R avatar(@RequestParam("apkfile") MultipartFile file) throws Exception
    {
        if (!file.isEmpty()) {
            String apk = FileUploadUtils.upload(XxConfig.getApkPath(), file, MimeTypeUtils.APK_EXTENSION);
            return R.ok(apk);
        }
        return R.fail("上传apk异常，请联系管理员");
    }
}

