package com.xx;

import com.xx.web.websocket.client.WSClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableScheduling
@ServletComponentScan
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class XxApplication {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(XxApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  xx管理平台启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    /*    SpringApplication application = new SpringApplication(XxApplication.class);
        application.setAllowCircularReferences(Boolean.TRUE);
        application.run(args);*/
/*        AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext();
        applicationContext.register(XxApplication.class);*/

    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }


    //@PostConstruct
    public void init() {
        System.out.println("初始化应用程序");// 初始化ws，链接服务端
        WSClient.startWS();
    }

}
