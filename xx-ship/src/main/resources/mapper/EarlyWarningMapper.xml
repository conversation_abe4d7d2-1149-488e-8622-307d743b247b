<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.EarlyWarningMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="EarlyWarningResult" type="com.xx.web.domain.entity.EarlyWarning">
        <id column="id" property="id"/>
        <result column="mmsi" property="mmsi"/>
        <result column="ship_name" property="shipName"/>
        <result column="event_content" property="eventContent"/>
        <result column="address" property="address"/>
        <result column="lon" property="lon"/>
        <result column="lat" property="lat"/>
        <result column="env_id" property="envId"/>
        <result column="env_type" property="envType"/>
        <result column="video_url" property="videoUrl"/>
        <result column="gmt_create" property="gmtCreate"/>
        <result column="gmt_modified" property="gmtModified"/>
        <result column="dispose" property="dispose"/>
        <result column="remark" property="remark"/>
        <result column="warning_lv" property="warningLv"/>
        <result column="ignore" property="ignore"/>
        <result column="finish" property="finish"/>
        <result column="end_time" property="endTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectEarlyWarningVo">
        select `id`,
               `mmsi`,
               `ship_name`,
               `event_content`,
               `address`,
               `lon`,
               `lat`,
               `env_id`,
               `env_type`,
               `video_url`,
               `gmt_create`,
               `gmt_modified`,
               `dispose`,
               `remark`,
               `warning_lv`,
               `ignore`,
               `finish`,
               `end_time`
        from early_warning
    </sql>

    <select id="shipTypeStatement" resultType="java.util.Map">
        SELECT CASE
        WHEN ship_type BETWEEN 100 AND 199 THEN '客船'
        WHEN ship_type BETWEEN 200 AND 299 THEN '货船'
        WHEN ship_type BETWEEN 300 AND 399 THEN '液化船'
        WHEN ship_type BETWEEN 400 AND 499 THEN '工作船'
        WHEN ship_type BETWEEN 500 AND 599 THEN '作业船'
        WHEN ship_type BETWEEN 600 AND 699 THEN '拖船'
        ELSE '其他'
        END AS category,
        COUNT(*) AS count
        FROM early_warning
        WHERE
        DATE_FORMAT(gmt_create
        , '%Y-%m-%d') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE()
        , INTERVAL #{days} DAY)
        , '%Y-%m-%d')
        AND DATE_FORMAT(CURDATE()
        , '%Y-%m-%d')
        GROUP BY category;
    </select>

    <select id="earlyWarningStatement" resultType="java.util.Map">
        SELECT event_content category,
        COUNT(*) AS count
        FROM early_warning
        WHERE
        DATE_FORMAT(gmt_create
        , '%Y-%m-%d') BETWEEN DATE_FORMAT(DATE_SUB(CURDATE()
        , INTERVAL #{days} DAY)
        , '%Y-%m-%d')
        AND DATE_FORMAT(CURDATE()
        , '%Y-%m-%d')
        GROUP BY category;
    </select>
</mapper>
