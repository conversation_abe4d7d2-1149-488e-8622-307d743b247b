<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.EarlyWarningRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="EarlyWarningRuleResult" type="com.xx.web.domain.entity.EarlyWarningRule">
                <id column="id" property="id"/>
                <result column="name" property="name"/>
                <result column="auto_exit" property="autoExit"/>
                <result column="mix_speed" property="mixSpeed"/>
                <result column="max_speed" property="maxSpeed"/>
                <result column="duration" property="duration"/>
                <result column="gmt_create" property="gmtCreate"/>
                <result column="gmt_modified" property="gmtModified"/>
                <result column="env_id" property="envId"/>
                <result column="env_type" property="envType"/>
                <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectEarlyWarningRuleVo">
        select `id`, `name`, `auto_exit`, `mix_speed`, `max_speed`, `duration`, `gmt_create`, `gmt_modified`, `env_id`, `env_type`, `status` from early_warning_rule
    </sql>

</mapper>
