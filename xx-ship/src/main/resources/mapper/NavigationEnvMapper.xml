<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.NavigationEnvMapper">


    <select id="getEnvList" resultType="com.xx.web.domain.vo.InnerNavigationEnvVo">
        SELECT et.`env_key`, e.id, e.name, e.env_type, e.`regions`
        FROM navigation_env_type et
                 JOIN navigation_env e ON et.`id` = e.`env_type_id` join early_warning_rule r on r.env_id = e.id
        WHERE et.`env_key` IN ('one_warning', 'two_warning', 'three_warning') AND r.`status` = 1 and e.del = 0 and et.del = 0 ORDER BY env_type
    </select>


    <select id="getHarborlist" resultType="com.xx.web.domain.vo.InnerNavigationEnvVo">
        SELECT e.* FROM navigation_env_type et JOIN  navigation_env e ON et.`id` = e.`env_type_id`
        WHERE et.`env_key` ='harbor' AND e.del = 0
    </select>
</mapper>
