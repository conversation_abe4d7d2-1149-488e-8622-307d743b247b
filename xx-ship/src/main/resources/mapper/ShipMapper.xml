<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.ShipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ShipResult" type="com.xx.web.domain.entity.Ship">
                <result column="id" property="id"/>
                <result column="name" property="name"/>
                <result column="time" property="time"/>
                <result column="version" property="version"/>
                <result column="lon" property="lon"/>
                <result column="lat" property="lat"/>
                <result column="sog" property="sog"/>
                <result column="cog" property="cog"/>
                <result column="rot" property="rot"/>
                <result column="heading" property="heading"/>
                <result column="mmsi" property="mmsi"/>
                <result column="imo" property="imo"/>
                <result column="callsign" property="callsign"/>
                <result column="english_name" property="englishName"/>
                <result column="chinese_name" property="chineseName"/>
                <result column="ship_type" property="shipType"/>
                <result column="width" property="width"/>
                <result column="length" property="length"/>
                <result column="to_bow" property="toBow"/>
                <result column="to_stern" property="toStern"/>
                <result column="to_port" property="toPort"/>
                <result column="to_starboard" property="toStarboard"/>
                <result column="ais_ship_type" property="aisShipType"/>
                <result column="file_ship_type" property="fileShipType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectShipVo">
        select `ship_id`, `id`, `name`, `time`, `version`, `lon`, `lat`, `sog`, `cog`, `rot`, `heading`,
        `mmsi`, `imo`, `callsign`, `english_name`, `chinese_name`, `ship_type`, `width`, `length`, `to_bow`, `to_stern`,
        `to_port`, `to_starboard`, `ais_ship_type`, `file_ship_type` from ship
    </sql>

    <select id="statistics" resultType="java.util.Map">
        SELECT ELT(INTERVAL(`length`,1,50,100,150,200,300),"1:50以内","2:50-100","3:100-150","4:150-200","5:200-300","6:300+") AS category ,COUNT(*) num
        FROM ship WHERE LENGTH != 0 GROUP BY category order by category;
    </select>
</mapper>
