<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.KeyShipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="KeyShipResult" type="com.xx.web.domain.entity.KeyShip">
                <id column="id" property="id"/>
                <result column="ship_id" property="shipId"/>
                <result column="mmsi" property="mmsi"/>
                <result column="english_name" property="englishName"/>
                <result column="chinese_name" property="chineseName"/>
                <result column="remark" property="remark"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectKeyShipVo">
        select `id`, `ship_id`, `mmsi`, `english_name`, `chinese_name`, `remark` from key_ship
    </sql>

</mapper>
