<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.EnvShipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="EnvShipResult" type="com.xx.web.domain.entity.EnvShip">
                <id column="id" property="id"/>
                <result column="ship_id" property="shipId"/>
                <result column="env_id" property="envId"/>
                <result column="env_type" property="envType"/>
                <result column="mmsi" property="mmsi"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectEnvShipVo">
        select `id`, `ship_id`, `env_id`, `env_type`, `mmsi` from env_ship
    </sql>

</mapper>
