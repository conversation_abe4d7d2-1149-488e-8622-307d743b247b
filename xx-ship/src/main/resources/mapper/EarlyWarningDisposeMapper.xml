<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.EarlyWarningDisposeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="EarlyWarningDisposeResult" type="com.xx.web.domain.entity.EarlyWarningDispose">
                <id column="id" property="id"/>
                <result column="early_id" property="earlyId"/>
                <result column="excepition_type" property="excepitionType"/>
                <result column="handling_way" property="handlingWay"/>
                <result column="detail" property="detail"/>
                <result column="treatment_measure" property="treatmentMeasure"/>
                <result column="correct_the_situation" property="correctTheSituation"/>
                <result column="remark" property="remark"/>
                <result column="punish" property="punish"/>
                <result column="accessory" property="accessory"/>
                <result column="gmt_create" property="gmtCreate"/>
                <result column="gmt_modified" property="gmtModified"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectEarlyWarningDisposeVo">
        select `id`, `early_id`, `excepition_type`, `handling_way`, `detail`, `treatment_measure`, `correct_the_situation`, `remark`, `punish`, `accessory`, `gmt_create`, 
        `gmt_modified` from early_warning_dispose
    </sql>

</mapper>
