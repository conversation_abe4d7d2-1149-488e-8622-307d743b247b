<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.ShipTrackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ShipTrackResult" type="com.xx.web.domain.entity.ShipTrack">
                <id column="track_id" property="trackId"/>
                <result column="id" property="id"/>
                <result column="name" property="name"/>
                <result column="time" property="time"/>
                <result column="version" property="version"/>
                <result column="lon" property="lon"/>
                <result column="lat" property="lat"/>
                <result column="sog" property="sog"/>
                <result column="cog" property="cog"/>
                <result column="heading" property="heading"/>
                <result column="english_name" property="englishName"/>
                <result column="timeout" property="timeout"/>
                <result column="alarms" property="alarms"/>
                <result column="region" property="region"/>
                <result column="file_id" property="fileId"/>
                <result column="ship_no" property="shipNo"/>
                <result column="tags" property="tags"/>
                <result column="track" property="track"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectShipTrackVo">
        select `track_id`, `id`, `name`, `time`, `version`, `lon`, `lat`, `sog`, `cog`, `heading`, `english_name`,
        `timeout`, `alarms`, `region`, `file_id`, `ship_no`, `tags`, `track` from ship_track
    </sql>

    <select id="trackList" parameterType="ShipTrackDto" resultMap="ShipTrackResult">
        SELECT t.* FROM (SELECT `track_id`,
        `id`,
        `name`,
        `time`,
        `version`,
        `lon`,
        `lat`,
        `sog`,
        `cog`,
        `heading`,
        `english_name`,
        `timeout`,
        `track`,
        region,
        file_id,
        tags,
        alarms,
        ship_no,
        ROW_NUMBER() OVER(PARTITION BY id ORDER BY `time` DESC ) AS rn
        FROM ship_track
        <where>
            <!--             (lon between #{minLon} and #{maxLon}) and (lat between #{minLat} and #{maxLat}) -->
            <if test="trackId != null">
                and trackId = #{trackId}
            </if>
            <if test="name != null  and name != ''">
                and `name` like concat(#{name}, '%')
            </if>
            <if test="id != null  and id != ''">
                and `id` = #{id}
            </if>
        </where>
        ) t
        WHERE t.rn &lt; 2
    </select>
</mapper>
