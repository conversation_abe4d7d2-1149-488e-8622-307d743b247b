<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.HarborLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="HarborLogResult" type="com.xx.web.domain.entity.HarborLog">
                <id column="id" property="id"/>
                <result column="ship_id" property="shipId"/>
                <result column="mmsi" property="mmsi"/>
                <result column="type" property="type"/>
                <result column="env_id" property="envId"/>
                <result column="env_type" property="envType"/>
                <result column="gmt_create" property="gmtCreate"/>
                <result column="gmt_modified" property="gmtModified"/>
                <result column="remark" property="remark"/>
                <result column="lon" property="lon"/>
                <result column="lat" property="lat"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectHarborLogVo">
        select `id`, `ship_id`, `mmsi`, `type`, `env_id`, `env_type`, `gmt_create`, `gmt_modified`, `remark`, `lon`, `lat` from harbor_log
    </sql>

</mapper>
