<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xx.web.mapper.ZlShipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="ZlShipResult" type="com.xx.web.domain.entity.ZlShip">
                <id column="id" property="id"/>
                <result column="imo" property="imo"/>
                <result column="mmsi" property="mmsi"/>
                <result column="ship_no" property="shipNo"/>
                <result column="ship_id" property="shipId"/>
                <result column="registration_no" property="registrationNo"/>
                <result column="initial_registration_no" property="initialRegistrationNo"/>
                <result column="ship_survey_no" property="shipSurveyNo"/>
                <result column="cardbook" property="cardbook"/>
                <result column="icno" property="icno"/>
                <result column="callsign" property="callsign"/>
                <result column="local_name" property="localName"/>
                <result column="ship_name_en" property="shipNameEn"/>
                <result column="former_name1" property="formerName1"/>
                <result column="former_name2" property="formerName2"/>
                <result column="former_name3" property="formerName3"/>
                <result column="status_code" property="statusCode"/>
                <result column="ship_type_code" property="shipTypeCode"/>
                <result column="flag_code" property="flagCode"/>
                <result column="registration_port" property="registrationPort"/>
                <result column="inland_ship_mark" property="inlandShipMark"/>
                <result column="build_date" property="buildDate"/>
                <result column="shipyard" property="shipyard"/>
                <result column="owner" property="owner"/>
                <result column="contact_no" property="contactNo"/>
                <result column="operator" property="operator"/>
                <result column="classification_code" property="classificationCode"/>
                <result column="max_speed" property="maxSpeed"/>
                <result column="loa" property="loa"/>
                <result column="lbp" property="lbp"/>
                <result column="depth" property="depth"/>
                <result column="bm" property="bm"/>
                <result column="draught" property="draught"/>
                <result column="height" property="height"/>
                <result column="gross" property="gross"/>
                <result column="net" property="net"/>
                <result column="dwt" property="dwt"/>
                <result column="holds" property="holds"/>
                <result column="hatch" property="hatch"/>
                <result column="min_freeboard" property="minFreeboard"/>
                <result column="wind_loading" property="windLoading"/>
                <result column="slot" property="slot"/>
                <result column="carport" property="carport"/>
                <result column="passenger_spaces" property="passengerSpaces"/>
                <result column="min_safe_manning_no" property="minSafeManningNo"/>
                <result column="max_survival_equipment_no" property="maxSurvivalEquipmentNo"/>
                <result column="hull_material_code" property="hullMaterialCode"/>
                <result column="propeller_type" property="propellerType"/>
                <result column="power" property="power"/>
                <result column="rpm" property="rpm"/>
                <result column="build_place" property="buildPlace"/>
                <result column="power_type" property="powerType"/>
                <result column="power_no" property="powerNo"/>
                <result column="power_bore_no" property="powerBoreNo"/>
                <result column="cylinder_bore" property="cylinderBore"/>
                <result column="power_itinerary" property="powerItinerary"/>
                <result column="decks" property="decks"/>
                <result column="ballast" property="ballast"/>
                <result column="auxiliary_power" property="auxiliaryPower"/>
                <result column="power_class" property="powerClass"/>
                <result column="data_source" property="dataSource"/>
                <result column="last_update_time" property="lastUpdateTime"/>
                <result column="del" property="del"/>
                <result column="row_ver" property="rowVer"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectZlShipVo">
        select `id`, `imo`, `mmsi`, `ship_no`, `ship_id`, `registration_no`, `initial_registration_no`, `ship_survey_no`, `cardbook`, `icno`, `callsign`,
        `local_name`, `ship_name_en`, `former_name1`, `former_name2`, `former_name3`, `status_code`, `ship_type_code`, `flag_code`, `registration_port`, `inland_ship_mark`,
        `build_date`, `shipyard`, `owner`, `contact_no`, `operator`, `classification_code`, `max_speed`, `loa`, `lbp`, `depth`,
        `bm`, `draught`, `height`, `gross`, `net`, `dwt`, `holds`, `hatch`, `min_freeboard`, `wind_loading`,
        `slot`, `carport`, `passenger_spaces`, `min_safe_manning_no`, `max_survival_equipment_no`, `hull_material_code`, `propeller_type`, `power`, `rpm`, `build_place`,
        `power_type`, `power_no`, `power_bore_no`, `cylinder_bore`, `power_itinerary`, `decks`, `ballast`, `auxiliary_power`, `power_class`, `data_source`,
        `last_update_time`, `del`, `row_ver` from zl_ship
    </sql>

</mapper>
