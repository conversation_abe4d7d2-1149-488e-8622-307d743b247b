package com.xx.web.task;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/25 23:04
 * @version: 1.0
 */
//@Component
//@RequiredArgsConstructor
public class EarlyWarningTask {

    //private final IEarlyWarningService warningService;

    //@Scheduled(cron = "0 0 */15 * * ?")// 每15分钟执行任务
/*    public void deleteOldData() {
        LocalDateTime currentDate = LocalDateTime.now();
        //LocalDate lastMonthFirstDay = LocalDate.of(currentDate.getYear(), currentDate.getMonth().minus(1), 1);
        long second = currentDate.toEpochSecond(ZoneOffset.of("+8"));

        warningService.update(Wrappers.<EarlyWarning>lambdaUpdate().set(EarlyWarning::getDispose, true).set(EarlyWarning::getDisposeTime, LocalDateTime.now()).eq(EarlyWarning::getDispose, false).le(EarlyWarning::getEndTime, second));
    }*/
}
