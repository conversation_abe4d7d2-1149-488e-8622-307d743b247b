package com.xx.web.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.xx.mp.mapper.XxBaseMapper;
import com.xx.web.domain.entity.ZlShip;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 船舶档案 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-11
 */
public interface ZlShipMapper extends XxBaseMapper<ZlShip> {

    String countByMMSI = "SELECT COUNT(mmsi) FROM zl_ship ${ew.customSqlSegment}";

    String wrapperSql = "select * from (" + countByMMSI + " ) as f ${ew.customSqlSegment}";

    @Select(countByMMSI)
    Long countByMMSI(@Param("ew") Wrapper queryWrapper);


}
