package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.dto.ShipTrackDto;
import com.xx.web.domain.entity.ShipTrack;
import com.xx.web.service.IShipTrackService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
//@Api(tags = "")
@RequiredArgsConstructor
/*@RestController
@RequestMapping("/nc/ship-track")*/
public class ShipTrackController extends MPBaseController {

    private final IShipTrackService shipTrackService;

    @ApiOperation(value = "列表", response = ShipTrack.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(ShipTrack shipTrack) {
        IPage<ShipTrack> data = shipTrackService.page(shipTrack);
        return getDataTable(data);
    }

    @ApiOperation(value = "航迹")
    @PostMapping(value = "/track/list")
    public R trackList(@RequestBody ShipTrackDto shipTrack) {
        List<ShipTrack> data = shipTrackService.trackList(shipTrack);
        return R.ok(data);
    }

    @ApiOperation(value = "详情", response = ShipTrack.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        ShipTrack data = shipTrackService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @PostMapping
    public R add(@Validated @RequestBody ShipTrack shipTrack) {
        return R.trBool(shipTrackService.add(shipTrack));
    }

    @ApiOperation(value = "修改")
    @PutMapping
    public R modify(@Validated @RequestBody ShipTrack shipTrack) {
        return R.trBool(shipTrackService.modify(shipTrack));
    }

    @ApiOperation(value = "删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(shipTrackService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(shipTrackService.removes(ids));
    }

}

