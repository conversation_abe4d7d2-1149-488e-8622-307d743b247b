package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.PlotType;
import com.xx.web.service.IPlotTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 标绘类型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Api(tags = "标绘类型")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/plot-type")
public class PlotTypeController extends MPBaseController{

    private final IPlotTypeService plotTypeService;

    @ApiOperation(value = "标绘类型列表", response = PlotType.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(PlotType plotType) {
        IPage<PlotType> data = plotTypeService.page(plotType);
        return getDataTable(data);
    }

    @ApiOperation(value = "标绘类型详情", response = PlotType.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        PlotType data = plotTypeService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "标绘类型新增")
    @Log(title = "标绘类型", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody PlotType plotType) {
        return R.trBool(plotTypeService.add(plotType));
    }

    @ApiOperation(value = "标绘类型修改")
    @Log(title = "标绘类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated @RequestBody PlotType plotType) {
        return R.trBool(plotTypeService.modify(plotType));
    }

    @ApiOperation(value = "标绘类型删除(单个条目)")
    @Log(title = "标绘类型", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(plotTypeService.remove(id));
    }

    @ApiOperation(value = "标绘类型删除(多个条目)")
    @Log(title = "标绘类型", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(plotTypeService.removes(ids));
    }

}

