package com.xx.web.controller;

import com.xx.web.domain.MgTrack;
import com.xx.web.domain.base.PageReq;
import com.xx.web.domain.base.PageResult;
import com.xx.web.domain.vo.DeviceVO;
import com.xx.web.service.MgTrackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 11:09
 * @version: 1.0
 */
@Api(tags = "航迹查询")
@RequiredArgsConstructor
@RestController
@RequestMapping("/mgTrack")
public class MgTrackController {

    private final MgTrackService mgTrackService;
    @ApiOperation(value = "分页查询航迹", response = DeviceVO.class)
    @GetMapping(value = "/page")
    public PageResult<MgTrack> page(MgTrack queryObject, Long startTime, Long endTime, PageReq pageReq) {
        return mgTrackService.dynamicQuery( queryObject,  startTime,  endTime, pageReq);
    }
}
