package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.interfaces.validatedGroups.Insert;
import com.xx.interfaces.validatedGroups.Update;
import com.xx.web.domain.entity.ZlShip;
import com.xx.web.service.IZlShipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 船舶档案 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
*/
@Api(tags = "船舶档案")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/shipInfo")
public class ZlShipController extends MPBaseController{

    private final IZlShipService zlShipService;

    @ApiOperation(value = "船舶档案列表", response = ZlShip.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(ZlShip zlShip) {
        IPage<ZlShip> data = zlShipService.page(zlShip);
        return getDataTable(data);
    }

    @ApiOperation(value = "船舶档案详情", response = ZlShip.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        ZlShip data = zlShipService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "通过mmsi查询船舶档案详情", response = ZlShip.class)
    @PostMapping(value = "/byMMSI/{mmsi}")
    public R infoByMMSI(@PathVariable Integer mmsi) {
        if ("0".equals(mmsi)) {
            return R.ok();
        }
        ZlShip data = zlShipService.infoByMMSI(mmsi);
        return R.ok(data);
    }

    @ApiOperation(value = "通过mmsi查询船舶档案是否存在")
    @PostMapping(value = "/existByMMSI/{mmsi}")
    public R existByMMSI(@PathVariable Integer mmsi) {
        if ("0".equals(mmsi)) {
            return R.ok(false);
        }
        return R.ok(zlShipService.countByMMSI(mmsi) > 0);
    }
    @ApiOperation(value = "船舶档案新增")
    @PostMapping
    public R add(@Validated(Insert.class) @RequestBody ZlShip zlShip) {
        return R.trBool(zlShipService.add(zlShip));
    }

    @ApiOperation(value = "船舶档案修改")
    @PutMapping
    public R modify(@Validated(Update.class) @RequestBody ZlShip zlShip) {
        return R.trBool(zlShipService.modify(zlShip));
    }

    @ApiOperation(value = "船舶档案删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(zlShipService.remove(id));
    }

    @ApiOperation(value = "船舶档案删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(zlShipService.removes(ids));
    }

}


