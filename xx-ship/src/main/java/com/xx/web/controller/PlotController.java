package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.Plot;
import com.xx.web.service.IPlotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 标绘 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Api(tags = "标绘")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/plot")
public class PlotController extends MPBaseController{

    private final IPlotService plotService;

    @ApiOperation(value = "标绘列表", response = Plot.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(Plot plot) {
        IPage<Plot> data = plotService.page(plot);
        return getDataTable(data);
    }

    @ApiOperation(value = "标绘详情", response = Plot.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        Plot data = plotService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "标绘新增")
    @Log(title = "标绘", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody Plot plot) {
        return R.trBool(plotService.add(plot));
    }

    @ApiOperation(value = "标绘修改")
    @Log(title = "标绘", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated @RequestBody Plot plot) {
        return R.trBool(plotService.modify(plot));
    }

    @ApiOperation(value = "标绘删除(单个条目)")
    @Log(title = "标绘", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        boolean remove = plotService.remove(id);
        return R.trBool(remove);
    }

    @ApiOperation(value = "标绘删除(多个条目)")
    @Log(title = "标绘", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(plotService.removes(ids));
    }

}

