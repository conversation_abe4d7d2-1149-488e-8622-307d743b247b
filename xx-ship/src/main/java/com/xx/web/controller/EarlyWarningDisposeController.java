package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.interfaces.validatedGroups.Insert;
import com.xx.interfaces.validatedGroups.Update;
import com.xx.web.domain.entity.EarlyWarningDispose;
import com.xx.web.service.IEarlyWarningDisposeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Api(tags = "预警信息处理")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/early-warning-dispose")
public class EarlyWarningDisposeController extends MPBaseController{

    private final IEarlyWarningDisposeService earlyWarningDisposeService;

    @ApiOperation(value = "列表", response = EarlyWarningDispose.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(EarlyWarningDispose earlyWarningDispose) {
        IPage<EarlyWarningDispose> data = earlyWarningDisposeService.page(earlyWarningDispose);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = EarlyWarningDispose.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        EarlyWarningDispose data = earlyWarningDisposeService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @Log(title = "", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated(Insert.class) @RequestBody EarlyWarningDispose earlyWarningDispose) {
        return R.trBool(earlyWarningDisposeService.add(earlyWarningDispose));
    }

    @ApiOperation(value = "修改")
    @Log(title = "", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated(Update.class) @RequestBody EarlyWarningDispose earlyWarningDispose) {
        return R.trBool(earlyWarningDisposeService.modify(earlyWarningDispose));
    }

    @ApiOperation(value = "删除(单个条目)")
    @Log(title = "", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(earlyWarningDisposeService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @Log(title = "", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(earlyWarningDisposeService.removes(ids));
    }

}

