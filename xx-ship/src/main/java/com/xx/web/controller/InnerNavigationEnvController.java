package com.xx.web.controller;

import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.NavigationEnv;
import com.xx.web.domain.vo.InnerNavigationEnvVo;
import com.xx.web.service.INavigationEnvService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 通航环境 内部控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
*/
@Api(tags = "通航环境")
@RequiredArgsConstructor
@RestController
@RequestMapping("/inner/navigation-env")
public class InnerNavigationEnvController extends MPBaseController{

    private final INavigationEnvService navigationEnvService;

    @ApiOperation(value = "通航环境预警列表", response = NavigationEnv.class)
    @GetMapping(value = "/getEnvAndRulelist")
    public List<InnerNavigationEnvVo> getEnvAndRulelist(HttpServletRequest request) {
        System.out.println(request.getHeader("token"));
        if (!"Ot70Q^Nv?Hg&JMgyrRfu#dLd&K$-yCFf".equals(request.getHeader("token"))) {
            return new ArrayList<>();
        }

        List<InnerNavigationEnvVo> data = navigationEnvService.getEnvAndRulelist();
        return data;
    }

/*    @ApiOperation(value = "通航环境港口列表", response = NavigationEnv.class)
    @GetMapping(value = "/getHarborlist")
    public List<InnerNavigationEnvVo> getHarborlist(HttpServletRequest request) {
        System.out.println(request.getHeader("token"));
        if (!"Ot70Q^Nv?Hg&JMgyrRfu#dLd&K$-yCFf".equals(request.getHeader("token"))) {
            return new ArrayList<>();
        }

        List<InnerNavigationEnvVo> data = navigationEnvService.getHarborlist();
        return data;
    }*/
}

