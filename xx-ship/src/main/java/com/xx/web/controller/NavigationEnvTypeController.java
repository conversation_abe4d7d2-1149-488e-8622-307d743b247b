package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.NavigationEnvType;
import com.xx.web.service.INavigationEnvTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 通航环境类型 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
*/
@Api(tags = "通航环境类型")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/navigation-env-type")
public class NavigationEnvTypeController extends MPBaseController{

    private final INavigationEnvTypeService navigationEnvTypeService;

    @ApiOperation(value = "通航环境类型列表", response = NavigationEnvType.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(NavigationEnvType navigationEnvType) {
        IPage<NavigationEnvType> data = navigationEnvTypeService.page(navigationEnvType);
        return getDataTable(data);
    }

    @ApiOperation(value = "通航环境类型详情", response = NavigationEnvType.class)
    @GetMapping(value = "/warning/list")
    public R list() {
        List<NavigationEnvType> data = navigationEnvTypeService.warningList();
        return R.ok(data);
    }
    @ApiOperation(value = "通航环境类型详情", response = NavigationEnvType.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        NavigationEnvType data = navigationEnvTypeService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "通航环境类型新增")
    @PostMapping
    public R add(@Validated @RequestBody NavigationEnvType navigationEnvType) {
        return R.trBool(navigationEnvTypeService.add(navigationEnvType));
    }

    @ApiOperation(value = "通航环境类型修改")
    @PutMapping
    public R modify(@Validated @RequestBody NavigationEnvType navigationEnvType) {
        return R.trBool(navigationEnvTypeService.modify(navigationEnvType));
    }

    @ApiOperation(value = "通航环境类型删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(navigationEnvTypeService.remove(id));
    }

    @ApiOperation(value = "通航环境类型删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(navigationEnvTypeService.removes(ids));
    }

}

