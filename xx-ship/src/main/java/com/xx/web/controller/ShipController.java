package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.RepeatSubmit;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.utils.poi.ExcelUtil;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.dto.ShipDto;
import com.xx.web.domain.entity.Ship;
import com.xx.web.domain.entity.ShipExcel;
import com.xx.web.service.IShipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * ship 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
*/
@Api(tags = "ship")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/ship")
    public class ShipController extends MPBaseController{

    private final IShipService shipService;

    @ApiOperation(value = "ship列表", response = Ship.class)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
        @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
        @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
        @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(ShipDto ship) {
        IPage<Ship> data = shipService.page(ship);
        return getDataTable(data);
    }

    @ApiOperation("长度统计")
    @GetMapping("/statistics")
    public R statistics() {
        return R.ok(shipService.statistics());
    }
    @ApiOperation(value = "ship详情", response = Ship.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        Ship data = shipService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "ship新增")
    @PostMapping
    public R add(@Validated @RequestBody Ship ship) {
        return R.trBool(shipService.add(ship));
    }

    @ApiOperation(value = "ship修改")
    @PutMapping
    public R modify(@Validated @RequestBody Ship ship) {
        return R.trBool(shipService.modify(ship));
    }

    @ApiOperation(value = "ship删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(shipService.remove(id));
    }

    @ApiOperation(value = "ship删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(shipService.removes(ids));
    }

    @RepeatSubmit
    @PostMapping("/importData")
    public R importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<ShipExcel> util = new ExcelUtil<>(ShipExcel.class);
        List<ShipExcel> list = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = shipService.importData(list, updateSupport, operName);
        return R.ok(message);
    }

}

