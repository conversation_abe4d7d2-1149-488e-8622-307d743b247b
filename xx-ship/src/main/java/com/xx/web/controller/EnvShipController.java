package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.EnvShip;
import com.xx.web.service.IEnvShipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 在港,锚地等船舶 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Api(tags = "在港,锚地等船舶")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/env-ship")
public class EnvShipController extends MPBaseController{

    private final IEnvShipService envShipService;

    @ApiOperation(value = "在港,锚地等船舶列表", response = EnvShip.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(EnvShip envShip) {
        IPage<EnvShip> data = envShipService.page(envShip);
        return getDataTable(data);
    }

    @ApiOperation(value = "在港,锚地等船舶详情", response = EnvShip.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        EnvShip data = envShipService.info(id);
        return R.ok(data);
    }



    @ApiOperation(value = "在港,锚地等船舶新增")
    @Log(title = "在港,锚地等船舶", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody EnvShip envShip) {
        return R.trBool(envShipService.add(envShip));
    }

    @ApiOperation(value = "在港,锚地等船舶修改")
    @Log(title = "在港,锚地等船舶", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated @RequestBody EnvShip envShip) {
        return R.trBool(envShipService.modify(envShip));
    }

    @ApiOperation(value = "在港,锚地等船舶删除(单个条目)")
    @Log(title = "在港,锚地等船舶", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(envShipService.remove(id));
    }

    @ApiOperation(value = "在港,锚地等船舶删除(多个条目)")
    @Log(title = "在港,锚地等船舶", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(envShipService.removes(ids));
    }

}

