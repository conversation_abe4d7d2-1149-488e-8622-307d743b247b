package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.NavigationEnv;
import com.xx.web.service.INavigationEnvService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 通航环境 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
*/
@Api(tags = "通航环境")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/navigation-env")
public class NavigationEnvController extends MPBaseController{

    private final INavigationEnvService navigationEnvService;

    @ApiOperation(value = "通航环境列表", response = NavigationEnv.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(NavigationEnv navigationEnv) {
        IPage<NavigationEnv> data = navigationEnvService.page(navigationEnv);
        return getDataTable(data);
    }

    @ApiOperation(value = "通航环境类型列表", response = NavigationEnv.class)
    @GetMapping(value = "/envTypeList")
    public R info() {
        return R.ok(navigationEnvService.envList());
    }

    @ApiOperation(value = "通航环境详情", response = NavigationEnv.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        NavigationEnv data = navigationEnvService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "通航环境新增")
    @PostMapping
    public R add(@Validated @RequestBody NavigationEnv navigationEnv) {
        return R.trBool(navigationEnvService.add(navigationEnv));
    }

    @ApiOperation(value = "通航环境修改")
    @PutMapping
    public R modify(@Validated @RequestBody NavigationEnv navigationEnv) {
        return R.trBool(navigationEnvService.modify(navigationEnv));
    }

    @ApiOperation(value = "通航环境删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(navigationEnvService.remove(id));
    }

    @ApiOperation(value = "通航环境删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(navigationEnvService.removes(ids));
    }

}

