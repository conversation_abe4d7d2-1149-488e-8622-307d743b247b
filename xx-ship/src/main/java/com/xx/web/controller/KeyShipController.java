package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.KeyShip;
import com.xx.web.service.IKeyShipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
*/
@Api(tags = "重点船")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/key-ship")
    public class KeyShipController extends MPBaseController{

    private final IKeyShipService keyShipService;

    @ApiOperation(value = "列表", response = KeyShip.class)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
        @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
        @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
        @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(KeyShip keyShip) {
        IPage<KeyShip> data = keyShipService.page(keyShip);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = KeyShip.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        KeyShip data = keyShipService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @PostMapping("/add/{shipId}")
    public R add(@PathVariable("shipId") String shipId,String remark) {
        return R.trBool(keyShipService.add(shipId,remark));
    }

    @ApiOperation(value = "修改")
    @PutMapping
    public R modify(@Validated @RequestBody KeyShip keyShip) {
        return R.trBool(keyShipService.modify(keyShip));
    }

    @ApiOperation(value = "删除(单个条目)")
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(keyShipService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(keyShipService.removes(ids));
    }

}

