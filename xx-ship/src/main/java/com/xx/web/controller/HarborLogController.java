package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.entity.HarborLog;
import com.xx.web.service.IHarborLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Api(tags = "")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/harbor-log")
public class HarborLogController extends MPBaseController{

    private final IHarborLogService harborLogService;

    @ApiOperation(value = "列表", response = HarborLog.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(HarborLog harborLog) {
        IPage<HarborLog> data = harborLogService.page(harborLog);
        return getDataTable(data);
    }

    @ApiOperation(value = "详情", response = HarborLog.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        HarborLog data = harborLogService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "新增")
    @Log(title = "", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody HarborLog harborLog) {
        return R.trBool(harborLogService.add(harborLog));
    }

    @ApiOperation(value = "修改")
    @Log(title = "", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated @RequestBody HarborLog harborLog) {
        return R.trBool(harborLogService.modify(harborLog));
    }

    @ApiOperation(value = "删除(单个条目)")
    @Log(title = "", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Long id) {
        return R.trBool(harborLogService.remove(id));
    }

    @ApiOperation(value = "删除(多个条目)")
    @Log(title = "", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Long> ids) {
        return R.trBool(harborLogService.removes(ids));
    }

}

