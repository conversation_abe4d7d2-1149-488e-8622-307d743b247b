package com.xx.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.common.annotation.Log;
import com.xx.common.core.domain.R;
import com.xx.common.core.page.TableDataInfo;
import com.xx.common.enums.BusinessType;
import com.xx.core.controller.MPBaseController;
import com.xx.web.domain.dto.EarlyWarningStatementDto;
import com.xx.web.domain.entity.EarlyWarning;
import com.xx.web.service.IEarlyWarningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 预警信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
*/
@Api(tags = "预警信息")
@RequiredArgsConstructor
@RestController
@RequestMapping("/nc/early-warning")
public class EarlyWarningController extends MPBaseController{

    private final IEarlyWarningService earlyWarningService;

    @ApiOperation(value = "预警信息列表", response = EarlyWarning.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页面", dataType = "Long"),
            @ApiImplicitParam(name = "pageSize", value = "页面数据量", dataType = "Long"),
            @ApiImplicitParam(name = "isAsc", value = "排序方式排序[asc:正序; desc:倒序]", dataType = "String"),
            @ApiImplicitParam(name = "orderByColumn", value = "排序字段,参照返回字段", dataType = "String")})
    @GetMapping(value = "/page")
    public TableDataInfo page(EarlyWarning earlyWarning) {
        IPage<EarlyWarning> data = earlyWarningService.page(earlyWarning);
        return getDataTable(data);
    }

    @ApiOperation(value = "预警信息报表", response = EarlyWarning.class)
    @PostMapping(value = "statement")
    public R statement(@RequestBody EarlyWarningStatementDto dto) {
        return R.ok(earlyWarningService.statement(dto));
    }

    @ApiOperation(value = "预警信息详情", response = EarlyWarning.class)
    @GetMapping(value = "/{id}")
    public R info(@PathVariable Long id) {
        EarlyWarning data = earlyWarningService.info(id);
        return R.ok(data);
    }

    @ApiOperation(value = "预警信息新增")
    @Log(title = "预警信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@Validated @RequestBody EarlyWarning earlyWarning) {
        return R.trBool(earlyWarningService.add(earlyWarning));
    }

    @ApiOperation(value = "预警信息修改")
    @Log(title = "预警信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R modify(@Validated @RequestBody EarlyWarning earlyWarning) {
        return R.trBool(earlyWarningService.modify(earlyWarning));
    }

    @ApiOperation(value = "预警信息删除(单个条目)")
    @Log(title = "预警信息", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/{id}")
    public R remove(@PathVariable Integer id) {
        return R.trBool(earlyWarningService.remove(id));
    }

    @ApiOperation(value = "预警信息删除(多个条目)")
    @Log(title = "预警信息", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/removes")
    public R removes(@RequestBody List<Integer> ids) {
        return R.trBool(earlyWarningService.removes(ids));
    }


    @ApiOperation(value = "预警信息结束忽略")
    @Log(title = "预警信息", businessType = BusinessType.UPDATE)
    @PutMapping("endOrIgnore/{id}/{oper}")
    public R endOrIgnore(@PathVariable Integer id,@PathVariable Byte oper) {
        return R.trBool(earlyWarningService.endOrIgnore(id,oper));
    }
}

