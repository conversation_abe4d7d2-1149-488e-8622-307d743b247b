package com.xx.web.component.convert;

import com.xx.web.domain.dto.DeviceDTO;
import com.xx.web.domain.entity.Device;
import com.xx.web.domain.vo.DeviceVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 转换类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */

@Mapper
public interface DeviceConvert {

    DeviceConvert INSTANCE = Mappers.getMapper(DeviceConvert.class);

    Device toEntity(DeviceDTO dto);

    List<Device> toEntities(List<DeviceDTO> dtos);

    DeviceVO toVO(Device entity);

    List<DeviceVO> toVOS(List<Device> entities);

}
