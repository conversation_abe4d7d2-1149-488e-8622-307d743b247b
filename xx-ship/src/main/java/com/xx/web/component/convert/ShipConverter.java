package com.xx.web.component.convert;

import com.xx.web.domain.base.BaseConvert;
import com.xx.web.domain.entity.ShipRor;
import com.xx.web.proto.ShipOuterClass;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * @description：自定义实体转换接口
 * @author：zhangzhixiang
 * @date：2019/03/07 13:19:23
 */
@Mapper
public interface ShipConverter extends BaseConvert<ShipOuterClass.Ship, ShipRor> {
    ShipConverter INSTANCE = Mappers.getMapper(ShipConverter.class);

    @Mappings({
            @Mapping(source = "mmsi", target = "mmsi"),
            @Mapping(source = "lon", target = "lon"),
            @Mapping(source = "lat", target = "lat"),
            @Mapping(source = "time", target = "triggerTime")
    })
    ShipRor source2Target(ShipOuterClass.Ship source);

}
