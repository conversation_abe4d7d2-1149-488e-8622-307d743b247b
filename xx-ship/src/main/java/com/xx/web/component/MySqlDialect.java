//package com.xx.web.component;
//
//import com.baomidou.mybatisplus.extension.plugins.pagination.dialects.IDialect;
//
//public class MySqlDialect implements IDialect {
//    public String buildCountSql(String originalSql) {
//        String countSql = "SELECT COUNT(mmsi) FROM (" + originalSql + ") tmp";
//        return countSql;
//    }
//
//    @Override
//    public String buildPaginationSql(String originalSql, long offset, long limit) {
//        String limitSql = " LIMIT " + offset + "," + limit;
//        String paginationSql = originalSql + limitSql;
//        return paginationSql;
//    }
//}
