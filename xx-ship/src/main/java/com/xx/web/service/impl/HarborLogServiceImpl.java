package com.xx.web.service.impl;

import com.xx.web.domain.entity.HarborLog;
import com.xx.web.mapper.HarborLogMapper;
import com.xx.web.service.IHarborLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.utils.base.MPageUtils;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Service
@RequiredArgsConstructor
public class HarborLogServiceImpl extends ServiceImpl<HarborLogMapper, HarborLog> implements IHarborLogService {

    private final HarborLogMapper mapper;

/**
* 分页列表
* @param harborLog 根据需要进行传值
* @return
*/
    @Override
    public IPage<HarborLog> page(HarborLog harborLog) {

        QueryWrapper<HarborLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 
            .eq(harborLog.getId() != null, HarborLog::getId, harborLog.getId())
            // 船舶id
            .eq(harborLog.getShipId() != null, HarborLog::getShipId, harborLog.getShipId())
            // mmsi
            .eq(!StringUtils.isEmpty(harborLog.getMmsi()), HarborLog::getMmsi, harborLog.getMmsi())
            // 1进0出
            .eq(harborLog.getType() != null, HarborLog::getType, harborLog.getType())
            // 通航环境id
            .eq(harborLog.getEnvId() != null, HarborLog::getEnvId, harborLog.getEnvId())
            // 通航环境类型id
            .eq(harborLog.getEnvType() != null, HarborLog::getEnvType, harborLog.getEnvType())
            // 创建时间
            .eq(harborLog.getGmtCreate() != null, HarborLog::getGmtCreate, harborLog.getGmtCreate())
            // 更新时间
            .eq(harborLog.getGmtModified() != null, HarborLog::getGmtModified, harborLog.getGmtModified())
            // 
            .eq(!StringUtils.isEmpty(harborLog.getRemark()), HarborLog::getRemark, harborLog.getRemark())
            // 经度
            .eq(harborLog.getLon() != null, HarborLog::getLon, harborLog.getLon())
            // 纬度
            .eq(harborLog.getLat() != null, HarborLog::getLat, harborLog.getLat());

        IPage<HarborLog> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 详情
    * @param id
    * @return
    */
    @Override
    public HarborLog info(Long id) {
        return getById(id);
    }

    /**
    * 新增
    * @param harborLog 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(HarborLog harborLog) {
        return save(harborLog);
    }

    /**
    * 修改
    * @param harborLog 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(HarborLog harborLog) {
        return updateById(harborLog);
    }

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Long id) {
        return removeById(id);
    }

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Long> ids) {
        return removeByIds(ids);
    }

}
