package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.constant.Constants;
import com.xx.utils.base.MPageUtils;
import com.xx.utils.redis.RedisUtil;
import com.xx.web.domain.entity.EarlyWarningRule;
import com.xx.web.mapper.EarlyWarningRuleMapper;
import com.xx.web.service.IEarlyWarningRuleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Service
@RequiredArgsConstructor
public class EarlyWarningRuleServiceImpl extends ServiceImpl<EarlyWarningRuleMapper, EarlyWarningRule> implements IEarlyWarningRuleService {

    private final EarlyWarningRuleMapper mapper;
    private final RedisUtil redisUtil;

    /**
     * 分页列表
     *
     * @param earlyWarningRule 根据需要进行传值
     * @return
     */
    @Override
    public IPage<EarlyWarningRule> page(EarlyWarningRule earlyWarningRule) {

        QueryWrapper<EarlyWarningRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                //
                .eq(earlyWarningRule.getId() != null, EarlyWarningRule::getId, earlyWarningRule.getId())
                // 规则类型
                .eq(!StringUtils.isEmpty(earlyWarningRule.getName()), EarlyWarningRule::getName,
                        earlyWarningRule.getName())
                // 自动结束时长(分)
                .eq(earlyWarningRule.getAutoExit() != null, EarlyWarningRule::getAutoExit,
                        earlyWarningRule.getAutoExit())
                // 最小速度(节)
                .eq(earlyWarningRule.getMixSpeed() != null, EarlyWarningRule::getMixSpeed,
                        earlyWarningRule.getMixSpeed())
                // 最大速度(节)
                .eq(earlyWarningRule.getMaxSpeed() != null, EarlyWarningRule::getMaxSpeed,
                        earlyWarningRule.getMaxSpeed())
                // 持续时间(分)
                .eq(earlyWarningRule.getDuration() != null, EarlyWarningRule::getDuration,
                        earlyWarningRule.getDuration())
                // 创建时间
                .eq(earlyWarningRule.getGmtCreate() != null, EarlyWarningRule::getGmtCreate,
                        earlyWarningRule.getGmtCreate())
                // 更新时间
                .eq(earlyWarningRule.getGmtModified() != null, EarlyWarningRule::getGmtModified,
                        earlyWarningRule.getGmtModified())
                // 通航环境id
                .eq(earlyWarningRule.getEnvId() != null, EarlyWarningRule::getEnvId, earlyWarningRule.getEnvId())
                // 通航类型id
                .eq(earlyWarningRule.getEnvType() != null, EarlyWarningRule::getEnvType, earlyWarningRule.getEnvType())
                // 1启用 0未启用
                .eq(earlyWarningRule.getStatus() != null, EarlyWarningRule::getStatus, earlyWarningRule.getStatus());

        IPage<EarlyWarningRule> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public EarlyWarningRule info(Long id) {
        return getById(id);
    }

    /**
     * 新增
     *
     * @param earlyWarningRule 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(EarlyWarningRule earlyWarningRule) {
        boolean b = save(earlyWarningRule);
        cacheWarningRule(b);
        return b;
    }

    /**
     * 修改
     *
     * @param earlyWarningRule 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(EarlyWarningRule earlyWarningRule) {
        boolean b = updateById(earlyWarningRule);
        cacheWarningRule(b);
        return b;
    }

    /**
     * 删除(单个条目)
     *
     * @param id
     * @return
     */
    @Override
    public boolean remove(Long id) {
        boolean b = removeById(id);
        cacheWarningRule(b);
        return b;
    }

    /**
     * 删除(多个条目)
     *
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Long> ids) {
        boolean b = removeByIds(ids);
        cacheWarningRule(b);
        return b;
    }

    private void cacheWarningRule(boolean b) {
        if (b) {
            redisUtil.del(Constants.WARNING_RULES);
            warningRules();
        }
    }

    @Override
    public void warningRules() {
        List<EarlyWarningRule> warningRules = list(Wrappers
                .<EarlyWarningRule>lambdaQuery().eq(EarlyWarningRule::getStatus, Boolean.TRUE));

        Map<String, EarlyWarningRule> warningRuleMap = warningRules.stream().collect(
                Collectors.toMap(rule -> String.valueOf(rule.getId()),
                        Function.identity()));

        Map<String, Object> objWarningRuleMap = warningRuleMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        redisUtil.hPutAll(Constants.WARNING_RULES, objWarningRuleMap);
    }

}
