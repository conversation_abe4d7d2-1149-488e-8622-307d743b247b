package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.Plot;
import com.xx.web.mapper.PlotMapper;
import com.xx.web.service.IPlotService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 标绘 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Service
@RequiredArgsConstructor
public class PlotServiceImpl extends ServiceImpl<PlotMapper, Plot> implements IPlotService {

    private final PlotMapper mapper;

/**
* 标绘分页列表
* @param plot 根据需要进行传值
* @return
*/
    @Override
    public IPage<Plot> page(Plot plot) {

        QueryWrapper<Plot> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 标绘id
            .eq(plot.getId() != null, Plot::getId, plot.getId())
            // 标绘名称
            .eq(!StringUtils.isEmpty(plot.getName()), Plot::getName, plot.getName())
            // 标绘类型
            .eq(plot.getPlotType() != null, Plot::getPlotType, plot.getPlotType())
            // 状态（1正常 0停用）
            .eq(plot.getStatus() != null, Plot::getStatus, plot.getStatus())
            // 图形类型
            .eq(plot.getGraphType() != null, Plot::getGraphType, plot.getGraphType())
            // 边框类型
            .eq(plot.getBorderType() != null, Plot::getBorderType, plot.getBorderType())
            // 边框颜色
            .eq(!StringUtils.isEmpty(plot.getBorderColor()), Plot::getBorderColor, plot.getBorderColor())
            // 边框宽度
            .eq(plot.getBorderWidth() != null, Plot::getBorderWidth, plot.getBorderWidth())
            // 填充颜色
            .eq(!StringUtils.isEmpty(plot.getFillColor()), Plot::getFillColor, plot.getFillColor())
            // 图标
            .eq(!StringUtils.isEmpty(plot.getIcon()), Plot::getIcon, plot.getIcon())
            // 区域
            .eq(!StringUtils.isEmpty(plot.getRegions()), Plot::getRegions, plot.getRegions())
            // 创建者
            .eq(!StringUtils.isEmpty(plot.getCreateBy()), Plot::getCreateBy, plot.getCreateBy())
            // 创建时间
            .eq(plot.getGmtCreate() != null, Plot::getGmtCreate, plot.getGmtCreate())
            // 更新者
            .eq(!StringUtils.isEmpty(plot.getUpdateBy()), Plot::getUpdateBy, plot.getUpdateBy())
            // 更新时间
            .eq(plot.getGmtModified() != null, Plot::getGmtModified, plot.getGmtModified())
            // 备注
            .eq(!StringUtils.isEmpty(plot.getRemark()), Plot::getRemark, plot.getRemark())
            // 等级
            .eq(plot.getLv() != null, Plot::getLv, plot.getLv())
            // 逻辑删除
            .eq(plot.getDel() != null, Plot::getDel, plot.getDel());

        IPage<Plot> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 标绘详情
    * @param id
    * @return
    */
    @Override
    public Plot info(Long id) {
        return getById(id);
    }

    /**
    * 标绘新增
    * @param plot 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(Plot plot) {
        return save(plot);
    }

    /**
    * 标绘修改
    * @param plot 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(Plot plot) {
        return updateById(plot);
    }

    /**
    * 标绘删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
    * 标绘删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

}
