package com.xx.web.service;

import com.xx.web.domain.entity.Plot;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    * 标绘 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-25
*/
public interface IPlotService extends IService<Plot> {

    /**
    * 标绘分页列表
    * @param plot 根据需要进行传值
    * @return
    */
    IPage<Plot> page(Plot plot);

    /**
    * 标绘详情
    * @param id
    * @return
    */
    Plot info(Long id);

    /**
    * 标绘新增
    * @param plot 根据需要进行传值
    * @return
    */
    boolean add(Plot plot);

    /**
    * 标绘修改
    * @param plot 根据需要进行传值
    * @return
    */
    boolean modify(Plot plot);

    /**
    * 标绘删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
