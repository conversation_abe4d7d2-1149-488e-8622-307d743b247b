package com.xx.web.service;

import com.xx.web.domain.dto.EarlyWarningStatementDto;
import com.xx.web.domain.dto.EarlyWarningTrackDto;
import com.xx.web.domain.dto.EarlyWarningTrackDto2;
import com.xx.web.domain.entity.EarlyWarning;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
* <p>
    * 预警信息 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-26
*/
public interface IEarlyWarningService extends IService<EarlyWarning> {

    /**
    * 预警信息分页列表
    * @param earlyWarning 根据需要进行传值
    * @return
    */
    IPage<EarlyWarning> page(EarlyWarning earlyWarning);

    List<EarlyWarning> pushList();
    /**
    * 预警信息详情
    * @param id
    * @return
    */
    EarlyWarningTrackDto2 info(Long id);

    /**
    * 预警信息新增
    * @param earlyWarning 根据需要进行传值
    * @return
    */
    boolean add(EarlyWarning earlyWarning);

    /**
    * 预警信息修改
    * @param earlyWarning 根据需要进行传值
    * @return
    */
    boolean modify(EarlyWarning earlyWarning);

    /**
    * 预警信息删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);

    List<Map<String,Object>> statement(EarlyWarningStatementDto dto);

    boolean saveEarlyWarningTrack(EarlyWarningTrackDto earlyWarning);

    boolean endOrIgnore(Integer id,Byte oper);
}
