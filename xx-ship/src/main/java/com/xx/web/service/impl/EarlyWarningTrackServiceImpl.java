package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.EarlyWarningTrack;
import com.xx.web.mapper.EarlyWarningTrackMapper;
import com.xx.web.service.IEarlyWarningTrackService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
*/
@Service
@RequiredArgsConstructor
public class EarlyWarningTrackServiceImpl extends MPJBaseServiceImpl<EarlyWarningTrackMapper, EarlyWarningTrack> implements IEarlyWarningTrackService {

    private final EarlyWarningTrackMapper mapper;

/**
* 分页列表
* @param earlyWarningTrack 根据需要进行传值
* @return
*/
    @Override
    public IPage<EarlyWarningTrack> page(EarlyWarningTrack earlyWarningTrack) {

        QueryWrapper<EarlyWarningTrack> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 预警id
            .eq(earlyWarningTrack.getId() != null, EarlyWarningTrack::getId, earlyWarningTrack.getId())
            // 预警航迹
            .eq(!StringUtils.isEmpty(earlyWarningTrack.getTrack()), EarlyWarningTrack::getTrack, earlyWarningTrack.getTrack());

        IPage<EarlyWarningTrack> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 详情
    * @param id
    * @return
    */
    @Override
    public EarlyWarningTrack info(Long id) {
        return getById(id);
    }

    /**
    * 新增
    * @param earlyWarningTrack 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(EarlyWarningTrack earlyWarningTrack) {
        return save(earlyWarningTrack);
    }

    /**
    * 修改
    * @param earlyWarningTrack 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(EarlyWarningTrack earlyWarningTrack) {
        return updateById(earlyWarningTrack);
    }

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

}
