package com.xx.web.service;

import com.xx.web.domain.MgTrack;
import com.xx.web.domain.base.PageReq;
import com.xx.web.domain.base.PageResult;

import java.util.List;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 0:13
 * @version: 1.0
 */
public interface MgTrackService {

    void batchSave(List<MgTrack> list);

    PageResult<MgTrack> dynamicQuery(MgTrack queryObject, Long startTime, Long endTime, PageReq pageReq);

    public List<MgTrack> trackQuery(MgTrack queryObject, Long startTime, Long endTime);
}
