package com.xx.web.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xx.web.domain.dto.ShipDto;
import com.xx.web.domain.entity.Ship;
import com.xx.web.domain.entity.ShipExcel;

import java.util.List;
import java.util.Map;

/**
* <p>
    * ship 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-03-17
*/
public interface IShipService extends IService<Ship> {

    /**
    * ship分页列表
    * @param ship 根据需要进行传值
    * @return
    */
    IPage<Ship> page(ShipDto ship);

    /**
    * ship详情
    * @param id
    * @return
    */
    Ship info(Long id);

    /**
    * ship新增
    * @param ship 根据需要进行传值
    * @return
    */
    boolean add(Ship ship);

    /**
    * ship修改
    * @param ship 根据需要进行传值
    * @return
    */
    boolean modify(Ship ship);

    /**
    * ship删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);

    List<Map<String, Integer>> statistics();

    String importData(List<ShipExcel> list, boolean updateSupport, String operName);
}
