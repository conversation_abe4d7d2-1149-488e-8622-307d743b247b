package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.exception.ServiceException;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.dto.ShipDto;
import com.xx.web.domain.entity.Ship;
import com.xx.web.domain.entity.ShipExcel;
import com.xx.web.mapper.ShipMapper;
import com.xx.web.service.IShipService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * ship 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Service
@RequiredArgsConstructor
public class ShipServiceImpl extends ServiceImpl<ShipMapper, Ship> implements IShipService {

    private final ShipMapper mapper;

    /**
     * ship分页列表
     * @param ship 根据需要进行传值
     * @return
     */
    @Override
    public IPage<Ship> page(ShipDto ship) {

        QueryWrapper<Ship> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // 船名
                .and(!StringUtils.isEmpty(ship.getSearch()),q-> q.like(Ship::getName,ship.getSearch())
                        .or(p -> p.like(Ship::getMmsi, ship.getSearch())).or(k -> k.like(Ship::getCallsign, ship.getSearch())))
                // 唯一标识
                .eq(!StringUtils.isEmpty(ship.getId()), Ship::getId, ship.getId());

        IPage<Ship> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * ship详情
     * @param id
     * @return
     */
    @Override
    public Ship info(Long id) {
        return getById(id);
    }

    /**
     * ship新增
     * @param ship 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(Ship ship) {
        return save(ship);
    }

    /**
     * ship修改
     * @param ship 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(Ship ship) {
        return updateById(ship);
    }

    /**
     * ship删除(单个条目)
     * @param id
     * @return
     */
    @Override
    public boolean remove(Long id) {
        return removeById(id);
    }

    /**
     * ship删除(多个条目)
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Long> ids) {
        return removeByIds(ids);
    }


    @Override
    public List<Map<String, Integer>> statistics() {
        return mapper.statistics();
    }

    @Override
    public String importData(List<ShipExcel> list, boolean updateSupport, String operName) {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("导入的数据不能为空！");
        }

        return null;
    }
}
