package com.xx.web.service;

import com.xx.web.domain.entity.HarborLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-25
*/
public interface IHarborLogService extends IService<HarborLog> {

    /**
    * 分页列表
    * @param harborLog 根据需要进行传值
    * @return
    */
    IPage<HarborLog> page(HarborLog harborLog);

    /**
    * 详情
    * @param id
    * @return
    */
    HarborLog info(Long id);

    /**
    * 新增
    * @param harborLog 根据需要进行传值
    * @return
    */
    boolean add(HarborLog harborLog);

    /**
    * 修改
    * @param harborLog 根据需要进行传值
    * @return
    */
    boolean modify(HarborLog harborLog);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);
        }
