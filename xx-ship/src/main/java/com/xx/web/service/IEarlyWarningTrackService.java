package com.xx.web.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseService;
import com.xx.web.domain.entity.EarlyWarningTrack;

import java.util.List;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-05-31
*/
public interface IEarlyWarningTrackService extends MPJBaseService<EarlyWarningTrack> {

    /**
    * 分页列表
    * @param earlyWarningTrack 根据需要进行传值
    * @return
    */
    IPage<EarlyWarningTrack> page(EarlyWarningTrack earlyWarningTrack);

    /**
    * 详情
    * @param id
    * @return
    */
    EarlyWarningTrack info(Long id);

    /**
    * 新增
    * @param earlyWarningTrack 根据需要进行传值
    * @return
    */
    boolean add(EarlyWarningTrack earlyWarningTrack);

    /**
    * 修改
    * @param earlyWarningTrack 根据需要进行传值
    * @return
    */
    boolean modify(EarlyWarningTrack earlyWarningTrack);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
