package com.xx.web.service;

import com.xx.web.domain.entity.NavigationEnv;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xx.web.domain.vo.InnerNavigationEnvVo;

import java.util.List;

/**
* <p>
    * 通航环境 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-24
*/
public interface INavigationEnvService extends IService<NavigationEnv> {

    /**
    * 通航环境分页列表
    * @param navigationEnv 根据需要进行传值
    * @return
    */
    IPage<NavigationEnv> page(NavigationEnv navigationEnv);

    public List<NavigationEnv> envList();
    /**
    * 通航环境详情
    * @param id
    * @return
    */
    NavigationEnv info(Long id);

    /**
    * 通航环境新增
    * @param navigationEnv 根据需要进行传值
    * @return
    */
    boolean add(NavigationEnv navigationEnv);

    /**
    * 通航环境修改
    * @param navigationEnv 根据需要进行传值
    * @return
    */
    boolean modify(NavigationEnv navigationEnv);

    /**
    * 通航环境删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);

    List<InnerNavigationEnvVo> getEnvAndRulelist();
}
