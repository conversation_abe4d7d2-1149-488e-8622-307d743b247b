package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.utils.SecurityUtils;
import com.xx.common.utils.bean.BeanUtils;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.MgTrack;
import com.xx.web.domain.dto.EarlyWarningStatementDto;
import com.xx.web.domain.dto.EarlyWarningTrackDto;
import com.xx.web.domain.dto.EarlyWarningTrackDto2;
import com.xx.web.domain.entity.EarlyWarning;
import com.xx.web.mapper.EarlyWarningMapper;
import com.xx.web.service.IEarlyWarningDisposeService;
import com.xx.web.service.IEarlyWarningService;
import com.xx.web.service.MgTrackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 预警信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Service
//@RequiredArgsConstructor
public class EarlyWarningServiceImpl extends ServiceImpl<EarlyWarningMapper, EarlyWarning> implements IEarlyWarningService {

    @Autowired
    private EarlyWarningMapper mapper;
    @Autowired
    private MgTrackService mgTrackService;
    @Autowired
    private IEarlyWarningDisposeService warningDisposeService;

    /**
     * 预警信息分页列表
     *
     * @param earlyWarning 根据需要进行传值
     * @return
     */
    @Override
    public IPage<EarlyWarning> page(EarlyWarning earlyWarning) {
        QueryWrapper<EarlyWarning> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                //
                .eq(earlyWarning.getId() != null, EarlyWarning::getId, earlyWarning.getId())
                // mmsi
                .and(!StringUtils.isEmpty(earlyWarning.getShipName()),q -> q.likeRight(EarlyWarning::getMmsi, earlyWarning.getShipName())
                        .or(p -> p.likeRight(EarlyWarning::getShipName,earlyWarning.getShipName())))
  /*              .likeRight(!StringUtils.isEmpty(earlyWarning.getMmsi()), EarlyWarning::getMmsi, earlyWarning.getShipName())
                // 船名
                .likeRight(!StringUtils.isEmpty(earlyWarning.getShipName()), EarlyWarning::getShipName,
                        earlyWarning.getShipName())*/
                // 事件内容
                .eq(!StringUtils.isEmpty(earlyWarning.getEventContent()), EarlyWarning::getEventContent,
                        earlyWarning.getEventContent())
                // 地点
                .eq(!StringUtils.isEmpty(earlyWarning.getAddress()), EarlyWarning::getAddress,
                        earlyWarning.getAddress())
                // 经度
                .eq(earlyWarning.getLon() != null, EarlyWarning::getLon, earlyWarning.getLon())
                // 纬度
                .eq(earlyWarning.getLat() != null, EarlyWarning::getLat, earlyWarning.getLat())
                .eq(earlyWarning.getShipType() != null, EarlyWarning::getShipType, earlyWarning.getShipType())
                // 通航环境id
                .eq(earlyWarning.getEnvId() != null, EarlyWarning::getEnvId, earlyWarning.getEnvId())
                // 通航环境类型id
                .eq(earlyWarning.getEnvType() != null, EarlyWarning::getEnvType, earlyWarning.getEnvType())
                // 视频id
                .eq(!StringUtils.isEmpty(earlyWarning.getVideoUrl()), EarlyWarning::getVideoUrl,
                        earlyWarning.getVideoUrl())
                // 创建时间
                .ge(earlyWarning.getGmtCreate() != null, EarlyWarning::getGmtCreate, earlyWarning.getGmtCreate())
                // 更新时间
                .lt(earlyWarning.getGmtModified() != null, EarlyWarning::getGmtModified, earlyWarning.getGmtModified())
                // 1已处理0未处理
                .eq(earlyWarning.getDispose() != null, EarlyWarning::getDispose, earlyWarning.getDispose())
                // 备注
                .eq(!StringUtils.isEmpty(earlyWarning.getRemark()), EarlyWarning::getRemark, earlyWarning.getRemark())
                // 预警等级
                .eq(!StringUtils.isEmpty(earlyWarning.getWarningLv()), EarlyWarning::getWarningLv,
                        earlyWarning.getWarningLv())
                // 1忽略 0未忽略
                .eq(earlyWarning.getIgnore() != null, EarlyWarning::getIgnore, earlyWarning.getIgnore())
                // 1结束 0未结束
                .eq(earlyWarning.getFinish() != null, EarlyWarning::getFinish, earlyWarning.getFinish())
                // 结束时间
                .eq(earlyWarning.getEndTime() != null, EarlyWarning::getEndTime, earlyWarning.getEndTime())
                .eq(StringUtils.hasLength(earlyWarning.getPlace()), EarlyWarning::getPlace, earlyWarning.getPlace());

        IPage<EarlyWarning> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    @Override
    public List<EarlyWarning> pushList() {
        return list(Wrappers.<EarlyWarning>lambdaQuery().eq(EarlyWarning::getDispose, false)
                .eq(EarlyWarning::getIgnore, false).eq(EarlyWarning::getFinish, false)
                .orderByDesc(EarlyWarning::getId).last("limit 5"));
    }

    /**
     * 预警信息详情
     *
     * @param id
     * @return
     */
    @Override
    public EarlyWarningTrackDto2 info(Long id) {

        EarlyWarning byId = getById(id);
        MgTrack mgTrack = new MgTrack();
        mgTrack.setMmsi(byId.getMmsi());
        long epochSecond = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));

        List<MgTrack> mgTracks = mgTrackService.trackQuery(mgTrack, epochSecond - 1800, epochSecond);
        EarlyWarningTrackDto2 earlyWarningTrackDto2 = new EarlyWarningTrackDto2();
        BeanUtils.copyProperties(byId, earlyWarningTrackDto2);
        List<List<MgTrack>> list = new ArrayList<>();
        list.add(mgTracks);
        earlyWarningTrackDto2.setTrack(list);
        earlyWarningTrackDto2.setWarningDispose(warningDisposeService.infoByEarlyId(byId.getId()));
        return earlyWarningTrackDto2;
    }

    /**
     * 预警信息新增
     *
     * @param earlyWarning 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(EarlyWarning earlyWarning) {
        return save(earlyWarning);
    }

    /**
     * 预警信息修改
     *
     * @param earlyWarning 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(EarlyWarning earlyWarning) {
        return updateById(earlyWarning);
    }

    /**
     * 预警信息删除(单个条目)
     *
     * @param id
     * @return
     */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
     * 预警信息删除(多个条目)
     *
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }

    @Override
    public List<Map<String, Object>> statement(EarlyWarningStatementDto dto) {
        List<Map<String, Object>> map = new ArrayList<>();
        if (dto.getType() == 1) {
            map = mapper.shipTypeStatement(dto);
        }

        if (dto.getType() == 2) {
            map = mapper.earlyWarningStatement(dto);
        }
        return map;
    }


    @Override
    public boolean saveEarlyWarningTrack(EarlyWarningTrackDto earlyWarning) {
        return save(earlyWarning);
/*        if (save(earlyWarning)) {
            EarlyWarningTrack earlyWarningTrack = new EarlyWarningTrack();
            earlyWarningTrack.setId(earlyWarning.getId());
            earlyWarningTrack.setTrack(JSON.toJSONString(earlyWarning.getTrack()));
            warlyWarningTrackMapper.insert(earlyWarningTrack);
            return true;
        }
        return false;*/
    }

    @Override
    public boolean endOrIgnore(Integer id, Byte oper) {
        LambdaUpdateWrapper<EarlyWarning> wrapper = new LambdaUpdateWrapper<>();

        LocalDateTime now = LocalDateTime.now();
        if (oper == 1) {
            wrapper.set(EarlyWarning::getIgnore, true);
        } else if (oper == 2) {
            wrapper.set(EarlyWarning::getFinish, true);
            wrapper.set(EarlyWarning::getDisposeTime, now);
        } else {
            return false;
        }
        wrapper.set(EarlyWarning::getDisposer, SecurityUtils.getUsername());
        wrapper.set(EarlyWarning::getDisposeTime, now);
        wrapper.eq(EarlyWarning::getId, id);
        return update(wrapper);
    }
}
