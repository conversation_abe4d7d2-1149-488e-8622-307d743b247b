package com.xx.web.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xx.web.domain.dto.ShipTrackDto;
import com.xx.web.domain.entity.ShipTrack;

import java.util.List;
import java.util.Map;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-03-17
*/
public interface IShipTrackService extends IService<ShipTrack> {

    /**
    * 分页列表
    * @param shipTrack 根据需要进行传值
    * @return
    */
    IPage<ShipTrack> page(ShipTrack shipTrack);

    List<ShipTrack> trackList(ShipTrackDto shipTrack);
    List<List<Object>> history(String shipId, Double hours);

    Map<String, Integer> historyStatistics(Integer days);
    /**
    * 详情
    * @param id
    * @return
    */
    ShipTrack info(Long id);

    /**
    * 新增
    * @param shipTrack 根据需要进行传值
    * @return
    */
    boolean add(ShipTrack shipTrack);

    /**
    * 修改
    * @param shipTrack 根据需要进行传值
    * @return
    */
    boolean modify(ShipTrack shipTrack);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);
        }
