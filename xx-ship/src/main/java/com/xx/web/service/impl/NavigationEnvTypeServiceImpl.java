package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.constant.UserConstants;
import com.xx.common.exception.ServiceException;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.NavigationEnvType;
import com.xx.web.mapper.NavigationEnvTypeMapper;
import com.xx.web.service.INavigationEnvTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 通航环境类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Service
@RequiredArgsConstructor
public class NavigationEnvTypeServiceImpl extends ServiceImpl<NavigationEnvTypeMapper, NavigationEnvType> implements INavigationEnvTypeService {

    private final NavigationEnvTypeMapper mapper;

    /**
     * 通航环境类型分页列表
     *
     * @param navigationEnvType 根据需要进行传值
     * @return
     */
    @Override
    public IPage<NavigationEnvType> page(NavigationEnvType navigationEnvType) {

        QueryWrapper<NavigationEnvType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // 通航环境类型id
                .eq(navigationEnvType.getId() != null, NavigationEnvType::getId, navigationEnvType.getId())
                // 类型名称
                .eq(!StringUtils.isEmpty(navigationEnvType.getName()), NavigationEnvType::getName, navigationEnvType.getName())
                .eq(!StringUtils.isEmpty(navigationEnvType.getEnvKey()), NavigationEnvType::getEnvKey, navigationEnvType.getEnvKey())
                // 父级id,默认0
                .eq(navigationEnvType.getParentid() != null, NavigationEnvType::getParentid, navigationEnvType.getParentid())
                // 是不是父节点,默认不是0
                .eq(navigationEnvType.getIsParent() != null, NavigationEnvType::getIsParent, navigationEnvType.getIsParent())
                // 状态（1正常 0停用）
                .eq(navigationEnvType.getStatus() != null, NavigationEnvType::getStatus, navigationEnvType.getStatus())
                .eq(navigationEnvType.getIsFence() != null, NavigationEnvType::getIsFence, navigationEnvType.getIsFence())
                // 图标
                .eq(!StringUtils.isEmpty(navigationEnvType.getIcon()), NavigationEnvType::getIcon, navigationEnvType.getIcon())
                // 创建者
                .eq(!StringUtils.isEmpty(navigationEnvType.getCreateBy()), NavigationEnvType::getCreateBy, navigationEnvType.getCreateBy())
                // 创建时间
                .eq(navigationEnvType.getGmtCreate() != null, NavigationEnvType::getGmtCreate, navigationEnvType.getGmtCreate())
                // 更新者
                .eq(!StringUtils.isEmpty(navigationEnvType.getUpdateBy()), NavigationEnvType::getUpdateBy, navigationEnvType.getUpdateBy())
                // 更新时间
                .eq(navigationEnvType.getGmtModified() != null, NavigationEnvType::getGmtModified, navigationEnvType.getGmtModified())
                // 备注
                .eq(!StringUtils.isEmpty(navigationEnvType.getRemark()), NavigationEnvType::getRemark, navigationEnvType.getRemark())
                // 排序
                .orderByAsc(NavigationEnvType::getSort);

        IPage<NavigationEnvType> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * 通航环境类型详情
     *
     * @param id
     * @return
     */
    @Override
    public NavigationEnvType info(Long id) {
        return getById(id);
    }

    /**
     * 通航环境类型新增
     *
     * @param navigationEnvType 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(NavigationEnvType navigationEnvType) {
        if (!checkEnvTypeUnique(navigationEnvType)) {
                throw new ServiceException("新增通航环境类型'" + navigationEnvType.getName() + "'失败，类型已存在");
        }
        return save(navigationEnvType);
    }

    /**
     * 通航环境类型修改
     *
     * @param navigationEnvType 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(NavigationEnvType navigationEnvType) {
        if (!checkEnvTypeUnique(navigationEnvType)) {
            throw new ServiceException("修改通航环境类型'" + navigationEnvType.getName() + "'失败，类型已存在");
        }
        return updateById(navigationEnvType);
    }

    /**
     * 通航环境类型删除(单个条目)
     *
     * @param id
     * @return
     */
    @Override
    public boolean remove(Integer id) {
        return removeById(id);
    }

    /**
     * 通航环境类型删除(多个条目)
     *
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Integer> ids) {
        return removeByIds(ids);
    }



    /**
     * 校验通航环境类型称是否唯一
     *
     * @param envType 通航环境类型
     * @return 结果
     */
    @Override
    public boolean checkEnvTypeUnique(NavigationEnvType envType)
    {
        Integer dictId = com.xx.common.utils.StringUtils.isNull(envType.getId()) ? -1 : envType.getId();
        NavigationEnvType navigationEnvType = mapper.selectOne(Wrappers.<NavigationEnvType>lambdaQuery()
                .select(NavigationEnvType::getId).eq(NavigationEnvType::getEnvKey, envType.getEnvKey()));
        if (com.xx.common.utils.StringUtils.isNotNull(navigationEnvType) && navigationEnvType.getId().equals(dictId))
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }


    @Override
    public List<NavigationEnvType> warningList() {

        return   list(Wrappers.<NavigationEnvType>lambdaQuery().select(NavigationEnvType::getId, NavigationEnvType::getEnvKey,
                NavigationEnvType::getName).in(NavigationEnvType::getEnvKey,"one_warning","two_warning","three_warning").eq(NavigationEnvType::getIsFence,true).orderByAsc(NavigationEnvType::getId));
    }
}
