package com.xx.web.service;

import com.xx.web.domain.entity.EarlyWarningDispose;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-25
*/
public interface IEarlyWarningDisposeService extends IService<EarlyWarningDispose> {

    /**
    * 分页列表
    * @param earlyWarningDispose 根据需要进行传值
    * @return
    */
    IPage<EarlyWarningDispose> page(EarlyWarningDispose earlyWarningDispose);

    /**
    * 详情
    * @param id
    * @return
    */
    EarlyWarningDispose info(Long id);

    EarlyWarningDispose infoByEarlyId(Integer earlyId);
    /**
    * 新增
    * @param earlyWarningDispose 根据需要进行传值
    * @return
    */
    boolean add(EarlyWarningDispose earlyWarningDispose);

    /**
    * 修改
    * @param earlyWarningDispose 根据需要进行传值
    * @return
    */
    boolean modify(EarlyWarningDispose earlyWarningDispose);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);
        }
