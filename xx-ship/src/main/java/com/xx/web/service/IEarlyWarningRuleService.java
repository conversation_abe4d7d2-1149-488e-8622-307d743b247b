package com.xx.web.service;

import com.xx.web.domain.entity.EarlyWarningRule;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-25
*/
public interface IEarlyWarningRuleService extends IService<EarlyWarningRule> {

    /**
    * 分页列表
    * @param earlyWarningRule 根据需要进行传值
    * @return
    */
    IPage<EarlyWarningRule> page(EarlyWarningRule earlyWarningRule);

    /**
    * 详情
    * @param id
    * @return
    */
    EarlyWarningRule info(Long id);

    /**
    * 新增
    * @param earlyWarningRule 根据需要进行传值
    * @return
    */
    boolean add(EarlyWarningRule earlyWarningRule);

    /**
    * 修改
    * @param earlyWarningRule 根据需要进行传值
    * @return
    */
    boolean modify(EarlyWarningRule earlyWarningRule);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);

    public void warningRules();
        }
