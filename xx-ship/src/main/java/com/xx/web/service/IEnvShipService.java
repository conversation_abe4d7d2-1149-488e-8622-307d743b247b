package com.xx.web.service;

import com.xx.web.domain.entity.EnvShip;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    * 在港,锚地等船舶 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-25
*/
public interface IEnvShipService extends IService<EnvShip> {

    /**
    * 在港,锚地等船舶分页列表
    * @param envShip 根据需要进行传值
    * @return
    */
    IPage<EnvShip> page(EnvShip envShip);

    /**
    * 在港,锚地等船舶详情
    * @param id
    * @return
    */
    EnvShip info(Long id);

    /**
    * 在港,锚地等船舶新增
    * @param envShip 根据需要进行传值
    * @return
    */
    boolean add(EnvShip envShip);

    /**
    * 在港,锚地等船舶修改
    * @param envShip 根据需要进行传值
    * @return
    */
    boolean modify(EnvShip envShip);

    /**
    * 在港,锚地等船舶删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);
        }
