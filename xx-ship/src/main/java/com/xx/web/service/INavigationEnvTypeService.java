package com.xx.web.service;

import com.xx.web.domain.entity.NavigationEnvType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 通航环境类型 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
public interface INavigationEnvTypeService extends IService<NavigationEnvType> {

    /**
     * 通航环境类型分页列表
     *
     * @param navigationEnvType 根据需要进行传值
     * @return
     */
    IPage<NavigationEnvType> page(NavigationEnvType navigationEnvType);

    /**
     * 通航环境类型详情
     *
     * @param id
     * @return
     */
    NavigationEnvType info(Long id);

    /**
     * 通航环境类型新增
     *
     * @param navigationEnvType 根据需要进行传值
     * @return
     */
    boolean add(NavigationEnvType navigationEnvType);

    /**
     * 通航环境类型修改
     *
     * @param navigationEnvType 根据需要进行传值
     * @return
     */
    boolean modify(NavigationEnvType navigationEnvType);

    /**
     * 通航环境类型删除(单个条目)
     *
     * @param id
     * @return
     */
    boolean remove(Integer id);

    /**
     * 删除(多个条目)
     *
     * @param ids
     * @return
     */
    boolean removes(List<Integer> ids);

    boolean checkEnvTypeUnique(NavigationEnvType envType);

    List<NavigationEnvType> warningList();
}
