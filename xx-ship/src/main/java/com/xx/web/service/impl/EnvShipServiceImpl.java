package com.xx.web.service.impl;

import com.xx.web.domain.entity.EnvShip;
import com.xx.web.mapper.EnvShipMapper;
import com.xx.web.service.IEnvShipService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.utils.base.MPageUtils;
import lombok.RequiredArgsConstructor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 在港,锚地等船舶 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Service
@RequiredArgsConstructor
public class EnvShipServiceImpl extends ServiceImpl<EnvShipMapper, EnvShip> implements IEnvShipService {

    private final EnvShipMapper mapper;

/**
* 在港,锚地等船舶分页列表
* @param envShip 根据需要进行传值
* @return
*/
    @Override
    public IPage<EnvShip> page(EnvShip envShip) {

        QueryWrapper<EnvShip> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 主键
            .eq(envShip.getId() != null, EnvShip::getId, envShip.getId())
            // ship id
            .eq(envShip.getShipId() != null, EnvShip::getShipId, envShip.getShipId())
            // 通航环境id
            .eq(envShip.getEnvId() != null, EnvShip::getEnvId, envShip.getEnvId())
            // 通航类型id
            .eq(envShip.getEnvType() != null, EnvShip::getEnvType, envShip.getEnvType())
            // mmsi
            .eq(!StringUtils.isEmpty(envShip.getMmsi()), EnvShip::getMmsi, envShip.getMmsi());

        IPage<EnvShip> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 在港,锚地等船舶详情
    * @param id
    * @return
    */
    @Override
    public EnvShip info(Long id) {
        return getById(id);
    }

    /**
    * 在港,锚地等船舶新增
    * @param envShip 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(EnvShip envShip) {
        return save(envShip);
    }

    /**
    * 在港,锚地等船舶修改
    * @param envShip 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(EnvShip envShip) {
        return updateById(envShip);
    }

    /**
    * 在港,锚地等船舶删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Long id) {
        return removeById(id);
    }

    /**
    * 在港,锚地等船舶删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Long> ids) {
        return removeByIds(ids);
    }

}
