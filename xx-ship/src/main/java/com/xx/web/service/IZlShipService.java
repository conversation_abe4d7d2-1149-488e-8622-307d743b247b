package com.xx.web.service;

import com.xx.web.domain.entity.ZlShip;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    * 船舶档案 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-11
*/
public interface IZlShipService extends IService<ZlShip> {

    /**
    * 船舶档案分页列表
    * @param zlShip 根据需要进行传值
    * @return
    */
    IPage<ZlShip> page(ZlShip zlShip);

    /**
    * 船舶档案详情
    * @param id
    * @return
    */
    ZlShip info(Long id);

    long countByMMSI(Integer mmsi);
    /**
    * 船舶档案新增
    * @param zlShip 根据需要进行传值
    * @return
    */
    boolean add(ZlShip zlShip);

    /**
    * 船舶档案修改
    * @param zlShip 根据需要进行传值
    * @return
    */
    boolean modify(ZlShip zlShip);

    /**
    * 船舶档案删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);

    ZlShip infoByMMSI(Integer mmsi);
}
