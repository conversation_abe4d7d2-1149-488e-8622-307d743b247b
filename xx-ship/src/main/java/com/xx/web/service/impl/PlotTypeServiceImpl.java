package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.exception.ServiceException;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.Plot;
import com.xx.web.domain.entity.PlotType;
import com.xx.web.mapper.PlotMapper;
import com.xx.web.mapper.PlotTypeMapper;
import com.xx.web.service.IPlotTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 标绘类型 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Service
@RequiredArgsConstructor
public class PlotTypeServiceImpl extends ServiceImpl<PlotTypeMapper, PlotType> implements IPlotTypeService {

    private final PlotTypeMapper mapper;

    private final PlotMapper plotMapper;

/**
* 标绘类型分页列表
* @param plotType 根据需要进行传值
* @return
*/
    @Override
    public IPage<PlotType> page(PlotType plotType) {

        QueryWrapper<PlotType> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            // 标绘类型id
            .eq(plotType.getId() != null, PlotType::getId, plotType.getId())
            // 类型名称
            .eq(!StringUtils.isEmpty(plotType.getName()), PlotType::getName, plotType.getName())
            // 类型key
            .eq(!StringUtils.isEmpty(plotType.getPlotKey()), PlotType::getPlotKey, plotType.getPlotKey())
            // 父级id,默认0
            .eq(plotType.getParentid() != null, PlotType::getParentid, plotType.getParentid())
            // 是不是父节点,默认不是0
            .eq(plotType.getIsParent() != null, PlotType::getIsParent, plotType.getIsParent())
            // 状态（1正常 0停用）
            .eq(plotType.getStatus() != null, PlotType::getStatus, plotType.getStatus())
            // 图标
            .eq(!StringUtils.isEmpty(plotType.getIcon()), PlotType::getIcon, plotType.getIcon())
            // 创建者
            .eq(!StringUtils.isEmpty(plotType.getCreateBy()), PlotType::getCreateBy, plotType.getCreateBy())
            // 创建时间
            .eq(plotType.getGmtCreate() != null, PlotType::getGmtCreate, plotType.getGmtCreate())
            // 更新者
            .eq(!StringUtils.isEmpty(plotType.getUpdateBy()), PlotType::getUpdateBy, plotType.getUpdateBy())
            // 更新时间
            .eq(plotType.getGmtModified() != null, PlotType::getGmtModified, plotType.getGmtModified())
            // 备注
            .eq(!StringUtils.isEmpty(plotType.getRemark()), PlotType::getRemark, plotType.getRemark())
            // 排序
            .eq(plotType.getSort() != null, PlotType::getSort, plotType.getSort())
            // 逻辑删除
            .eq(plotType.getDel() != null, PlotType::getDel, plotType.getDel());

        IPage<PlotType> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 标绘类型详情
    * @param id
    * @return
    */
    @Override
    public PlotType info(Long id) {
        return getById(id);
    }

    /**
    * 标绘类型新增
    * @param plotType 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(PlotType plotType) {
        return save(plotType);
    }

    /**
    * 标绘类型修改
    * @param plotType 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(PlotType plotType) {
        return updateById(plotType);
    }

    /**
    * 标绘类型删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Integer id) {
        checkPlotExist(id);
        return removeById(id);
    }

    private void checkPlotExist(Integer id) {
        if (plotMapper.selectCount(Wrappers.<Plot>lambdaQuery().eq(Plot::getPlotType, id)) > 0) {
            throw new ServiceException("分组下有标绘,无法删除");
        }
    }

    /**
    * 标绘类型删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Integer> ids) {
        ids.forEach(id -> {
            checkPlotExist(id);
        });
        return removeByIds(ids);
    }

}
