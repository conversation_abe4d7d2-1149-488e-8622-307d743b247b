package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.exception.ServiceException;
import com.xx.common.utils.SecurityUtils;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.EarlyWarning;
import com.xx.web.domain.entity.EarlyWarningDispose;
import com.xx.web.mapper.EarlyWarningDisposeMapper;
import com.xx.web.service.IEarlyWarningDisposeService;
import com.xx.web.service.IEarlyWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-25
*/
@Service
//@RequiredArgsConstructor
public class EarlyWarningDisposeServiceImpl extends ServiceImpl<EarlyWarningDisposeMapper, EarlyWarningDispose> implements IEarlyWarningDisposeService {

    @Autowired
    private  EarlyWarningDisposeMapper mapper;
    @Autowired
    private  IEarlyWarningService warningService;

/**
* 分页列表
* @param earlyWarningDispose 根据需要进行传值
* @return
*/
    @Override
    public IPage<EarlyWarningDispose> page(EarlyWarningDispose earlyWarningDispose) {

        QueryWrapper<EarlyWarningDispose> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            //
            .eq(earlyWarningDispose.getId() != null, EarlyWarningDispose::getId, earlyWarningDispose.getId())
            // 预警id
            .eq(earlyWarningDispose.getEarlyId() != null, EarlyWarningDispose::getEarlyId, earlyWarningDispose.getEarlyId())
            // 异常类型
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getExcepitionType()), EarlyWarningDispose::getExcepitionType, earlyWarningDispose.getExcepitionType())
            // 处置方式
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getHandlingWay()), EarlyWarningDispose::getHandlingWay, earlyWarningDispose.getHandlingWay())
            // 详细描述
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getDetail()), EarlyWarningDispose::getDetail, earlyWarningDispose.getDetail())
            // 处置措施
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getTreatmentMeasure()), EarlyWarningDispose::getTreatmentMeasure, earlyWarningDispose.getTreatmentMeasure())
            // 纠正情况
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getCorrectTheSituation()), EarlyWarningDispose::getCorrectTheSituation, earlyWarningDispose.getCorrectTheSituation())
            // 备注
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getRemark()), EarlyWarningDispose::getRemark, earlyWarningDispose.getRemark())
            // 是否处罚1处罚 0未处罚
            .eq(earlyWarningDispose.getPunish() != null, EarlyWarningDispose::getPunish, earlyWarningDispose.getPunish())
            // 附件最多5个,单个附件不超过2m
            .eq(!StringUtils.isEmpty(earlyWarningDispose.getAccessory()), EarlyWarningDispose::getAccessory, earlyWarningDispose.getAccessory())
            // 创建时间
            .eq(earlyWarningDispose.getGmtCreate() != null, EarlyWarningDispose::getGmtCreate, earlyWarningDispose.getGmtCreate())
            // 更新时间
            .eq(earlyWarningDispose.getGmtModified() != null, EarlyWarningDispose::getGmtModified, earlyWarningDispose.getGmtModified());

        IPage<EarlyWarningDispose> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
    * 详情
    * @param id
    * @return
    */
    @Override
    public EarlyWarningDispose info(Long id) {
        return getById(id);
    }

    /**
     * 详情
     * @param earlyId
     * @return
     */
    @Override
    public EarlyWarningDispose infoByEarlyId(Integer earlyId) {
        return getOne(Wrappers.<EarlyWarningDispose>lambdaQuery().eq(EarlyWarningDispose::getEarlyId,earlyId));
    }

    /**
    * 新增
    * @param earlyWarningDispose 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(EarlyWarningDispose earlyWarningDispose) {
        if (count(Wrappers.<EarlyWarningDispose>lambdaQuery().eq(EarlyWarningDispose::getEarlyId,
                earlyWarningDispose.getEarlyId())) > 0) {
            throw new ServiceException("该预警已处理");
        }
        boolean save = save(earlyWarningDispose);
        if (save) {
            warningService.update(Wrappers.<EarlyWarning>lambdaUpdate().set(EarlyWarning::getDispose, true)
                    .set(EarlyWarning::getDisposeTime, LocalDateTime.now())
                    .set(EarlyWarning::getDisposer, SecurityUtils.getLoginUser().getUsername()).eq(EarlyWarning::getId, earlyWarningDispose.getEarlyId()));
        }
        return save;
    }

    /**
    * 修改
    * @param earlyWarningDispose 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(EarlyWarningDispose earlyWarningDispose) {
        return updateById(earlyWarningDispose);
    }

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Long id) {
        return removeById(id);
    }

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Long> ids) {
        return removeByIds(ids);
    }

}
