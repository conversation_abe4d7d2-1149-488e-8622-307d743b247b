package com.xx.web.service;

import com.xx.web.domain.entity.PlotType;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    * 标绘类型 服务类
    * </p>
*
* <AUTHOR>
* @since 2023-04-25
*/
public interface IPlotTypeService extends IService<PlotType> {

    /**
    * 标绘类型分页列表
    * @param plotType 根据需要进行传值
    * @return
    */
    IPage<PlotType> page(PlotType plotType);

    /**
    * 标绘类型详情
    * @param id
    * @return
    */
    PlotType info(Long id);

    /**
    * 标绘类型新增
    * @param plotType 根据需要进行传值
    * @return
    */
    boolean add(PlotType plotType);

    /**
    * 标绘类型修改
    * @param plotType 根据需要进行传值
    * @return
    */
    boolean modify(PlotType plotType);

    /**
    * 标绘类型删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Integer id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Integer> ids);
        }
