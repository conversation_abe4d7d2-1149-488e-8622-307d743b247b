package com.xx.web.service.impl;

import com.xx.web.domain.MgTrack;
import com.xx.web.domain.base.PageReq;
import com.xx.web.domain.base.PageResult;
import com.xx.web.mapper.MgTrackRepository;
import com.xx.web.service.MgTrackService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 0:13
 * @version: 1.0
 */
@Service
@RequiredArgsConstructor
public class MgTrackServiceImpl implements MgTrackService {

    private final MgTrackRepository trackRepository;

    private final MongoTemplate mongoTemplate;

    public static final String COLLECTION_NAME_PREFIX = "mgTrack_";
    //private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMM");
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");
    public static String getCollectionNameFromDate(LocalDate date) {
        return COLLECTION_NAME_PREFIX + DATE_FORMAT.format(date);
    }
    public void batchSave(List<MgTrack> list) {
        mongoTemplate.insert(list,getCollectionNameFromDate(LocalDate.now()));
    }

    public PageResult<MgTrack> dynamicQuery(MgTrack queryObject, Long startTime, Long endTime, PageReq pageReq) {


        Query query = new Query();

        if (queryObject.getMmsi() != null) {
            query.addCriteria(Criteria.where("mmsi").is(queryObject.getMmsi()));
        }

        if (queryObject.getStatus() != null) {
            query.addCriteria(Criteria.where("status").is(queryObject.getStatus()));
        }

        if (queryObject.getRot() != null) {
            query.addCriteria(Criteria.where("rot").is(queryObject.getRot()));
        }
        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("time").gte(startTime).lte(endTime));
        }
        query.with(Sort.by(Sort.Direction.ASC, "time"));

        // Count total records before pagination
        long totalCount = mongoTemplate.count(query, MgTrack.class);

        // Sort and paginate
        query.with(Sort.by(Sort.Direction.ASC, "time"));
        Pageable pageable = PageRequest.of(pageReq.getPage(), pageReq.getPageSize());
        query.with(pageable);

        // Fetch the paginated result list
        List<MgTrack> resultList = mongoTemplate.find(query, MgTrack.class);

        // Create and return the PageResult with pagination information
        return new PageResult<>(resultList, totalCount, pageable);
    }
    public List<MgTrack> trackQuery(MgTrack queryObject, Long startTime, Long endTime) {

        Query query = new Query();

        if (queryObject.getMmsi() != null) {
            query.addCriteria(Criteria.where("mmsi").is(queryObject.getMmsi()));
        }
        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("time").gte(startTime).lte(endTime));
        }
        query.with(Sort.by(Sort.Direction.ASC, "time"));

        // Count total records before pagination
        long totalCount = mongoTemplate.count(query, MgTrack.class);

        // Sort and paginate
        query.with(Sort.by(Sort.Direction.ASC, "time"));
        Pageable pageable = PageRequest.of(0, 500);
        query.with(pageable);

        // Fetch the paginated result list
        List<MgTrack> resultList = mongoTemplate.find(query, MgTrack.class);
        return resultList;
    }
}
