package com.xx.web.service;

import com.xx.web.domain.entity.KeyShip;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
* <p>
    *  服务类
    * </p>
*
* <AUTHOR>
* @since 2023-03-17
*/
public interface IKeyShipService extends IService<KeyShip> {

    /**
    * 分页列表
    * @param keyShip 根据需要进行传值
    * @return
    */
    IPage<KeyShip> page(KeyShip keyShip);

    /**
    * 详情
    * @param id
    * @return
    */
    KeyShip info(Long id);

    /**
    * 新增
    * @param shipId 根据需要进行传值
    * @return
    */
    boolean add(String shipId,String remark);

    /**
    * 修改
    * @param keyShip 根据需要进行传值
    * @return
    */
    boolean modify(KeyShip keyShip);

    /**
    * 删除(单个条目)
    * @param id
    * @return
    */
    boolean remove(Long id);

    /**
    * 删除(多个条目)
    * @param ids
    * @return
    */
    boolean removes(List<Long> ids);
        }
