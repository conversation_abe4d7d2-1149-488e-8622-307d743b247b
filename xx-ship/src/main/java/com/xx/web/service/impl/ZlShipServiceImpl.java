package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.exception.ServiceException;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.ZlShip;
import com.xx.web.mapper.ZlShipMapper;
import com.xx.web.service.IZlShipService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * 船舶档案 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-11
*/
@Service
@RequiredArgsConstructor
public class ZlShipServiceImpl extends ServiceImpl<ZlShipMapper, ZlShip> implements IZlShipService {

    private final ZlShipMapper mapper;

/**
* 船舶档案分页列表
* @param zlShip 根据需要进行传值
* @return
*/
    @Override
    public IPage<ZlShip> page(ZlShip zlShip) {

        QueryWrapper<ZlShip> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
            //
            .eq(zlShip.getId() != null, ZlShip::getId, zlShip.getId())
            // IMO
            .eq(!StringUtils.isEmpty(zlShip.getImo()), ZlShip::getImo, zlShip.getImo())
            // MMSI
            .eq(!StringUtils.isEmpty(zlShip.getMmsi()), ZlShip::getMmsi, zlShip.getMmsi())
            // 船舶编号
            .eq(!StringUtils.isEmpty(zlShip.getShipNo()), ZlShip::getShipNo, zlShip.getShipNo())
            // 船舶ID
            .eq(!StringUtils.isEmpty(zlShip.getShipId()), ZlShip::getShipId, zlShip.getShipId())
            // 登记编号
            .eq(!StringUtils.isEmpty(zlShip.getRegistrationNo()), ZlShip::getRegistrationNo, zlShip.getRegistrationNo())
            // 初始登记号
            .eq(!StringUtils.isEmpty(zlShip.getInitialRegistrationNo()), ZlShip::getInitialRegistrationNo, zlShip.getInitialRegistrationNo())
            // 检查号
            .eq(!StringUtils.isEmpty(zlShip.getShipSurveyNo()), ZlShip::getShipSurveyNo, zlShip.getShipSurveyNo())
            // 牌簿号
            .eq(!StringUtils.isEmpty(zlShip.getCardbook()), ZlShip::getCardbook, zlShip.getCardbook())
            // IC卡号
            .eq(!StringUtils.isEmpty(zlShip.getIcno()), ZlShip::getIcno, zlShip.getIcno())
            // 呼号
            .eq(!StringUtils.isEmpty(zlShip.getCallsign()), ZlShip::getCallsign, zlShip.getCallsign())
            // 中文船名
            .eq(!StringUtils.isEmpty(zlShip.getLocalName()), ZlShip::getLocalName, zlShip.getLocalName())
            // 英文船名
            .eq(!StringUtils.isEmpty(zlShip.getShipNameEn()), ZlShip::getShipNameEn, zlShip.getShipNameEn())
            // 船舶状态
            .eq(!StringUtils.isEmpty(zlShip.getStatusCode()), ZlShip::getStatusCode, zlShip.getStatusCode())
            // 船舶类型
            .eq(!StringUtils.isEmpty(zlShip.getShipTypeCode()), ZlShip::getShipTypeCode, zlShip.getShipTypeCode())
            // 船旗国码
            .eq(!StringUtils.isEmpty(zlShip.getFlagCode()), ZlShip::getFlagCode, zlShip.getFlagCode())
            // 注册港口
            .eq(!StringUtils.isEmpty(zlShip.getRegistrationPort()), ZlShip::getRegistrationPort, zlShip.getRegistrationPort())
            // 建造日期
            .eq(zlShip.getBuildDate() != null, ZlShip::getBuildDate, zlShip.getBuildDate())
            // 船厂
            .eq(!StringUtils.isEmpty(zlShip.getShipyard()), ZlShip::getShipyard, zlShip.getShipyard())
            // 所属
            .eq(!StringUtils.isEmpty(zlShip.getOwner()), ZlShip::getOwner, zlShip.getOwner())
            // 最大船速
            .eq(zlShip.getMaxSpeed() != null, ZlShip::getMaxSpeed, zlShip.getMaxSpeed())
            // 船长
            .eq(zlShip.getLoa() != null, ZlShip::getLoa, zlShip.getLoa())
            // 船舶型宽
            .eq(zlShip.getLbp() != null, ZlShip::getLbp, zlShip.getLbp())
            // 船舶型深
            .eq(zlShip.getDepth() != null, ZlShip::getDepth, zlShip.getDepth())
            // 船宽
            .eq(zlShip.getBm() != null, ZlShip::getBm, zlShip.getBm())
            // 夏季满载吃水
            .eq(zlShip.getDraught() != null, ZlShip::getDraught, zlShip.getDraught())
            // 船高
            .eq(zlShip.getHeight() != null, ZlShip::getHeight, zlShip.getHeight());

        IPage<ZlShip> page = page(MPageUtils.mpPage(false,null,false), queryWrapper);
        page.setTotal(mapper.countByMMSI(queryWrapper));
        return page;
    }
    /**
    * 船舶档案详情
    * @param id
    * @return
    */
    @Override
    public ZlShip info(Long id) {
        return getById(id);
    }


    public long countByMMSI(Integer mmsi) {

        return count(Wrappers.<ZlShip>lambdaQuery().eq(ZlShip::getMmsi, mmsi));
    }
    /**
    * 船舶档案新增
    * @param zlShip 根据需要进行传值
    * @return
    */
    @Override
    public boolean add(ZlShip zlShip) {
        if (countByMMSI(zlShip.getMmsi()) > 0) {
            throw new ServiceException("添加的船舶信息已存在");
        }
        return save(zlShip);
    }

    /**
    * 船舶档案修改
    * @param zlShip 根据需要进行传值
    * @return
    */
    @Override
    public boolean modify(ZlShip zlShip) {
        zlShip.setMmsi(null);
        return updateById(zlShip);
    }

    /**
    * 船舶档案删除(单个条目)
    * @param id
    * @return
    */
    @Override
    public boolean remove(Long id) {
        return removeById(id);
    }

    /**
    * 船舶档案删除(多个条目)
    * @param ids
    * @return
    */
    @Override
    public boolean removes(List<Long> ids) {
        return removeByIds(ids);
    }


    @Override
    public ZlShip infoByMMSI(Integer mmsi) {
        return  getOne(Wrappers.<ZlShip>lambdaQuery().eq(ZlShip::getMmsi, mmsi).last("limit 1"));
    }
}
