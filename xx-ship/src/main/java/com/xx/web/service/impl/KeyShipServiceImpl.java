package com.xx.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xx.common.exception.ServiceException;
import com.xx.utils.base.MPageUtils;
import com.xx.web.domain.entity.KeyShip;
import com.xx.web.domain.entity.ZlShip;
import com.xx.web.mapper.KeyShipMapper;
import com.xx.web.mapper.ZlShipMapper;
import com.xx.web.service.IKeyShipService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Service
@RequiredArgsConstructor
public class KeyShipServiceImpl extends ServiceImpl<KeyShipMapper, KeyShip> implements IKeyShipService {

    private final KeyShipMapper mapper;

    private final ZlShipMapper shipMapper;

    /**
     * 分页列表
     * @param keyShip 根据需要进行传值
     * @return
     */
    @Override
    public IPage<KeyShip> page(KeyShip keyShip) {

        QueryWrapper<KeyShip> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                // 主键
                .eq(keyShip.getId() != null, KeyShip::getId, keyShip.getId())
                // 船舶id
                .like(keyShip.getShipId()!= null, KeyShip::getShipId, keyShip.getShipId())
                // mmsi
                .like(!StringUtils.isEmpty(keyShip.getMmsi()) , KeyShip::getMmsi, keyShip.getMmsi())
                // 英文名
                .eq(!StringUtils.isEmpty(keyShip.getEnglishName()), KeyShip::getEnglishName, keyShip.getEnglishName())
                // 中文名
                .eq(!StringUtils.isEmpty(keyShip.getChineseName()), KeyShip::getChineseName, keyShip.getChineseName())
                // 备注
                .eq(!StringUtils.isEmpty(keyShip.getRemark()), KeyShip::getRemark, keyShip.getRemark());

        IPage<KeyShip> page = page(MPageUtils.mpPage(), queryWrapper);
        return page;
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @Override
    public KeyShip info(Long id) {
        return getById(id);
    }

    /**
     * 新增
     * @param shipId 根据需要进行传值
     * @return
     */
    @Override
    public boolean add(String shipId,String remark) {
        ZlShip ship = shipMapper.selectById(shipId);
        if (ship == null) {
            throw new ServiceException("选择的船不存在!");
        }
        KeyShip keyShip = new KeyShip();
        keyShip.setMmsi(ship.getMmsi())
                .setChineseName(ship.getLocalName())
                .setEnglishName(ship.getShipNameEn())
                .setShipId(ship.getId())
                .setRemark(remark);
        return save(keyShip);
    }

    /**
     * 修改
     * @param keyShip 根据需要进行传值
     * @return
     */
    @Override
    public boolean modify(KeyShip keyShip) {
        return updateById(keyShip);
    }

    /**
     * 删除(单个条目)
     * @param id
     * @return
     */
    @Override
    public boolean remove(Long id) {
        return removeById(id);
    }

    /**
     * 删除(多个条目)
     * @param ids
     * @return
     */
    @Override
    public boolean removes(List<Long> ids) {
        return removeByIds(ids);
    }

}
