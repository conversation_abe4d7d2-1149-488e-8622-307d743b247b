package com.xx.web.domain.dto;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.xx.utils.giu.BmapPoint;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: EnvDto
 * @Description:
 * @Date: 2023/5/26 10:54
 * @since JDK 1.8
 */
@Data
@Accessors(chain = true)
public class EnvDto {

    private Integer id;

    private String name;

    private String envType;


    private Integer envTypeId;

    private String group;

    private String place;

    private List<List<BmapPoint>> regions;


    public EnvDto setRegions(String regions) {
        this.regions = JSON.parseObject(regions, new TypeReference<List<List<BmapPoint>>>() {
        });

        return this;
    }
}
