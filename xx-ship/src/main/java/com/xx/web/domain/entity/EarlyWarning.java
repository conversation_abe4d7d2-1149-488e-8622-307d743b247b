package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
* <p>
* 预警信息
* </p>
* <AUTHOR>
* @since 2023-04-26
*/
@Data
@Accessors(chain = true)
@TableName("early_warning")
@ApiModel(value = "EarlyWarning对象", description = "预警信息")
public class EarlyWarning {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("mmsi")
    @TableField("`mmsi`")
    private Integer mmsi;
    @ApiModelProperty("船名")
    @TableField("`ship_name`")
    private String shipName;
    @ApiModelProperty("事件内容")
    @TableField("`event_content`")
    private String eventContent;
    @ApiModelProperty("地点")
    @TableField("`address`")
    private String address;
    @ApiModelProperty("经度")
    @TableField("`lon`")
    private Double lon;
    @ApiModelProperty("纬度")
    @TableField("`lat`")
    private Double lat;
    @ApiModelProperty("船舶类型")
    @TableField("`ship_type`")
    private Integer shipType;
    @ApiModelProperty("通航环境id")
    @TableField("`env_id`")
    private Integer envId;
    @ApiModelProperty("通航环境类型id")
    @TableField("`env_type`")
    private Integer envType;
    @ApiModelProperty("视频id")
    @TableField("`video_url`")
    private String videoUrl;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
    @ApiModelProperty("1已处理0未处理")
    @TableField("`dispose`")
    private Boolean dispose;
    @ApiModelProperty("处理时间")
    @TableField("`dispose_time`")
    private LocalDateTime disposeTime;
    @ApiModelProperty("处理人")
    @TableField("`disposer`")
    private String disposer;
    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;
    @ApiModelProperty("预警等级")
    @TableField("`warning_lv`")
    private String warningLv;
    @ApiModelProperty("1忽略 0未忽略")
    @TableField("`ignore`")
    private Boolean ignore;
    @ApiModelProperty("1结束 0未结束")
    @TableField("`finish`")
    private Boolean finish;
    @ApiModelProperty("结束时间")
    @TableField("`end_time`")
    private Long endTime;
    @ApiModelProperty("触发时间")
    @TableField("`warning_time`")
    private Long warningTime;
    @ApiModelProperty("0 ais 1雷达")
    @TableField("`type`")
    private Byte type;
    @ApiModelProperty("0其他,1表示册子,2表示金塘")
    @TableField("`place`")
    private String place;

}
