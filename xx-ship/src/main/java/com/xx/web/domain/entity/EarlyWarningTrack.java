package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* <p>
*
* </p>
* <AUTHOR>
* @since 2023-05-31
*/
@Data
@Accessors(chain = true)
@TableName("early_warning_track")
@ApiModel(value = "EarlyWarningTrack对象", description = "")
public class EarlyWarningTrack {

    @ApiModelProperty("预警id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("预警航迹")
    @TableField("`track`")
    private String track;
}
