package com.xx.web.domain;

import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;

import java.io.Serializable;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 0:08
 * @version: 1.0
 */
@Data
//@Document(collection = "MgTrack")
public class MgTrack implements Serializable {

    private String id;

    @Indexed
    private Integer mmsi;
    /**
     * 航行状态
     */
    private Integer status;
    /**
     * 转向率
     */
    private Float rot;
    /**
     * 对地航速
     */
    private Float sog;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 精度
     */
    private Double lon;
    /**
     * 对地航向
     */
    private Float cog;
    /**
     * 艏向
     */
    private Float heading;

    @Indexed
    private Long time;




}
