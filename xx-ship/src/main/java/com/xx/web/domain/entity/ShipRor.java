package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
* <p>
* 船舶留档记录
* </p>
* <AUTHOR>
* @since 2023-05-29
*/
@Data
@Accessors(chain = true)
@TableName("ship_ror")
@ApiModel(value = "ShipRor对象", description = "船舶留档记录")
public class ShipRor {

    @ApiModelProperty("船舶留档记录id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("船舶mmsi")
    @TableField("`mmsi`")
    private Integer mmsi;
    @ApiModelProperty("船舶名称")
    @TableField("`name`")
    private String name;
    @ApiModelProperty("预警类型")
    @TableField("`env_type`")
    private String envType;
    @ApiModelProperty("经度")
    @TableField("`lon`")
    private Double lon;
    @ApiModelProperty("纬度")
    @TableField("`lat`")
    private Double lat;
    @ApiModelProperty("触发预警时的航迹")
    @TableField("`track`")
    private String track;
    @ApiModelProperty("触发时间")
    @TableField("`trigger_time`")
    private Long triggerTime;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
}
