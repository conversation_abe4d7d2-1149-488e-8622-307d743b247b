package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-21
 */
@Data
@Accessors(chain = true)
@TableName("device")
@ApiModel(value = "Device对象", description = "")
public class Device {

    @ApiModelProperty("id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("设备名称")
    @TableField("`name`")
    private String name;
    @ApiModelProperty("0未知,1雷达,2广播,3光照,4vhf,5摄像头")
    @TableField("`type`")
    private Byte type;
    @ApiModelProperty("IP地址")
    @TableField("`ip`")
    private String ip;
    @ApiModelProperty("端口")
    @TableField("`port`")
    private Integer port;
    @ApiModelProperty("0无需连接 1 netty")
    @TableField("`conn_type`")
    private Byte connType;
    @ApiModelProperty("连接类型 1client,2server,0其他")
    @TableField("`contype`")
    private Byte contype;
    @ApiModelProperty("1正常,0关闭")
    @TableField("`status`")
    private Boolean status;
    @ApiModelProperty("1删除,0正常")
    @TableField("`del`")
    @TableLogic
    private Boolean del;
    @ApiModelProperty("经度")
    @TableField("`lon`")
    private Object lon;
    @ApiModelProperty("纬度")
    @TableField("`lat`")
    private Object lat;
    @ApiModelProperty("设备特有属性，可以使用JSON格式存储。例如，对于摄像头，可以存储广角、清晰度等属性；对于喇叭，可以存储声音等属性；对于探照灯，可以存储照明距离和范围等属性。")
    @TableField("`own_attr`")
    private String ownAttr;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
}
