package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xx.interfaces.validatedGroups.Insert;
import com.xx.interfaces.validatedGroups.Update;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 船舶档案
* </p>
* <AUTHOR>
* @since 2023-04-11
*/
@Data
@Accessors(chain = true)
@TableName("zl_ship")
@ApiModel(value = "ZlShip对象", description = "船舶档案")
public class ZlShip {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("IMO")
    @TableField("imo")
    private String imo;
    @ApiModelProperty("MMSI")
    @NotNull(groups = {Insert.class, Update.class},message = "mmsi是必须的,没有mmsi的船填0")
    @Min(0)
    @Max(Integer.MAX_VALUE)
    @TableField("mmsi")
    private Integer mmsi;
    @ApiModelProperty("船舶编号")
    @TableField("ship_no")
    private String shipNo;
    @ApiModelProperty("船舶ID")
    @TableField("ship_id")
    private String shipId;
    @ApiModelProperty("登记编号")
    @TableField("registration_no")
    private String registrationNo;
    @ApiModelProperty("初始登记号")
    @TableField("initial_registration_no")
    private String initialRegistrationNo;
    @ApiModelProperty("检查号")
    @TableField("ship_survey_no")
    private String shipSurveyNo;
    @ApiModelProperty("牌簿号")
    @TableField("cardbook")
    private String cardbook;
    @ApiModelProperty("IC卡号")
    @TableField("icno")
    private String icno;
    @ApiModelProperty("呼号")
    @TableField("callsign")
    private String callsign;
    @ApiModelProperty("中文船名")
    @TableField("local_name")
    private String localName;
    @ApiModelProperty("英文船名")
    @TableField("ship_name_en")
    private String shipNameEn;
    @ApiModelProperty("曾用名1")
    @TableField("former_name1")
    private String formerName1;
    @ApiModelProperty("曾用名2")
    @TableField("former_name2")
    private String formerName2;
    @ApiModelProperty("曾用名3")
    @TableField("former_name3")
    private String formerName3;
    @ApiModelProperty("船舶状态")
    @TableField("status_code")
    private String statusCode;
    @ApiModelProperty("船舶类型")
    @TableField("ship_type_code")
    private String shipTypeCode;
    @ApiModelProperty("船旗国码")
    @TableField("flag_code")
    private String flagCode;
    @ApiModelProperty("注册港口")
    @TableField("registration_port")
    private String registrationPort;
    @ApiModelProperty("内河船")
    @TableField("inland_ship_mark")
    private String inlandShipMark;
    @ApiModelProperty("建造日期")
    @TableField("build_date")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime buildDate;
    @ApiModelProperty("船厂")
    @TableField("shipyard")
    private String shipyard;
    @ApiModelProperty("所属")
    @TableField("owner")
    private String owner;
    @ApiModelProperty("联系人电话")
    @TableField("contact_no")
    private String contactNo;
    @ApiModelProperty("船舶经营人")
    @TableField("operator")
    private String operator;
    @ApiModelProperty("船级社")
    @TableField("classification_code")
    private String classificationCode;
    @ApiModelProperty("最大船速")
    @TableField("max_speed")
    private Integer maxSpeed;
    @ApiModelProperty("船长")
    @TableField("loa")
    private BigDecimal loa;
    @ApiModelProperty("船舶型宽")
    @TableField("lbp")
    private BigDecimal lbp;
    @ApiModelProperty("船舶型深")
    @TableField("depth")
    private BigDecimal depth;
    @ApiModelProperty("船宽")
    @TableField("bm")
    private BigDecimal bm;
    @ApiModelProperty("夏季满载吃水")
    @TableField("draught")
    private BigDecimal draught;
    @ApiModelProperty("船高")
    @TableField("height")
    private BigDecimal height;
    @ApiModelProperty("总吨")
    @TableField("gross")
    private Integer gross;
    @ApiModelProperty("净吨")
    @TableField("net")
    private Integer net;
    @ApiModelProperty("载重吨")
    @TableField("dwt")
    private Integer dwt;
    @ApiModelProperty("各货舱容积")
    @TableField("holds")
    private String holds;
    @ApiModelProperty("货舱数及总容积")
    @TableField("hatch")
    private String hatch;
    @ApiModelProperty("最小干舷")
    @TableField("min_freeboard")
    private String minFreeboard;
    @ApiModelProperty("核定抗风等级")
    @TableField("wind_loading")
    private Integer windLoading;
    @ApiModelProperty("箱位")
    @TableField("slot")
    private Integer slot;
    @ApiModelProperty("车位")
    @TableField("carport")
    private Integer carport;
    @ApiModelProperty("客位")
    @TableField("passenger_spaces")
    private Integer passengerSpaces;
    @ApiModelProperty("最低安全配员数")
    @TableField("min_safe_manning_no")
    private Integer minSafeManningNo;
    @ApiModelProperty("救生设备最大数")
    @TableField("max_survival_equipment_no")
    private Integer maxSurvivalEquipmentNo;
    @ApiModelProperty("船体材料代码")
    @TableField("hull_material_code")
    private String hullMaterialCode;
    @ApiModelProperty("螺旋桨类型")
    @TableField("propeller_type")
    private String propellerType;
    @ApiModelProperty("主机功率")
    @TableField("power")
    private BigDecimal power;
    @ApiModelProperty("主机转速")
    @TableField("rpm")
    private Integer rpm;
    @ApiModelProperty("建造地方")
    @TableField("build_place")
    private String buildPlace;
    @ApiModelProperty("主机型式")
    @TableField("power_type")
    private String powerType;
    @ApiModelProperty("主机数量")
    @TableField("power_no")
    private Integer powerNo;
    @ApiModelProperty("主机缸数")
    @TableField("power_bore_no")
    private String powerBoreNo;
    @ApiModelProperty("主机缸径")
    @TableField("cylinder_bore")
    private String cylinderBore;
    @ApiModelProperty("主机行程")
    @TableField("power_itinerary")
    private Double powerItinerary;
    @ApiModelProperty("甲板层数")
    @TableField("decks")
    private Integer decks;
    @ApiModelProperty("Ballast")
    @TableField("ballast")
    private String ballast;
    @ApiModelProperty("AuxiliaryPower")
    @TableField("auxiliary_power")
    private String auxiliaryPower;
    @ApiModelProperty("PowerClass")
    @TableField("power_class")
    private String powerClass;
    @ApiModelProperty("数据源")
    @TableField("data_source")
    private Integer dataSource;
    @ApiModelProperty("最后更新时间")
    @TableField("last_update_time")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;
    @ApiModelProperty("删除标志")
    @TableField("del")
    private Boolean del;
    @TableField("row_ver")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime rowVer;
}
