package com.xx.web.domain.vo;

import com.xx.web.domain.entity.EarlyWarningRule;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: InnerNavigationEnvVo
 * @Description:
 * @Date: 2023/4/25 17:11
 * @since JDK 1.8
 */
@Data
public class InnerNavigationEnvVo {

    private Integer id;

    private String name;

    private String envKey;

    private Integer envType;

    private String regions;

    private List<EarlyWarningRule> rules;


}
