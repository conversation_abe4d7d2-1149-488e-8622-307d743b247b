package com.xx.web.domain.dto;

import com.xx.web.domain.entity.EarlyWarning;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: EarlyWarningTrack
 * @Description:
 * @Date: 2023/5/31 17:47
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EarlyWarningTrackDto extends EarlyWarning {

    private List<List<Object>> track;
}
