package com.xx.web.domain.dto;

import com.xx.web.domain.MgTrack;
import com.xx.web.domain.entity.EarlyWarning;
import com.xx.web.domain.entity.EarlyWarningDispose;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: EarlyWarningTrack
 * @Description:
 * @Date: 2023/5/31 17:47
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EarlyWarningTrackDto2 extends EarlyWarning {

    private List<List<MgTrack>> track;
    private EarlyWarningDispose warningDispose;

}
