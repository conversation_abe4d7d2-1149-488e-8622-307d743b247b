package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
* 预警规则
* </p>
* <AUTHOR>
* @since 2023-04-25
*/
@Data
@Accessors(chain = true)
@TableName("early_warning_rule")
@ApiModel(value = "EarlyWarningRule对象", description = "")
public class EarlyWarningRule {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("规则类型")
    @TableField("`name`")
    private String name;
    @ApiModelProperty("自动结束时长(分)")
    @TableField("`auto_exit`")
    private Integer autoExit;
    @ApiModelProperty("最小速度(节)")
    @TableField("`mix_speed`")
    private Integer mixSpeed;
    @ApiModelProperty("最大速度(节)")
    @TableField("`max_speed`")
    private Integer maxSpeed;
    @ApiModelProperty("持续时间(分)")
    @TableField("`duration`")
    private BigDecimal duration;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
    @ApiModelProperty("通航环境id")
    @TableField("`env_id`")
    private Integer envId;
    @ApiModelProperty("通航类型id")
    @TableField("`env_type`")
    private Integer envType;
    @ApiModelProperty("1启用 0未启用")
    @TableField("`status`")
    private Boolean status;
}
