package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
* <p>
* 通航环境类型
* </p>
* <AUTHOR>
* @since 2023-04-24
*/
@Data
@Accessors(chain = true)
@TableName("navigation_env_type")
@ApiModel(value = "NavigationEnvType对象", description = "通航环境类型")
public class NavigationEnvType {

    @ApiModelProperty("通航环境类型id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("类型名称")
    @TableField("name")
    private String name;
    @ApiModelProperty("类型key")
    @TableField("env_key")
    private String envKey;
    @ApiModelProperty("父级id,默认0")
    @TableField("parentid")
    private Integer parentid;
    @ApiModelProperty("是不是父节点,默认不是0")
    @TableField("is_parent")
    private Byte isParent;
    @ApiModelProperty("状态（1正常 0停用）")
    @TableField("status")
    private Byte status;
    @ApiModelProperty("图标")
    @TableField("icon")
    private String icon;
    @ApiModelProperty("创建者")
    @TableField("create_by")
    private String createBy;
    @ApiModelProperty("创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新者")
    @TableField("update_by")
    private String updateBy;
    @ApiModelProperty("更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;
    @ApiModelProperty("排序")
    @TableField("sort")
    private Integer sort;
    @ApiModelProperty("逻辑删除 1删除 0未删除")
    @TableField("del")
    @TableLogic
    private Boolean del;
    @ApiModelProperty("是否是电子围栏")
    @TableField("`is_fence`")
    private Boolean isFence;
}
