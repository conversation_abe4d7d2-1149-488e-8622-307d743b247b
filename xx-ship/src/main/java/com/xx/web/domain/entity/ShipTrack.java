package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* <p>
*
* </p>
* <AUTHOR>
* @since 2023-03-17
*/
@Data
@Accessors(chain = true)
@TableName("ship_track")
@ApiModel(value = "ShipTrack对象", description = "")
public class ShipTrack {

    @TableId(value = "track_id", type = IdType.AUTO)
    private Long trackId;
    @ApiModelProperty("唯一标识")
    @TableField("id")
    private String id;
    @ApiModelProperty("船名")
    @TableField("name")
    private String name;
    @ApiModelProperty("更新时间")
    @TableField("time")
    private Long time;
    @ApiModelProperty("数据版本")
    @TableField("version")
    @Version
    private Long version;
    @ApiModelProperty("经度")
    @TableField("lon")
    private Object lon;
    @ApiModelProperty("纬度")
    @TableField("lat")
    private Object lat;
    @ApiModelProperty("航速")
    @TableField("sog")
    private Double sog;
    @ApiModelProperty("航向")
    @TableField("cog")
    private Double cog;
    @ApiModelProperty("艏向")
    @TableField("heading")
    private Double heading;
    @ApiModelProperty("英文船名")
    @TableField("english_name")
    private String englishName;
    @ApiModelProperty("timeout")
    @TableField("timeout")
    private Integer timeout;
    @ApiModelProperty("alarms")
    @TableField("alarms")
    private Integer alarms;
    @ApiModelProperty("region")
    @TableField("region")
    private String region;
    @ApiModelProperty("fileId")
    @TableField("file_id")
    private String fileId;
    @ApiModelProperty("shipNo")
    @TableField("ship_no")
    private String shipNo;
    @ApiModelProperty("tags")
    @TableField("tags")
    private Long tags;
    @ApiModelProperty("track")
    @TableField("track")
    private Long track;
}
