package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
* <p>
* 标绘
* </p>
* <AUTHOR>
* @since 2023-04-25
*/
@Data
@Accessors(chain = true)
@TableName("plot")
@ApiModel(value = "Plot对象", description = "标绘")
public class Plot {

    @ApiModelProperty("标绘id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("标绘名称")
    @TableField("name")
    private String name;
    @ApiModelProperty("标绘类型")
    @TableField("plot_type")
    private Integer plotType;
    @ApiModelProperty("状态（1正常 0停用）")
    @TableField("status")
    private Byte status;
    @ApiModelProperty("图形类型")
    @TableField("graph_type")
    private Byte graphType;
    @ApiModelProperty("边框类型")
    @TableField("border_type")
    private Byte borderType;
    @ApiModelProperty("边框颜色")
    @TableField("border_color")
    private String borderColor;
    @ApiModelProperty("边框宽度")
    @TableField("border_width")
    private Byte borderWidth;
    @ApiModelProperty("填充颜色")
    @TableField("fill_color")
    private String fillColor;
    @ApiModelProperty("图标")
    @TableField("icon")
    private String icon;
    @ApiModelProperty("区域")
    @TableField("regions")
    private String regions;
    @ApiModelProperty("创建者")
    @TableField("create_by")
    private String createBy;
    @ApiModelProperty("创建时间")
    @TableField(value = "gmt_create", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新者")
    @TableField("update_by")
    private String updateBy;
    @ApiModelProperty("更新时间")
    @TableField(value = "gmt_modified", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;
    @ApiModelProperty("等级")
    @TableField("lv")
    private Byte lv;
    @ApiModelProperty("逻辑删除")
    @TableField("del")
    @TableLogic
    private Boolean del;
}
