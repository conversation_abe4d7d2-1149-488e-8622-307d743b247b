package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
* <p>
* 黑名单
* </p>
* <AUTHOR>
* @since 2023-05-24
*/
@Data
@Accessors(chain = true)
@TableName("black_list")
@ApiModel(value = "BlackList对象", description = "黑名单")
public class BlackList implements Serializable {

    @ApiModelProperty("黑名单记录ID")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("船名")
    @TableField("`ship_name`")
    private String shipName;
    @ApiModelProperty("船舶MMSI号码")
    @TableField("`mmsi`")
    private String mmsi;
    @ApiModelProperty("类型1有过历史违规行径（历史违规行为，并经过人工确认）2三无船（未开启AIS船舶）3异常行为船4移锚走锚船 5人工设置黑名单船舶（人工设置风险船舶）0其他")
    @TableField("`type`")
    private Byte type;
    @ApiModelProperty("黑名单生效时间")
    @TableField("`start_time`")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime startTime;
    @ApiModelProperty("黑名单失效时间")
    @TableField("`end_time`")
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime endTime;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
}
