package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
* <p>
* 在港,锚地等船舶
* </p>
* <AUTHOR>
* @since 2023-04-25
*/
@Data
@Accessors(chain = true)
@TableName("env_ship")
@ApiModel(value = "EnvShip对象", description = "在港,锚地等船舶")
public class EnvShip {

    @ApiModelProperty("主键")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("ship id")
    @TableField("`ship_id`")
    private Integer shipId;
    @ApiModelProperty("通航环境id")
    @TableField("`env_id`")
    private Integer envId;
    @ApiModelProperty("通航类型id")
    @TableField("`env_type`")
    private Integer envType;
    @ApiModelProperty("mmsi")
    @TableField("`mmsi`")
    private String mmsi;
}
