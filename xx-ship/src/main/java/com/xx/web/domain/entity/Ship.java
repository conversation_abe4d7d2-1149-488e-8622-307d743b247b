package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* <p>
* ship
* </p>
* <AUTHOR>
* @since 2023-03-17
*/
@Data
@Accessors(chain = true)
@TableName("ship")
@ApiModel(value = "Ship对象", description = "ship")
public class Ship {

    @ApiModelProperty("唯一标识")
    @TableId(value = "id", type = IdType.INPUT)
    private String id;
    @ApiModelProperty("船名")
    @TableField("name")
    private String name;
    @ApiModelProperty("更新时间")
    @TableField("time")
    private Long time;
    @ApiModelProperty("数据版本")
    @TableField("version")
    @Version
    private Integer version;
    @ApiModelProperty("经度")
    @TableField("lon")
    private Object lon;
    @ApiModelProperty("纬度")
    @TableField("lat")
    private Object lat;
    @ApiModelProperty("航速")
    @TableField("sog")
    private Double sog;
    @ApiModelProperty("航向")
    @TableField("cog")
    private Double cog;
    @ApiModelProperty("转向率")
    @TableField("rot")
    private Double rot;
    @ApiModelProperty("艏向")
    @TableField("heading")
    private Double heading;
    @ApiModelProperty("mmsi")
    @TableField("mmsi")
    private Integer mmsi;
    @ApiModelProperty("IMO")
    @TableField("imo")
    private Integer imo;
    @ApiModelProperty("呼号")
    @TableField("callsign")
    private String callsign;
    @ApiModelProperty("英文船名")
    @TableField("english_name")
    private String englishName;
    @ApiModelProperty("中文船名")
    @TableField("chinese_name")
    private String chineseName;
    @ApiModelProperty("船舶类型")
    @TableField("ship_type")
    private Integer shipType;
    @ApiModelProperty("船宽")
    @TableField("width")
    private Double width;
    @ApiModelProperty("船长")
    @TableField("length")
    private Double length;
    @ApiModelProperty("船台到船艏距离，单位：米")
    @TableField("to_bow")
    private Double toBow;
    @ApiModelProperty("船台到船尾距离")
    @TableField("to_stern")
    private Double toStern;
    @ApiModelProperty("船台到左舷距离")
    @TableField("to_port")
    private Double toPort;
    @ApiModelProperty("船台到右舷距离")
    @TableField("to_starboard")
    private Double toStarboard;
    @ApiModelProperty("ais信号类型")
    @TableField("ais_ship_type")
    private Integer aisShipType;
    @ApiModelProperty("档案匹配类型")
    @TableField("file_ship_type")
    private Integer fileShipType;
}
