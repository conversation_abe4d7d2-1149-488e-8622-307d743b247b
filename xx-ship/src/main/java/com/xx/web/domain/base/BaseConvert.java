package com.xx.web.domain.base;

import org.mapstruct.InheritConfiguration;
import org.mapstruct.InheritInverseConfiguration;

import java.util.List;

/**
 * 1、SOURCE与TARGET中属性名相同的默认映射（如这两个都有name属性）
 * 2、SOURCE与TARGET中属性名不同的，需要通过@Mappings({@Mapping()})明确关系来形成映射（如sex对应gender）
 * 3、形成映射关系的属性类型不同的，需要通过表达式转换数据类型（如Date对应String）
 * 4、无映射关系属性被忽略（如UserEntity的password）
 *
 * <AUTHOR>
 * @since v1.0.0
 */
//@Mapper
public interface BaseConvert<SOURCE, TARGET> {

    /**
     * 映射同名属性
     * @param source 源对象
     */
    TARGET source2Target(SOURCE source);

    /**
     * 反向，映射同名属性
     */
    @InheritInverseConfiguration(name = "source2Target")
    SOURCE target2Source(TARGET target);

    /**
     * 映射同名属性，集合形式
     * @param sources 源对象集合
     */
    @InheritConfiguration(name = "source2Target")
    List<TARGET> sources2Targets(List<SOURCE> sources);

    /**
     * 反向，映射同名属性，集合形式
     * @param targets 目标对象集合
     */
    @InheritConfiguration(name = "target2Source")
    List<SOURCE> targets2Sources(List<TARGET> targets);

}
