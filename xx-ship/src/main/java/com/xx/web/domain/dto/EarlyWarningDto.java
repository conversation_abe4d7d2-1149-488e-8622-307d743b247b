package com.xx.web.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 预警信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-26
 */
@Data
@Accessors(chain = true)
public class EarlyWarningDto {

    private String mmsi;
    private String shipName;
    private String eventContent;
    private String address;
    private BigDecimal lon;
    private BigDecimal lat;
    private Integer envId;
    private Integer envType;
    private String videoUrl;
    private String remark;
    private Long endTime;
    private Long warningTime;
    private Byte type;
    private List<Set<Object>> track;

}
