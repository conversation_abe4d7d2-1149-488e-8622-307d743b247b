package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xx.interfaces.validatedGroups.Insert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
* <p>
* 预警信息处理
* </p>
* <AUTHOR>
* @since 2023-04-25
*/
@Data
@Accessors(chain = true)
@TableName("early_warning_dispose")
@ApiModel(value = "EarlyWarningDispose对象", description = "")
public class EarlyWarningDispose {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("预警id")
    @TableField("`early_id`")
    @NotNull(groups = Insert.class)
    private Integer earlyId;
    @ApiModelProperty("异常类型")
    @TableField("`excepition_type`")
    private String excepitionType;
    @ApiModelProperty("处置方式")
    @TableField("`handling_way`")
    private String handlingWay;
    @ApiModelProperty("详细描述")
    @TableField("`detail`")
    private String detail;
    @ApiModelProperty("处置措施")
    @TableField("`treatment_measure`")
    private String treatmentMeasure;
    @ApiModelProperty("纠正情况")
    @TableField("`correct_the_situation`")
    private String correctTheSituation;
    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;
    @ApiModelProperty("是否处罚1处罚 0未处罚")
    @TableField("`punish`")
    private Boolean punish;
    @ApiModelProperty("附件最多5个,单个附件不超过2m")
    @TableField("`accessory`")
    private String accessory;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
}
