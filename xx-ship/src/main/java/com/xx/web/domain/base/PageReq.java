package com.xx.web.domain.base;

/**
 * @description:
 * @author: xx
 * @Date 2023/7/22 11:18
 * @version: 1.0
 */

public class PageReq {

    private int page;

    private int pageSize = 20;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        // Validate pageSize to ensure it's within a reasonable range
        int maxPageSize = 100; // Set a maximum page size as an example (adjust this value as needed)
        if (pageSize <= 0 || pageSize > maxPageSize) {
            pageSize = maxPageSize; // Adjust to a sensible default value
        }
        this.pageSize = pageSize;
    }
}
