package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <p>
 * 通航环境
 * </p>
 * <AUTHOR>
 * @since 2023-05-25
 */
@Data
@TableName("navigation_env")
@ApiModel(value = "NavigationEnv对象", description = "通航环境")
public class NavigationEnv {

    @ApiModelProperty("通航环境id")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("环境名称")
    @TableField("`name`")
    private String name;

    @ApiModelProperty("通航环境类型")
    @TableField("`env_type`")
    private String envType;

    @ApiModelProperty("通航环境类型id")
    @TableField("`env_type_id`")
    private Integer envTypeId;

    @ApiModelProperty("组")
    @TableField("`group`")
    private String group;

    @ApiModelProperty("状态（1正常 0停用）")
    @TableField("`status`")
    private Byte status;

    @ApiModelProperty("边框类型")
    @TableField("`border_type`")

    private Byte borderType;
    @ApiModelProperty("边框颜色")
    @TableField("`border_color`")
    private String borderColor;

    @ApiModelProperty("边框宽度")
    @TableField("`border_width`")
    private Byte borderWidth;

    @ApiModelProperty("填充颜色")
    @TableField("`fill_color`")
    private String fillColor;

    @ApiModelProperty("图标")
    @TableField("`icon`")
    private String icon;

    @ApiModelProperty("区域")
    @TableField("`regions`")
    private String regions;

    @ApiModelProperty("区域2")
    @TableField("`regions2`")
    private String regions2;

    @ApiModelProperty("区域显示")
    @TableField("`display`")
    private Boolean display;

    @ApiModelProperty("创建者")
    @TableField(value = "`create_by`", fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;

    @ApiModelProperty("更新者")
    @TableField(value = "`update_by`", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;

    @ApiModelProperty("备注")
    @TableField("`remark`")
    private String remark;

    @ApiModelProperty("等级")
    @TableField("`lv`")
    private Byte lv;

    @ApiModelProperty("逻辑删除")
    @TableField("`del`")
    @TableLogic
    private Boolean del;

    @ApiModelProperty("0其他,1表示册子,2表示金塘")
    @TableField("`place`")
    private String place;
}
