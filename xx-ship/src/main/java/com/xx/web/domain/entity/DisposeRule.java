package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 处置规则
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
@Accessors(chain = true)
@TableName("dispose_rule")
@ApiModel(value = "DisposeRule对象", description = "处置规则")
public class DisposeRule {

    @ApiModelProperty("处置规则ID")
    @TableId(value = "`id`", type = IdType.AUTO)
    private Integer id;
    @ApiModelProperty("0不预警 1预警")
    @TableField("`warning`")
    private Boolean warning;
    @ApiModelProperty("0VHF不喊话 1喊话")
    @TableField("`vhf`")
    private Boolean vhf;
    @ApiModelProperty("VHF喊话内容")
    @TableField("`vhf_content`")
    private String vhfContent;
    @ApiModelProperty("0喇叭不喊话 1喊话")
    @TableField("`loudspeaker`")
    private Boolean loudspeaker;
    @ApiModelProperty("喇叭喊话内容")
    @TableField("`loudspeaker_content`")
    private String loudspeakerContent;
    @ApiModelProperty("0探照灯启动 1不启动")
    @TableField("`searchlight`")
    private Boolean searchlight;
    @ApiModelProperty("开始时间")
    @TableField("`searchlight_start`")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime searchlightStart;
    @ApiModelProperty("结束时间")
    @TableField("`searchlight_end`")
    @JsonFormat(pattern = "HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "HH:mm:ss")
    private LocalTime searchlightEnd;
    @ApiModelProperty("0ais报文 1不报文")
    @TableField("`ais`")
    private Boolean ais;
    @ApiModelProperty("ais报文内容")
    @TableField("`ais_content`")
    private String aisContent;
    @ApiModelProperty("0不发送短信 1发送短信")
    @TableField("`sms`")
    private Boolean sms;
    @ApiModelProperty("短信模板")
    @TableField("`sms_template`")
    private String smsTemplate;
    @ApiModelProperty("0不移交 1移交")
    @TableField("`hand_over`")
    private Boolean handOver;
    @ApiModelProperty("0事件不记录 1记录")
    @TableField("`event_record`")
    private Boolean eventRecord;
    @ApiModelProperty("0航迹不记录 1记录")
    @TableField("`track_record`")
    private Boolean trackRecord;
    @ApiModelProperty("0视频不记录 1记录")
    @TableField("`video_record`")
    private Boolean videoRecord;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
    @ApiModelProperty("状态 true启用,false关闭")
    @TableField("`status`")
    private Boolean status;
    @ApiModelProperty("通航环境类型key")
    @TableField("`env_type`")
    private String envType;

    @ApiModelProperty("有效开始时间")
    @TableField("`start_date`")
    private LocalDateTime startDate;
    @ApiModelProperty("有效结束时间")
    @TableField("`end_date`")
    private LocalDateTime endDate;
    @ApiModelProperty("每天有效开始时间")
    @TableField("`start_time_day`")
    private LocalTime startTimeDay;
    @ApiModelProperty("每天有效结束时间时间")
    @TableField("`end_time_day`")
    private LocalTime endTimeDay;

}
