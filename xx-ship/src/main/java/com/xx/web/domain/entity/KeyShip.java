package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* <p>
*
* </p>
* <AUTHOR>
* @since 2023-03-17
*/
@Data
@Accessors(chain = true)
@TableName("key_ship")
@ApiModel(value = "KeyShip对象", description = "")
public class KeyShip {

    @ApiModelProperty("主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("船舶id")
    @TableField("ship_id")
    private Integer shipId;
    @ApiModelProperty("mmsi")
    @TableField("mmsi")
    private Integer mmsi;
    @ApiModelProperty("英文名")
    @TableField("english_name")
    private String englishName;
    @ApiModelProperty("中文名")
    @TableField("chinese_name")
    private String chineseName;
    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;
}
