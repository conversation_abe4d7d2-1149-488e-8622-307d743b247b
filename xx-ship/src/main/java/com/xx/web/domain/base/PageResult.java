package com.xx.web.domain.base;

import lombok.Data;
import org.springframework.data.domain.Pageable;
import java.util.List;

@Data
public class PageResult<T> {
    private List<T> data;
    private long totalCount;
    private Pageable pageable;

    public PageResult(List<T> data, long totalCount, Pageable pageable) {
        this.data = data;
        this.totalCount = totalCount;
        this.pageable = pageable;
    }

}
