package com.xx.web.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <p>
*
* </p>
* <AUTHOR>
* @since 2023-04-25
*/
@Data
@Accessors(chain = true)
@TableName("harbor_log")
@ApiModel(value = "HarborLog对象", description = "")
public class HarborLog {

    @TableId(value = "`id`", type = IdType.AUTO)
    private Long id;
    @ApiModelProperty("船舶id")
    @TableField("`ship_id`")
    private Integer shipId;
    @ApiModelProperty("mmsi")
    @TableField("`mmsi`")
    private String mmsi;
    @ApiModelProperty("1进0出")
    @TableField("`type`")
    private Boolean type;
    @ApiModelProperty("通航环境id")
    @TableField("`env_id`")
    private Integer envId;
    @ApiModelProperty("通航环境类型id")
    @TableField("`env_type`")
    private Integer envType;
    @ApiModelProperty("创建时间")
    @TableField(value = "`gmt_create`", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtCreate;
    @ApiModelProperty("更新时间")
    @TableField(value = "`gmt_modified`", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    private LocalDateTime gmtModified;
    @TableField("`remark`")
    private String remark;
    @ApiModelProperty("经度")
    @TableField("`lon`")
    private BigDecimal lon;
    @ApiModelProperty("纬度")
    @TableField("`lat`")
    private BigDecimal lat;
}
