// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto/ship.proto

package com.xx.web.proto;

import com.google.protobuf.AbstractMessageLite;
import com.google.protobuf.GeneratedMessageV3;

public final class ShipOuterClass {
  private ShipOuterClass() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ShipOrBuilder extends
      // @@protoc_insertion_point(interface_extends:litong.Ship)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    String getId();
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>sint64 time = 3;</code>
     * @return The time.
     */
    long getTime();

    /**
     * <code>uint64 version = 4;</code>
     * @return The version.
     */
    long getVersion();

    /**
     * <code>double lon = 5;</code>
     * @return The lon.
     */
    double getLon();

    /**
     * <code>double lat = 6;</code>
     * @return The lat.
     */
    double getLat();

    /**
     * <code>float sog = 7;</code>
     * @return The sog.
     */
    float getSog();

    /**
     * <code>float cog = 8;</code>
     * @return The cog.
     */
    float getCog();

    /**
     * <code>float rot = 9;</code>
     * @return The rot.
     */
    float getRot();

    /**
     * <code>float heading = 10;</code>
     * @return The heading.
     */
    float getHeading();

    /**
     * <code>uint32 mmsi = 11;</code>
     * @return The mmsi.
     */
    int getMmsi();

    /**
     * <code>uint32 imo = 12;</code>
     * @return The imo.
     */
    int getImo();

    /**
     * <code>string callsign = 13;</code>
     * @return The callsign.
     */
    String getCallsign();
    /**
     * <code>string callsign = 13;</code>
     * @return The bytes for callsign.
     */
    com.google.protobuf.ByteString
        getCallsignBytes();

    /**
     * <code>string english_name = 14;</code>
     * @return The englishName.
     */
    String getEnglishName();
    /**
     * <code>string english_name = 14;</code>
     * @return The bytes for englishName.
     */
    com.google.protobuf.ByteString
        getEnglishNameBytes();

    /**
     * <code>string chinese_name = 15;</code>
     * @return The chineseName.
     */
    String getChineseName();
    /**
     * <code>string chinese_name = 15;</code>
     * @return The bytes for chineseName.
     */
    com.google.protobuf.ByteString
        getChineseNameBytes();

    /**
     * <code>uint32 ship_type = 16;</code>
     * @return The shipType.
     */
    int getShipType();

    /**
     * <code>uint32 ais_ship_type = 42;</code>
     * @return The aisShipType.
     */
    int getAisShipType();

    /**
     * <code>uint32 file_ship_type = 43;</code>
     * @return The fileShipType.
     */
    int getFileShipType();

    /**
     * <code>float width = 17;</code>
     * @return The width.
     */
    float getWidth();

    /**
     * <code>float length = 18;</code>
     * @return The length.
     */
    float getLength();

    /**
     * <code>float to_bow = 19;</code>
     * @return The toBow.
     */
    float getToBow();

    /**
     * <code>float to_stern = 20;</code>
     * @return The toStern.
     */
    float getToStern();

    /**
     * <code>float to_port = 21;</code>
     * @return The toPort.
     */
    float getToPort();

    /**
     * <code>float to_starboard = 22;</code>
     * @return The toStarboard.
     */
    float getToStarboard();

    /**
     * <code>uint32 timeout = 23;</code>
     * @return The timeout.
     */
    int getTimeout();

    /**
     * <code>string region = 24;</code>
     * @return The region.
     */
    String getRegion();
    /**
     * <code>string region = 24;</code>
     * @return The bytes for region.
     */
    com.google.protobuf.ByteString
        getRegionBytes();

    /**
     * <code>float draught = 25;</code>
     * @return The draught.
     */
    float getDraught();

    /**
     * <code>sint64 eta = 26;</code>
     * @return The eta.
     */
    long getEta();

    /**
     * <code>string destination = 27;</code>
     * @return The destination.
     */
    String getDestination();
    /**
     * <code>string destination = 27;</code>
     * @return The bytes for destination.
     */
    com.google.protobuf.ByteString
        getDestinationBytes();

    /**
     * <code>uint32 status = 28;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <code>string file_id = 40;</code>
     * @return The fileId.
     */
    String getFileId();
    /**
     * <code>string file_id = 40;</code>
     * @return The bytes for fileId.
     */
    com.google.protobuf.ByteString
        getFileIdBytes();

    /**
     * <code>string ship_no = 41;</code>
     * @return The shipNo.
     */
    String getShipNo();
    /**
     * <code>string ship_no = 41;</code>
     * @return The bytes for shipNo.
     */
    com.google.protobuf.ByteString
        getShipNoBytes();

    /**
     * <code>uint32 alarms = 52;</code>
     * @return The alarms.
     */
    int getAlarms();

    /**
     * <code>uint64 tags = 53;</code>
     * @return The tags.
     */
    long getTags();

    /**
     * <code>uint32 special_tag = 54;</code>
     * @return The specialTag.
     */
    int getSpecialTag();

    /**
     * <code>uint64 tags_two = 55;</code>
     * @return The tagsTwo.
     */
    long getTagsTwo();

    /**
     * <code>uint32 origins = 56;</code>
     * @return The origins.
     */
    int getOrigins();

    /**
     * <code>uint64 track = 30;</code>
     * @return The track.
     */
    long getTrack();

    /**
     * <code>uint32 reliability = 70;</code>
     * @return The reliability.
     */
    int getReliability();
  }
  /**
   * Protobuf type {@code litong.Ship}
   */
  public static final class Ship extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:litong.Ship)
      ShipOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Ship.newBuilder() to construct.
    private Ship(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Ship() {
      id_ = "";
      name_ = "";
      callsign_ = "";
      englishName_ = "";
      chineseName_ = "";
      region_ = "";
      destination_ = "";
      fileId_ = "";
      shipNo_ = "";
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new Ship();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ShipOuterClass.internal_static_litong_Ship_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ShipOuterClass.internal_static_litong_Ship_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Ship.class, Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    @Override
    public String getId() {
      Object ref = id_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @Override
    public String getName() {
      Object ref = name_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIME_FIELD_NUMBER = 3;
    private long time_ = 0L;
    /**
     * <code>sint64 time = 3;</code>
     * @return The time.
     */
    @Override
    public long getTime() {
      return time_;
    }

    public static final int VERSION_FIELD_NUMBER = 4;
    private long version_ = 0L;
    /**
     * <code>uint64 version = 4;</code>
     * @return The version.
     */
    @Override
    public long getVersion() {
      return version_;
    }

    public static final int LON_FIELD_NUMBER = 5;
    private double lon_ = 0D;
    /**
     * <code>double lon = 5;</code>
     * @return The lon.
     */
    @Override
    public double getLon() {
      return lon_;
    }

    public static final int LAT_FIELD_NUMBER = 6;
    private double lat_ = 0D;
    /**
     * <code>double lat = 6;</code>
     * @return The lat.
     */
    @Override
    public double getLat() {
      return lat_;
    }

    public static final int SOG_FIELD_NUMBER = 7;
    private float sog_ = 0F;
    /**
     * <code>float sog = 7;</code>
     * @return The sog.
     */
    @Override
    public float getSog() {
      return sog_;
    }

    public static final int COG_FIELD_NUMBER = 8;
    private float cog_ = 0F;
    /**
     * <code>float cog = 8;</code>
     * @return The cog.
     */
    @Override
    public float getCog() {
      return cog_;
    }

    public static final int ROT_FIELD_NUMBER = 9;
    private float rot_ = 0F;
    /**
     * <code>float rot = 9;</code>
     * @return The rot.
     */
    @Override
    public float getRot() {
      return rot_;
    }

    public static final int HEADING_FIELD_NUMBER = 10;
    private float heading_ = 0F;
    /**
     * <code>float heading = 10;</code>
     * @return The heading.
     */
    @Override
    public float getHeading() {
      return heading_;
    }

    public static final int MMSI_FIELD_NUMBER = 11;
    private int mmsi_ = 0;
    /**
     * <code>uint32 mmsi = 11;</code>
     * @return The mmsi.
     */
    @Override
    public int getMmsi() {
      return mmsi_;
    }

    public static final int IMO_FIELD_NUMBER = 12;
    private int imo_ = 0;
    /**
     * <code>uint32 imo = 12;</code>
     * @return The imo.
     */
    @Override
    public int getImo() {
      return imo_;
    }

    public static final int CALLSIGN_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile Object callsign_ = "";
    /**
     * <code>string callsign = 13;</code>
     * @return The callsign.
     */
    @Override
    public String getCallsign() {
      Object ref = callsign_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        callsign_ = s;
        return s;
      }
    }
    /**
     * <code>string callsign = 13;</code>
     * @return The bytes for callsign.
     */
    @Override
    public com.google.protobuf.ByteString
        getCallsignBytes() {
      Object ref = callsign_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        callsign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENGLISH_NAME_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile Object englishName_ = "";
    /**
     * <code>string english_name = 14;</code>
     * @return The englishName.
     */
    @Override
    public String getEnglishName() {
      Object ref = englishName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        englishName_ = s;
        return s;
      }
    }
    /**
     * <code>string english_name = 14;</code>
     * @return The bytes for englishName.
     */
    @Override
    public com.google.protobuf.ByteString
        getEnglishNameBytes() {
      Object ref = englishName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        englishName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CHINESE_NAME_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile Object chineseName_ = "";
    /**
     * <code>string chinese_name = 15;</code>
     * @return The chineseName.
     */
    @Override
    public String getChineseName() {
      Object ref = chineseName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        chineseName_ = s;
        return s;
      }
    }
    /**
     * <code>string chinese_name = 15;</code>
     * @return The bytes for chineseName.
     */
    @Override
    public com.google.protobuf.ByteString
        getChineseNameBytes() {
      Object ref = chineseName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        chineseName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHIP_TYPE_FIELD_NUMBER = 16;
    private int shipType_ = 0;
    /**
     * <code>uint32 ship_type = 16;</code>
     * @return The shipType.
     */
    @Override
    public int getShipType() {
      return shipType_;
    }

    public static final int AIS_SHIP_TYPE_FIELD_NUMBER = 42;
    private int aisShipType_ = 0;
    /**
     * <code>uint32 ais_ship_type = 42;</code>
     * @return The aisShipType.
     */
    @Override
    public int getAisShipType() {
      return aisShipType_;
    }

    public static final int FILE_SHIP_TYPE_FIELD_NUMBER = 43;
    private int fileShipType_ = 0;
    /**
     * <code>uint32 file_ship_type = 43;</code>
     * @return The fileShipType.
     */
    @Override
    public int getFileShipType() {
      return fileShipType_;
    }

    public static final int WIDTH_FIELD_NUMBER = 17;
    private float width_ = 0F;
    /**
     * <code>float width = 17;</code>
     * @return The width.
     */
    @Override
    public float getWidth() {
      return width_;
    }

    public static final int LENGTH_FIELD_NUMBER = 18;
    private float length_ = 0F;
    /**
     * <code>float length = 18;</code>
     * @return The length.
     */
    @Override
    public float getLength() {
      return length_;
    }

    public static final int TO_BOW_FIELD_NUMBER = 19;
    private float toBow_ = 0F;
    /**
     * <code>float to_bow = 19;</code>
     * @return The toBow.
     */
    @Override
    public float getToBow() {
      return toBow_;
    }

    public static final int TO_STERN_FIELD_NUMBER = 20;
    private float toStern_ = 0F;
    /**
     * <code>float to_stern = 20;</code>
     * @return The toStern.
     */
    @Override
    public float getToStern() {
      return toStern_;
    }

    public static final int TO_PORT_FIELD_NUMBER = 21;
    private float toPort_ = 0F;
    /**
     * <code>float to_port = 21;</code>
     * @return The toPort.
     */
    @Override
    public float getToPort() {
      return toPort_;
    }

    public static final int TO_STARBOARD_FIELD_NUMBER = 22;
    private float toStarboard_ = 0F;
    /**
     * <code>float to_starboard = 22;</code>
     * @return The toStarboard.
     */
    @Override
    public float getToStarboard() {
      return toStarboard_;
    }

    public static final int TIMEOUT_FIELD_NUMBER = 23;
    private int timeout_ = 0;
    /**
     * <code>uint32 timeout = 23;</code>
     * @return The timeout.
     */
    @Override
    public int getTimeout() {
      return timeout_;
    }

    public static final int REGION_FIELD_NUMBER = 24;
    @SuppressWarnings("serial")
    private volatile Object region_ = "";
    /**
     * <code>string region = 24;</code>
     * @return The region.
     */
    @Override
    public String getRegion() {
      Object ref = region_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        region_ = s;
        return s;
      }
    }
    /**
     * <code>string region = 24;</code>
     * @return The bytes for region.
     */
    @Override
    public com.google.protobuf.ByteString
        getRegionBytes() {
      Object ref = region_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        region_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DRAUGHT_FIELD_NUMBER = 25;
    private float draught_ = 0F;
    /**
     * <code>float draught = 25;</code>
     * @return The draught.
     */
    @Override
    public float getDraught() {
      return draught_;
    }

    public static final int ETA_FIELD_NUMBER = 26;
    private long eta_ = 0L;
    /**
     * <code>sint64 eta = 26;</code>
     * @return The eta.
     */
    @Override
    public long getEta() {
      return eta_;
    }

    public static final int DESTINATION_FIELD_NUMBER = 27;
    @SuppressWarnings("serial")
    private volatile Object destination_ = "";
    /**
     * <code>string destination = 27;</code>
     * @return The destination.
     */
    @Override
    public String getDestination() {
      Object ref = destination_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        destination_ = s;
        return s;
      }
    }
    /**
     * <code>string destination = 27;</code>
     * @return The bytes for destination.
     */
    @Override
    public com.google.protobuf.ByteString
        getDestinationBytes() {
      Object ref = destination_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        destination_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int STATUS_FIELD_NUMBER = 28;
    private int status_ = 0;
    /**
     * <code>uint32 status = 28;</code>
     * @return The status.
     */
    @Override
    public int getStatus() {
      return status_;
    }

    public static final int FILE_ID_FIELD_NUMBER = 40;
    @SuppressWarnings("serial")
    private volatile Object fileId_ = "";
    /**
     * <code>string file_id = 40;</code>
     * @return The fileId.
     */
    @Override
    public String getFileId() {
      Object ref = fileId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        fileId_ = s;
        return s;
      }
    }
    /**
     * <code>string file_id = 40;</code>
     * @return The bytes for fileId.
     */
    @Override
    public com.google.protobuf.ByteString
        getFileIdBytes() {
      Object ref = fileId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        fileId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHIP_NO_FIELD_NUMBER = 41;
    @SuppressWarnings("serial")
    private volatile Object shipNo_ = "";
    /**
     * <code>string ship_no = 41;</code>
     * @return The shipNo.
     */
    @Override
    public String getShipNo() {
      Object ref = shipNo_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        shipNo_ = s;
        return s;
      }
    }
    /**
     * <code>string ship_no = 41;</code>
     * @return The bytes for shipNo.
     */
    @Override
    public com.google.protobuf.ByteString
        getShipNoBytes() {
      Object ref = shipNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        shipNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ALARMS_FIELD_NUMBER = 52;
    private int alarms_ = 0;
    /**
     * <code>uint32 alarms = 52;</code>
     * @return The alarms.
     */
    @Override
    public int getAlarms() {
      return alarms_;
    }

    public static final int TAGS_FIELD_NUMBER = 53;
    private long tags_ = 0L;
    /**
     * <code>uint64 tags = 53;</code>
     * @return The tags.
     */
    @Override
    public long getTags() {
      return tags_;
    }

    public static final int SPECIAL_TAG_FIELD_NUMBER = 54;
    private int specialTag_ = 0;
    /**
     * <code>uint32 special_tag = 54;</code>
     * @return The specialTag.
     */
    @Override
    public int getSpecialTag() {
      return specialTag_;
    }

    public static final int TAGS_TWO_FIELD_NUMBER = 55;
    private long tagsTwo_ = 0L;
    /**
     * <code>uint64 tags_two = 55;</code>
     * @return The tagsTwo.
     */
    @Override
    public long getTagsTwo() {
      return tagsTwo_;
    }

    public static final int ORIGINS_FIELD_NUMBER = 56;
    private int origins_ = 0;
    /**
     * <code>uint32 origins = 56;</code>
     * @return The origins.
     */
    @Override
    public int getOrigins() {
      return origins_;
    }

    public static final int TRACK_FIELD_NUMBER = 30;
    private long track_ = 0L;
    /**
     * <code>uint64 track = 30;</code>
     * @return The track.
     */
    @Override
    public long getTrack() {
      return track_;
    }

    public static final int RELIABILITY_FIELD_NUMBER = 70;
    private int reliability_ = 0;
    /**
     * <code>uint32 reliability = 70;</code>
     * @return The reliability.
     */
    @Override
    public int getReliability() {
      return reliability_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!GeneratedMessageV3.isStringEmpty(id_)) {
        GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (!GeneratedMessageV3.isStringEmpty(name_)) {
        GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (time_ != 0L) {
        output.writeSInt64(3, time_);
      }
      if (version_ != 0L) {
        output.writeUInt64(4, version_);
      }
      if (Double.doubleToRawLongBits(lon_) != 0) {
        output.writeDouble(5, lon_);
      }
      if (Double.doubleToRawLongBits(lat_) != 0) {
        output.writeDouble(6, lat_);
      }
      if (Float.floatToRawIntBits(sog_) != 0) {
        output.writeFloat(7, sog_);
      }
      if (Float.floatToRawIntBits(cog_) != 0) {
        output.writeFloat(8, cog_);
      }
      if (Float.floatToRawIntBits(rot_) != 0) {
        output.writeFloat(9, rot_);
      }
      if (Float.floatToRawIntBits(heading_) != 0) {
        output.writeFloat(10, heading_);
      }
      if (mmsi_ != 0) {
        output.writeUInt32(11, mmsi_);
      }
      if (imo_ != 0) {
        output.writeUInt32(12, imo_);
      }
      if (!GeneratedMessageV3.isStringEmpty(callsign_)) {
        GeneratedMessageV3.writeString(output, 13, callsign_);
      }
      if (!GeneratedMessageV3.isStringEmpty(englishName_)) {
        GeneratedMessageV3.writeString(output, 14, englishName_);
      }
      if (!GeneratedMessageV3.isStringEmpty(chineseName_)) {
        GeneratedMessageV3.writeString(output, 15, chineseName_);
      }
      if (shipType_ != 0) {
        output.writeUInt32(16, shipType_);
      }
      if (Float.floatToRawIntBits(width_) != 0) {
        output.writeFloat(17, width_);
      }
      if (Float.floatToRawIntBits(length_) != 0) {
        output.writeFloat(18, length_);
      }
      if (Float.floatToRawIntBits(toBow_) != 0) {
        output.writeFloat(19, toBow_);
      }
      if (Float.floatToRawIntBits(toStern_) != 0) {
        output.writeFloat(20, toStern_);
      }
      if (Float.floatToRawIntBits(toPort_) != 0) {
        output.writeFloat(21, toPort_);
      }
      if (Float.floatToRawIntBits(toStarboard_) != 0) {
        output.writeFloat(22, toStarboard_);
      }
      if (timeout_ != 0) {
        output.writeUInt32(23, timeout_);
      }
      if (!GeneratedMessageV3.isStringEmpty(region_)) {
        GeneratedMessageV3.writeString(output, 24, region_);
      }
      if (Float.floatToRawIntBits(draught_) != 0) {
        output.writeFloat(25, draught_);
      }
      if (eta_ != 0L) {
        output.writeSInt64(26, eta_);
      }
      if (!GeneratedMessageV3.isStringEmpty(destination_)) {
        GeneratedMessageV3.writeString(output, 27, destination_);
      }
      if (status_ != 0) {
        output.writeUInt32(28, status_);
      }
      if (track_ != 0L) {
        output.writeUInt64(30, track_);
      }
      if (!GeneratedMessageV3.isStringEmpty(fileId_)) {
        GeneratedMessageV3.writeString(output, 40, fileId_);
      }
      if (!GeneratedMessageV3.isStringEmpty(shipNo_)) {
        GeneratedMessageV3.writeString(output, 41, shipNo_);
      }
      if (aisShipType_ != 0) {
        output.writeUInt32(42, aisShipType_);
      }
      if (fileShipType_ != 0) {
        output.writeUInt32(43, fileShipType_);
      }
      if (alarms_ != 0) {
        output.writeUInt32(52, alarms_);
      }
      if (tags_ != 0L) {
        output.writeUInt64(53, tags_);
      }
      if (specialTag_ != 0) {
        output.writeUInt32(54, specialTag_);
      }
      if (tagsTwo_ != 0L) {
        output.writeUInt64(55, tagsTwo_);
      }
      if (origins_ != 0) {
        output.writeUInt32(56, origins_);
      }
      if (reliability_ != 0) {
        output.writeUInt32(70, reliability_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!GeneratedMessageV3.isStringEmpty(id_)) {
        size += GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (!GeneratedMessageV3.isStringEmpty(name_)) {
        size += GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (time_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(3, time_);
      }
      if (version_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, version_);
      }
      if (Double.doubleToRawLongBits(lon_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(5, lon_);
      }
      if (Double.doubleToRawLongBits(lat_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, lat_);
      }
      if (Float.floatToRawIntBits(sog_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, sog_);
      }
      if (Float.floatToRawIntBits(cog_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(8, cog_);
      }
      if (Float.floatToRawIntBits(rot_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, rot_);
      }
      if (Float.floatToRawIntBits(heading_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(10, heading_);
      }
      if (mmsi_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(11, mmsi_);
      }
      if (imo_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(12, imo_);
      }
      if (!GeneratedMessageV3.isStringEmpty(callsign_)) {
        size += GeneratedMessageV3.computeStringSize(13, callsign_);
      }
      if (!GeneratedMessageV3.isStringEmpty(englishName_)) {
        size += GeneratedMessageV3.computeStringSize(14, englishName_);
      }
      if (!GeneratedMessageV3.isStringEmpty(chineseName_)) {
        size += GeneratedMessageV3.computeStringSize(15, chineseName_);
      }
      if (shipType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, shipType_);
      }
      if (Float.floatToRawIntBits(width_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(17, width_);
      }
      if (Float.floatToRawIntBits(length_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(18, length_);
      }
      if (Float.floatToRawIntBits(toBow_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(19, toBow_);
      }
      if (Float.floatToRawIntBits(toStern_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(20, toStern_);
      }
      if (Float.floatToRawIntBits(toPort_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(21, toPort_);
      }
      if (Float.floatToRawIntBits(toStarboard_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(22, toStarboard_);
      }
      if (timeout_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(23, timeout_);
      }
      if (!GeneratedMessageV3.isStringEmpty(region_)) {
        size += GeneratedMessageV3.computeStringSize(24, region_);
      }
      if (Float.floatToRawIntBits(draught_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(25, draught_);
      }
      if (eta_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(26, eta_);
      }
      if (!GeneratedMessageV3.isStringEmpty(destination_)) {
        size += GeneratedMessageV3.computeStringSize(27, destination_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, status_);
      }
      if (track_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(30, track_);
      }
      if (!GeneratedMessageV3.isStringEmpty(fileId_)) {
        size += GeneratedMessageV3.computeStringSize(40, fileId_);
      }
      if (!GeneratedMessageV3.isStringEmpty(shipNo_)) {
        size += GeneratedMessageV3.computeStringSize(41, shipNo_);
      }
      if (aisShipType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, aisShipType_);
      }
      if (fileShipType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(43, fileShipType_);
      }
      if (alarms_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(52, alarms_);
      }
      if (tags_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(53, tags_);
      }
      if (specialTag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(54, specialTag_);
      }
      if (tagsTwo_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(55, tagsTwo_);
      }
      if (origins_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(56, origins_);
      }
      if (reliability_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(70, reliability_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Ship)) {
        return super.equals(obj);
      }
      Ship other = (Ship) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getTime()
          != other.getTime()) return false;
      if (getVersion()
          != other.getVersion()) return false;
      if (Double.doubleToLongBits(getLon())
          != Double.doubleToLongBits(
              other.getLon())) return false;
      if (Double.doubleToLongBits(getLat())
          != Double.doubleToLongBits(
              other.getLat())) return false;
      if (Float.floatToIntBits(getSog())
          != Float.floatToIntBits(
              other.getSog())) return false;
      if (Float.floatToIntBits(getCog())
          != Float.floatToIntBits(
              other.getCog())) return false;
      if (Float.floatToIntBits(getRot())
          != Float.floatToIntBits(
              other.getRot())) return false;
      if (Float.floatToIntBits(getHeading())
          != Float.floatToIntBits(
              other.getHeading())) return false;
      if (getMmsi()
          != other.getMmsi()) return false;
      if (getImo()
          != other.getImo()) return false;
      if (!getCallsign()
          .equals(other.getCallsign())) return false;
      if (!getEnglishName()
          .equals(other.getEnglishName())) return false;
      if (!getChineseName()
          .equals(other.getChineseName())) return false;
      if (getShipType()
          != other.getShipType()) return false;
      if (getAisShipType()
          != other.getAisShipType()) return false;
      if (getFileShipType()
          != other.getFileShipType()) return false;
      if (Float.floatToIntBits(getWidth())
          != Float.floatToIntBits(
              other.getWidth())) return false;
      if (Float.floatToIntBits(getLength())
          != Float.floatToIntBits(
              other.getLength())) return false;
      if (Float.floatToIntBits(getToBow())
          != Float.floatToIntBits(
              other.getToBow())) return false;
      if (Float.floatToIntBits(getToStern())
          != Float.floatToIntBits(
              other.getToStern())) return false;
      if (Float.floatToIntBits(getToPort())
          != Float.floatToIntBits(
              other.getToPort())) return false;
      if (Float.floatToIntBits(getToStarboard())
          != Float.floatToIntBits(
              other.getToStarboard())) return false;
      if (getTimeout()
          != other.getTimeout()) return false;
      if (!getRegion()
          .equals(other.getRegion())) return false;
      if (Float.floatToIntBits(getDraught())
          != Float.floatToIntBits(
              other.getDraught())) return false;
      if (getEta()
          != other.getEta()) return false;
      if (!getDestination()
          .equals(other.getDestination())) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (!getFileId()
          .equals(other.getFileId())) return false;
      if (!getShipNo()
          .equals(other.getShipNo())) return false;
      if (getAlarms()
          != other.getAlarms()) return false;
      if (getTags()
          != other.getTags()) return false;
      if (getSpecialTag()
          != other.getSpecialTag()) return false;
      if (getTagsTwo()
          != other.getTagsTwo()) return false;
      if (getOrigins()
          != other.getOrigins()) return false;
      if (getTrack()
          != other.getTrack()) return false;
      if (getReliability()
          != other.getReliability()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
      hash = (37 * hash) + VERSION_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVersion());
      hash = (37 * hash) + LON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLon()));
      hash = (37 * hash) + LAT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLat()));
      hash = (37 * hash) + SOG_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getSog());
      hash = (37 * hash) + COG_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getCog());
      hash = (37 * hash) + ROT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getRot());
      hash = (37 * hash) + HEADING_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getHeading());
      hash = (37 * hash) + MMSI_FIELD_NUMBER;
      hash = (53 * hash) + getMmsi();
      hash = (37 * hash) + IMO_FIELD_NUMBER;
      hash = (53 * hash) + getImo();
      hash = (37 * hash) + CALLSIGN_FIELD_NUMBER;
      hash = (53 * hash) + getCallsign().hashCode();
      hash = (37 * hash) + ENGLISH_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getEnglishName().hashCode();
      hash = (37 * hash) + CHINESE_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getChineseName().hashCode();
      hash = (37 * hash) + SHIP_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getShipType();
      hash = (37 * hash) + AIS_SHIP_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getAisShipType();
      hash = (37 * hash) + FILE_SHIP_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getFileShipType();
      hash = (37 * hash) + WIDTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getWidth());
      hash = (37 * hash) + LENGTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getLength());
      hash = (37 * hash) + TO_BOW_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToBow());
      hash = (37 * hash) + TO_STERN_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToStern());
      hash = (37 * hash) + TO_PORT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToPort());
      hash = (37 * hash) + TO_STARBOARD_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToStarboard());
      hash = (37 * hash) + TIMEOUT_FIELD_NUMBER;
      hash = (53 * hash) + getTimeout();
      hash = (37 * hash) + REGION_FIELD_NUMBER;
      hash = (53 * hash) + getRegion().hashCode();
      hash = (37 * hash) + DRAUGHT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getDraught());
      hash = (37 * hash) + ETA_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEta());
      hash = (37 * hash) + DESTINATION_FIELD_NUMBER;
      hash = (53 * hash) + getDestination().hashCode();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + FILE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getFileId().hashCode();
      hash = (37 * hash) + SHIP_NO_FIELD_NUMBER;
      hash = (53 * hash) + getShipNo().hashCode();
      hash = (37 * hash) + ALARMS_FIELD_NUMBER;
      hash = (53 * hash) + getAlarms();
      hash = (37 * hash) + TAGS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTags());
      hash = (37 * hash) + SPECIAL_TAG_FIELD_NUMBER;
      hash = (53 * hash) + getSpecialTag();
      hash = (37 * hash) + TAGS_TWO_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTagsTwo());
      hash = (37 * hash) + ORIGINS_FIELD_NUMBER;
      hash = (53 * hash) + getOrigins();
      hash = (37 * hash) + TRACK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTrack());
      hash = (37 * hash) + RELIABILITY_FIELD_NUMBER;
      hash = (53 * hash) + getReliability();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Ship parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Ship parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Ship parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Ship parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Ship parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Ship parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Ship parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input);
    }
    public static Ship parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static Ship parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input);
    }
    public static Ship parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Ship parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input);
    }
    public static Ship parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Ship prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code litong.Ship}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:litong.Ship)
        ShipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ShipOuterClass.internal_static_litong_Ship_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ShipOuterClass.internal_static_litong_Ship_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Ship.class, Builder.class);
      }

      // Construct using litong.ShipOuterClass.Ship.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        id_ = "";
        name_ = "";
        time_ = 0L;
        version_ = 0L;
        lon_ = 0D;
        lat_ = 0D;
        sog_ = 0F;
        cog_ = 0F;
        rot_ = 0F;
        heading_ = 0F;
        mmsi_ = 0;
        imo_ = 0;
        callsign_ = "";
        englishName_ = "";
        chineseName_ = "";
        shipType_ = 0;
        aisShipType_ = 0;
        fileShipType_ = 0;
        width_ = 0F;
        length_ = 0F;
        toBow_ = 0F;
        toStern_ = 0F;
        toPort_ = 0F;
        toStarboard_ = 0F;
        timeout_ = 0;
        region_ = "";
        draught_ = 0F;
        eta_ = 0L;
        destination_ = "";
        status_ = 0;
        fileId_ = "";
        shipNo_ = "";
        alarms_ = 0;
        tags_ = 0L;
        specialTag_ = 0;
        tagsTwo_ = 0L;
        origins_ = 0;
        track_ = 0L;
        reliability_ = 0;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ShipOuterClass.internal_static_litong_Ship_descriptor;
      }

      @Override
      public Ship getDefaultInstanceForType() {
        return Ship.getDefaultInstance();
      }

      @Override
      public Ship build() {
        Ship result = buildPartial();
        if (!result.isInitialized()) {
          throw Builder.newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public Ship buildPartial() {
        Ship result = new Ship(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(Ship result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.time_ = time_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.version_ = version_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.lon_ = lon_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.lat_ = lat_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.sog_ = sog_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.cog_ = cog_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.rot_ = rot_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.heading_ = heading_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.mmsi_ = mmsi_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.imo_ = imo_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.callsign_ = callsign_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.englishName_ = englishName_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.chineseName_ = chineseName_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.shipType_ = shipType_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.aisShipType_ = aisShipType_;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.fileShipType_ = fileShipType_;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.width_ = width_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.length_ = length_;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.toBow_ = toBow_;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.toStern_ = toStern_;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.toPort_ = toPort_;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.toStarboard_ = toStarboard_;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.timeout_ = timeout_;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.region_ = region_;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.draught_ = draught_;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.eta_ = eta_;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.destination_ = destination_;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.status_ = status_;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.fileId_ = fileId_;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.shipNo_ = shipNo_;
        }
      }

      private void buildPartial1(Ship result) {
        int from_bitField1_ = bitField1_;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.alarms_ = alarms_;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.tags_ = tags_;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.specialTag_ = specialTag_;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.tagsTwo_ = tagsTwo_;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.origins_ = origins_;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.track_ = track_;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.reliability_ = reliability_;
        }
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Ship) {
          return mergeFrom((Ship)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Ship other) {
        if (other == Ship.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.getTime() != 0L) {
          setTime(other.getTime());
        }
        if (other.getVersion() != 0L) {
          setVersion(other.getVersion());
        }
        if (other.getLon() != 0D) {
          setLon(other.getLon());
        }
        if (other.getLat() != 0D) {
          setLat(other.getLat());
        }
        if (other.getSog() != 0F) {
          setSog(other.getSog());
        }
        if (other.getCog() != 0F) {
          setCog(other.getCog());
        }
        if (other.getRot() != 0F) {
          setRot(other.getRot());
        }
        if (other.getHeading() != 0F) {
          setHeading(other.getHeading());
        }
        if (other.getMmsi() != 0) {
          setMmsi(other.getMmsi());
        }
        if (other.getImo() != 0) {
          setImo(other.getImo());
        }
        if (!other.getCallsign().isEmpty()) {
          callsign_ = other.callsign_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (!other.getEnglishName().isEmpty()) {
          englishName_ = other.englishName_;
          bitField0_ |= 0x00002000;
          onChanged();
        }
        if (!other.getChineseName().isEmpty()) {
          chineseName_ = other.chineseName_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        if (other.getShipType() != 0) {
          setShipType(other.getShipType());
        }
        if (other.getAisShipType() != 0) {
          setAisShipType(other.getAisShipType());
        }
        if (other.getFileShipType() != 0) {
          setFileShipType(other.getFileShipType());
        }
        if (other.getWidth() != 0F) {
          setWidth(other.getWidth());
        }
        if (other.getLength() != 0F) {
          setLength(other.getLength());
        }
        if (other.getToBow() != 0F) {
          setToBow(other.getToBow());
        }
        if (other.getToStern() != 0F) {
          setToStern(other.getToStern());
        }
        if (other.getToPort() != 0F) {
          setToPort(other.getToPort());
        }
        if (other.getToStarboard() != 0F) {
          setToStarboard(other.getToStarboard());
        }
        if (other.getTimeout() != 0) {
          setTimeout(other.getTimeout());
        }
        if (!other.getRegion().isEmpty()) {
          region_ = other.region_;
          bitField0_ |= 0x02000000;
          onChanged();
        }
        if (other.getDraught() != 0F) {
          setDraught(other.getDraught());
        }
        if (other.getEta() != 0L) {
          setEta(other.getEta());
        }
        if (!other.getDestination().isEmpty()) {
          destination_ = other.destination_;
          bitField0_ |= 0x10000000;
          onChanged();
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (!other.getFileId().isEmpty()) {
          fileId_ = other.fileId_;
          bitField0_ |= 0x40000000;
          onChanged();
        }
        if (!other.getShipNo().isEmpty()) {
          shipNo_ = other.shipNo_;
          bitField0_ |= 0x80000000;
          onChanged();
        }
        if (other.getAlarms() != 0) {
          setAlarms(other.getAlarms());
        }
        if (other.getTags() != 0L) {
          setTags(other.getTags());
        }
        if (other.getSpecialTag() != 0) {
          setSpecialTag(other.getSpecialTag());
        }
        if (other.getTagsTwo() != 0L) {
          setTagsTwo(other.getTagsTwo());
        }
        if (other.getOrigins() != 0) {
          setOrigins(other.getOrigins());
        }
        if (other.getTrack() != 0L) {
          setTrack(other.getTrack());
        }
        if (other.getReliability() != 0) {
          setReliability(other.getReliability());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                time_ = input.readSInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                version_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 41: {
                lon_ = input.readDouble();
                bitField0_ |= 0x00000010;
                break;
              } // case 41
              case 49: {
                lat_ = input.readDouble();
                bitField0_ |= 0x00000020;
                break;
              } // case 49
              case 61: {
                sog_ = input.readFloat();
                bitField0_ |= 0x00000040;
                break;
              } // case 61
              case 69: {
                cog_ = input.readFloat();
                bitField0_ |= 0x00000080;
                break;
              } // case 69
              case 77: {
                rot_ = input.readFloat();
                bitField0_ |= 0x00000100;
                break;
              } // case 77
              case 85: {
                heading_ = input.readFloat();
                bitField0_ |= 0x00000200;
                break;
              } // case 85
              case 88: {
                mmsi_ = input.readUInt32();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 96: {
                imo_ = input.readUInt32();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 106: {
                callsign_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                englishName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                chineseName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 128: {
                shipType_ = input.readUInt32();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 141: {
                width_ = input.readFloat();
                bitField0_ |= 0x00040000;
                break;
              } // case 141
              case 149: {
                length_ = input.readFloat();
                bitField0_ |= 0x00080000;
                break;
              } // case 149
              case 157: {
                toBow_ = input.readFloat();
                bitField0_ |= 0x00100000;
                break;
              } // case 157
              case 165: {
                toStern_ = input.readFloat();
                bitField0_ |= 0x00200000;
                break;
              } // case 165
              case 173: {
                toPort_ = input.readFloat();
                bitField0_ |= 0x00400000;
                break;
              } // case 173
              case 181: {
                toStarboard_ = input.readFloat();
                bitField0_ |= 0x00800000;
                break;
              } // case 181
              case 184: {
                timeout_ = input.readUInt32();
                bitField0_ |= 0x01000000;
                break;
              } // case 184
              case 194: {
                region_ = input.readStringRequireUtf8();
                bitField0_ |= 0x02000000;
                break;
              } // case 194
              case 205: {
                draught_ = input.readFloat();
                bitField0_ |= 0x04000000;
                break;
              } // case 205
              case 208: {
                eta_ = input.readSInt64();
                bitField0_ |= 0x08000000;
                break;
              } // case 208
              case 218: {
                destination_ = input.readStringRequireUtf8();
                bitField0_ |= 0x10000000;
                break;
              } // case 218
              case 224: {
                status_ = input.readUInt32();
                bitField0_ |= 0x20000000;
                break;
              } // case 224
              case 240: {
                track_ = input.readUInt64();
                bitField1_ |= 0x00000020;
                break;
              } // case 240
              case 322: {
                fileId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x40000000;
                break;
              } // case 322
              case 330: {
                shipNo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x80000000;
                break;
              } // case 330
              case 336: {
                aisShipType_ = input.readUInt32();
                bitField0_ |= 0x00010000;
                break;
              } // case 336
              case 344: {
                fileShipType_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 344
              case 416: {
                alarms_ = input.readUInt32();
                bitField1_ |= 0x00000001;
                break;
              } // case 416
              case 424: {
                tags_ = input.readUInt64();
                bitField1_ |= 0x00000002;
                break;
              } // case 424
              case 432: {
                specialTag_ = input.readUInt32();
                bitField1_ |= 0x00000004;
                break;
              } // case 432
              case 440: {
                tagsTwo_ = input.readUInt64();
                bitField1_ |= 0x00000008;
                break;
              } // case 440
              case 448: {
                origins_ = input.readUInt32();
                bitField1_ |= 0x00000010;
                break;
              } // case 448
              case 560: {
                reliability_ = input.readUInt32();
                bitField1_ |= 0x00000040;
                break;
              } // case 560
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private Object id_ = "";
      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      public String getId() {
        Object ref = id_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public String getName() {
        Object ref = name_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private long time_ ;
      /**
       * <code>sint64 time = 3;</code>
       * @return The time.
       */
      @Override
      public long getTime() {
        return time_;
      }
      /**
       * <code>sint64 time = 3;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(long value) {

        time_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>sint64 time = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        bitField0_ = (bitField0_ & ~0x00000004);
        time_ = 0L;
        onChanged();
        return this;
      }

      private long version_ ;
      /**
       * <code>uint64 version = 4;</code>
       * @return The version.
       */
      @Override
      public long getVersion() {
        return version_;
      }
      /**
       * <code>uint64 version = 4;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(long value) {

        version_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 version = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000008);
        version_ = 0L;
        onChanged();
        return this;
      }

      private double lon_ ;
      /**
       * <code>double lon = 5;</code>
       * @return The lon.
       */
      @Override
      public double getLon() {
        return lon_;
      }
      /**
       * <code>double lon = 5;</code>
       * @param value The lon to set.
       * @return This builder for chaining.
       */
      public Builder setLon(double value) {

        lon_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>double lon = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLon() {
        bitField0_ = (bitField0_ & ~0x00000010);
        lon_ = 0D;
        onChanged();
        return this;
      }

      private double lat_ ;
      /**
       * <code>double lat = 6;</code>
       * @return The lat.
       */
      @Override
      public double getLat() {
        return lat_;
      }
      /**
       * <code>double lat = 6;</code>
       * @param value The lat to set.
       * @return This builder for chaining.
       */
      public Builder setLat(double value) {

        lat_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>double lat = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearLat() {
        bitField0_ = (bitField0_ & ~0x00000020);
        lat_ = 0D;
        onChanged();
        return this;
      }

      private float sog_ ;
      /**
       * <code>float sog = 7;</code>
       * @return The sog.
       */
      @Override
      public float getSog() {
        return sog_;
      }
      /**
       * <code>float sog = 7;</code>
       * @param value The sog to set.
       * @return This builder for chaining.
       */
      public Builder setSog(float value) {

        sog_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>float sog = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearSog() {
        bitField0_ = (bitField0_ & ~0x00000040);
        sog_ = 0F;
        onChanged();
        return this;
      }

      private float cog_ ;
      /**
       * <code>float cog = 8;</code>
       * @return The cog.
       */
      @Override
      public float getCog() {
        return cog_;
      }
      /**
       * <code>float cog = 8;</code>
       * @param value The cog to set.
       * @return This builder for chaining.
       */
      public Builder setCog(float value) {

        cog_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>float cog = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCog() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cog_ = 0F;
        onChanged();
        return this;
      }

      private float rot_ ;
      /**
       * <code>float rot = 9;</code>
       * @return The rot.
       */
      @Override
      public float getRot() {
        return rot_;
      }
      /**
       * <code>float rot = 9;</code>
       * @param value The rot to set.
       * @return This builder for chaining.
       */
      public Builder setRot(float value) {

        rot_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>float rot = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearRot() {
        bitField0_ = (bitField0_ & ~0x00000100);
        rot_ = 0F;
        onChanged();
        return this;
      }

      private float heading_ ;
      /**
       * <code>float heading = 10;</code>
       * @return The heading.
       */
      @Override
      public float getHeading() {
        return heading_;
      }
      /**
       * <code>float heading = 10;</code>
       * @param value The heading to set.
       * @return This builder for chaining.
       */
      public Builder setHeading(float value) {

        heading_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>float heading = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeading() {
        bitField0_ = (bitField0_ & ~0x00000200);
        heading_ = 0F;
        onChanged();
        return this;
      }

      private int mmsi_ ;
      /**
       * <code>uint32 mmsi = 11;</code>
       * @return The mmsi.
       */
      @Override
      public int getMmsi() {
        return mmsi_;
      }
      /**
       * <code>uint32 mmsi = 11;</code>
       * @param value The mmsi to set.
       * @return This builder for chaining.
       */
      public Builder setMmsi(int value) {

        mmsi_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 mmsi = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearMmsi() {
        bitField0_ = (bitField0_ & ~0x00000400);
        mmsi_ = 0;
        onChanged();
        return this;
      }

      private int imo_ ;
      /**
       * <code>uint32 imo = 12;</code>
       * @return The imo.
       */
      @Override
      public int getImo() {
        return imo_;
      }
      /**
       * <code>uint32 imo = 12;</code>
       * @param value The imo to set.
       * @return This builder for chaining.
       */
      public Builder setImo(int value) {

        imo_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 imo = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearImo() {
        bitField0_ = (bitField0_ & ~0x00000800);
        imo_ = 0;
        onChanged();
        return this;
      }

      private Object callsign_ = "";
      /**
       * <code>string callsign = 13;</code>
       * @return The callsign.
       */
      public String getCallsign() {
        Object ref = callsign_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          callsign_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string callsign = 13;</code>
       * @return The bytes for callsign.
       */
      public com.google.protobuf.ByteString
          getCallsignBytes() {
        Object ref = callsign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          callsign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string callsign = 13;</code>
       * @param value The callsign to set.
       * @return This builder for chaining.
       */
      public Builder setCallsign(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        callsign_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>string callsign = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearCallsign() {
        callsign_ = getDefaultInstance().getCallsign();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <code>string callsign = 13;</code>
       * @param value The bytes for callsign to set.
       * @return This builder for chaining.
       */
      public Builder setCallsignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        callsign_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private Object englishName_ = "";
      /**
       * <code>string english_name = 14;</code>
       * @return The englishName.
       */
      public String getEnglishName() {
        Object ref = englishName_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          englishName_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string english_name = 14;</code>
       * @return The bytes for englishName.
       */
      public com.google.protobuf.ByteString
          getEnglishNameBytes() {
        Object ref = englishName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          englishName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string english_name = 14;</code>
       * @param value The englishName to set.
       * @return This builder for chaining.
       */
      public Builder setEnglishName(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        englishName_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>string english_name = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnglishName() {
        englishName_ = getDefaultInstance().getEnglishName();
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }
      /**
       * <code>string english_name = 14;</code>
       * @param value The bytes for englishName to set.
       * @return This builder for chaining.
       */
      public Builder setEnglishNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        englishName_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }

      private Object chineseName_ = "";
      /**
       * <code>string chinese_name = 15;</code>
       * @return The chineseName.
       */
      public String getChineseName() {
        Object ref = chineseName_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          chineseName_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @return The bytes for chineseName.
       */
      public com.google.protobuf.ByteString
          getChineseNameBytes() {
        Object ref = chineseName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          chineseName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @param value The chineseName to set.
       * @return This builder for chaining.
       */
      public Builder setChineseName(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        chineseName_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearChineseName() {
        chineseName_ = getDefaultInstance().getChineseName();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @param value The bytes for chineseName to set.
       * @return This builder for chaining.
       */
      public Builder setChineseNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        chineseName_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      private int shipType_ ;
      /**
       * <code>uint32 ship_type = 16;</code>
       * @return The shipType.
       */
      @Override
      public int getShipType() {
        return shipType_;
      }
      /**
       * <code>uint32 ship_type = 16;</code>
       * @param value The shipType to set.
       * @return This builder for chaining.
       */
      public Builder setShipType(int value) {

        shipType_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 ship_type = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearShipType() {
        bitField0_ = (bitField0_ & ~0x00008000);
        shipType_ = 0;
        onChanged();
        return this;
      }

      private int aisShipType_ ;
      /**
       * <code>uint32 ais_ship_type = 42;</code>
       * @return The aisShipType.
       */
      @Override
      public int getAisShipType() {
        return aisShipType_;
      }
      /**
       * <code>uint32 ais_ship_type = 42;</code>
       * @param value The aisShipType to set.
       * @return This builder for chaining.
       */
      public Builder setAisShipType(int value) {

        aisShipType_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 ais_ship_type = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearAisShipType() {
        bitField0_ = (bitField0_ & ~0x00010000);
        aisShipType_ = 0;
        onChanged();
        return this;
      }

      private int fileShipType_ ;
      /**
       * <code>uint32 file_ship_type = 43;</code>
       * @return The fileShipType.
       */
      @Override
      public int getFileShipType() {
        return fileShipType_;
      }
      /**
       * <code>uint32 file_ship_type = 43;</code>
       * @param value The fileShipType to set.
       * @return This builder for chaining.
       */
      public Builder setFileShipType(int value) {

        fileShipType_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 file_ship_type = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileShipType() {
        bitField0_ = (bitField0_ & ~0x00020000);
        fileShipType_ = 0;
        onChanged();
        return this;
      }

      private float width_ ;
      /**
       * <code>float width = 17;</code>
       * @return The width.
       */
      @Override
      public float getWidth() {
        return width_;
      }
      /**
       * <code>float width = 17;</code>
       * @param value The width to set.
       * @return This builder for chaining.
       */
      public Builder setWidth(float value) {

        width_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>float width = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearWidth() {
        bitField0_ = (bitField0_ & ~0x00040000);
        width_ = 0F;
        onChanged();
        return this;
      }

      private float length_ ;
      /**
       * <code>float length = 18;</code>
       * @return The length.
       */
      @Override
      public float getLength() {
        return length_;
      }
      /**
       * <code>float length = 18;</code>
       * @param value The length to set.
       * @return This builder for chaining.
       */
      public Builder setLength(float value) {

        length_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>float length = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearLength() {
        bitField0_ = (bitField0_ & ~0x00080000);
        length_ = 0F;
        onChanged();
        return this;
      }

      private float toBow_ ;
      /**
       * <code>float to_bow = 19;</code>
       * @return The toBow.
       */
      @Override
      public float getToBow() {
        return toBow_;
      }
      /**
       * <code>float to_bow = 19;</code>
       * @param value The toBow to set.
       * @return This builder for chaining.
       */
      public Builder setToBow(float value) {

        toBow_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_bow = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearToBow() {
        bitField0_ = (bitField0_ & ~0x00100000);
        toBow_ = 0F;
        onChanged();
        return this;
      }

      private float toStern_ ;
      /**
       * <code>float to_stern = 20;</code>
       * @return The toStern.
       */
      @Override
      public float getToStern() {
        return toStern_;
      }
      /**
       * <code>float to_stern = 20;</code>
       * @param value The toStern to set.
       * @return This builder for chaining.
       */
      public Builder setToStern(float value) {

        toStern_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_stern = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearToStern() {
        bitField0_ = (bitField0_ & ~0x00200000);
        toStern_ = 0F;
        onChanged();
        return this;
      }

      private float toPort_ ;
      /**
       * <code>float to_port = 21;</code>
       * @return The toPort.
       */
      @Override
      public float getToPort() {
        return toPort_;
      }
      /**
       * <code>float to_port = 21;</code>
       * @param value The toPort to set.
       * @return This builder for chaining.
       */
      public Builder setToPort(float value) {

        toPort_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_port = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearToPort() {
        bitField0_ = (bitField0_ & ~0x00400000);
        toPort_ = 0F;
        onChanged();
        return this;
      }

      private float toStarboard_ ;
      /**
       * <code>float to_starboard = 22;</code>
       * @return The toStarboard.
       */
      @Override
      public float getToStarboard() {
        return toStarboard_;
      }
      /**
       * <code>float to_starboard = 22;</code>
       * @param value The toStarboard to set.
       * @return This builder for chaining.
       */
      public Builder setToStarboard(float value) {

        toStarboard_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_starboard = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearToStarboard() {
        bitField0_ = (bitField0_ & ~0x00800000);
        toStarboard_ = 0F;
        onChanged();
        return this;
      }

      private int timeout_ ;
      /**
       * <code>uint32 timeout = 23;</code>
       * @return The timeout.
       */
      @Override
      public int getTimeout() {
        return timeout_;
      }
      /**
       * <code>uint32 timeout = 23;</code>
       * @param value The timeout to set.
       * @return This builder for chaining.
       */
      public Builder setTimeout(int value) {

        timeout_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 timeout = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimeout() {
        bitField0_ = (bitField0_ & ~0x01000000);
        timeout_ = 0;
        onChanged();
        return this;
      }

      private Object region_ = "";
      /**
       * <code>string region = 24;</code>
       * @return The region.
       */
      public String getRegion() {
        Object ref = region_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          region_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string region = 24;</code>
       * @return The bytes for region.
       */
      public com.google.protobuf.ByteString
          getRegionBytes() {
        Object ref = region_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          region_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string region = 24;</code>
       * @param value The region to set.
       * @return This builder for chaining.
       */
      public Builder setRegion(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        region_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>string region = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearRegion() {
        region_ = getDefaultInstance().getRegion();
        bitField0_ = (bitField0_ & ~0x02000000);
        onChanged();
        return this;
      }
      /**
       * <code>string region = 24;</code>
       * @param value The bytes for region to set.
       * @return This builder for chaining.
       */
      public Builder setRegionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        region_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }

      private float draught_ ;
      /**
       * <code>float draught = 25;</code>
       * @return The draught.
       */
      @Override
      public float getDraught() {
        return draught_;
      }
      /**
       * <code>float draught = 25;</code>
       * @param value The draught to set.
       * @return This builder for chaining.
       */
      public Builder setDraught(float value) {

        draught_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>float draught = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearDraught() {
        bitField0_ = (bitField0_ & ~0x04000000);
        draught_ = 0F;
        onChanged();
        return this;
      }

      private long eta_ ;
      /**
       * <code>sint64 eta = 26;</code>
       * @return The eta.
       */
      @Override
      public long getEta() {
        return eta_;
      }
      /**
       * <code>sint64 eta = 26;</code>
       * @param value The eta to set.
       * @return This builder for chaining.
       */
      public Builder setEta(long value) {

        eta_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>sint64 eta = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearEta() {
        bitField0_ = (bitField0_ & ~0x08000000);
        eta_ = 0L;
        onChanged();
        return this;
      }

      private Object destination_ = "";
      /**
       * <code>string destination = 27;</code>
       * @return The destination.
       */
      public String getDestination() {
        Object ref = destination_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          destination_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string destination = 27;</code>
       * @return The bytes for destination.
       */
      public com.google.protobuf.ByteString
          getDestinationBytes() {
        Object ref = destination_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          destination_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string destination = 27;</code>
       * @param value The destination to set.
       * @return This builder for chaining.
       */
      public Builder setDestination(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        destination_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>string destination = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearDestination() {
        destination_ = getDefaultInstance().getDestination();
        bitField0_ = (bitField0_ & ~0x10000000);
        onChanged();
        return this;
      }
      /**
       * <code>string destination = 27;</code>
       * @param value The bytes for destination to set.
       * @return This builder for chaining.
       */
      public Builder setDestinationBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        destination_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <code>uint32 status = 28;</code>
       * @return The status.
       */
      @Override
      public int getStatus() {
        return status_;
      }
      /**
       * <code>uint32 status = 28;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {

        status_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 status = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x20000000);
        status_ = 0;
        onChanged();
        return this;
      }

      private Object fileId_ = "";
      /**
       * <code>string file_id = 40;</code>
       * @return The fileId.
       */
      public String getFileId() {
        Object ref = fileId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          fileId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string file_id = 40;</code>
       * @return The bytes for fileId.
       */
      public com.google.protobuf.ByteString
          getFileIdBytes() {
        Object ref = fileId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          fileId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string file_id = 40;</code>
       * @param value The fileId to set.
       * @return This builder for chaining.
       */
      public Builder setFileId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        fileId_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>string file_id = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileId() {
        fileId_ = getDefaultInstance().getFileId();
        bitField0_ = (bitField0_ & ~0x40000000);
        onChanged();
        return this;
      }
      /**
       * <code>string file_id = 40;</code>
       * @param value The bytes for fileId to set.
       * @return This builder for chaining.
       */
      public Builder setFileIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        fileId_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }

      private Object shipNo_ = "";
      /**
       * <code>string ship_no = 41;</code>
       * @return The shipNo.
       */
      public String getShipNo() {
        Object ref = shipNo_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          shipNo_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string ship_no = 41;</code>
       * @return The bytes for shipNo.
       */
      public com.google.protobuf.ByteString
          getShipNoBytes() {
        Object ref = shipNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          shipNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string ship_no = 41;</code>
       * @param value The shipNo to set.
       * @return This builder for chaining.
       */
      public Builder setShipNo(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        shipNo_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>string ship_no = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearShipNo() {
        shipNo_ = getDefaultInstance().getShipNo();
        bitField0_ = (bitField0_ & ~0x80000000);
        onChanged();
        return this;
      }
      /**
       * <code>string ship_no = 41;</code>
       * @param value The bytes for shipNo to set.
       * @return This builder for chaining.
       */
      public Builder setShipNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        shipNo_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }

      private int alarms_ ;
      /**
       * <code>uint32 alarms = 52;</code>
       * @return The alarms.
       */
      @Override
      public int getAlarms() {
        return alarms_;
      }
      /**
       * <code>uint32 alarms = 52;</code>
       * @param value The alarms to set.
       * @return This builder for chaining.
       */
      public Builder setAlarms(int value) {

        alarms_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 alarms = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlarms() {
        bitField1_ = (bitField1_ & ~0x00000001);
        alarms_ = 0;
        onChanged();
        return this;
      }

      private long tags_ ;
      /**
       * <code>uint64 tags = 53;</code>
       * @return The tags.
       */
      @Override
      public long getTags() {
        return tags_;
      }
      /**
       * <code>uint64 tags = 53;</code>
       * @param value The tags to set.
       * @return This builder for chaining.
       */
      public Builder setTags(long value) {

        tags_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 tags = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearTags() {
        bitField1_ = (bitField1_ & ~0x00000002);
        tags_ = 0L;
        onChanged();
        return this;
      }

      private int specialTag_ ;
      /**
       * <code>uint32 special_tag = 54;</code>
       * @return The specialTag.
       */
      @Override
      public int getSpecialTag() {
        return specialTag_;
      }
      /**
       * <code>uint32 special_tag = 54;</code>
       * @param value The specialTag to set.
       * @return This builder for chaining.
       */
      public Builder setSpecialTag(int value) {

        specialTag_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 special_tag = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpecialTag() {
        bitField1_ = (bitField1_ & ~0x00000004);
        specialTag_ = 0;
        onChanged();
        return this;
      }

      private long tagsTwo_ ;
      /**
       * <code>uint64 tags_two = 55;</code>
       * @return The tagsTwo.
       */
      @Override
      public long getTagsTwo() {
        return tagsTwo_;
      }
      /**
       * <code>uint64 tags_two = 55;</code>
       * @param value The tagsTwo to set.
       * @return This builder for chaining.
       */
      public Builder setTagsTwo(long value) {

        tagsTwo_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 tags_two = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearTagsTwo() {
        bitField1_ = (bitField1_ & ~0x00000008);
        tagsTwo_ = 0L;
        onChanged();
        return this;
      }

      private int origins_ ;
      /**
       * <code>uint32 origins = 56;</code>
       * @return The origins.
       */
      @Override
      public int getOrigins() {
        return origins_;
      }
      /**
       * <code>uint32 origins = 56;</code>
       * @param value The origins to set.
       * @return This builder for chaining.
       */
      public Builder setOrigins(int value) {

        origins_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 origins = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrigins() {
        bitField1_ = (bitField1_ & ~0x00000010);
        origins_ = 0;
        onChanged();
        return this;
      }

      private long track_ ;
      /**
       * <code>uint64 track = 30;</code>
       * @return The track.
       */
      @Override
      public long getTrack() {
        return track_;
      }
      /**
       * <code>uint64 track = 30;</code>
       * @param value The track to set.
       * @return This builder for chaining.
       */
      public Builder setTrack(long value) {

        track_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 track = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearTrack() {
        bitField1_ = (bitField1_ & ~0x00000020);
        track_ = 0L;
        onChanged();
        return this;
      }

      private int reliability_ ;
      /**
       * <code>uint32 reliability = 70;</code>
       * @return The reliability.
       */
      @Override
      public int getReliability() {
        return reliability_;
      }
      /**
       * <code>uint32 reliability = 70;</code>
       * @param value The reliability to set.
       * @return This builder for chaining.
       */
      public Builder setReliability(int value) {

        reliability_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 reliability = 70;</code>
       * @return This builder for chaining.
       */
      public Builder clearReliability() {
        bitField1_ = (bitField1_ & ~0x00000040);
        reliability_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:litong.Ship)
    }

    // @@protoc_insertion_point(class_scope:litong.Ship)
    private static final Ship DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Ship();
    }

    public static Ship getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Ship>
        PARSER = new com.google.protobuf.AbstractParser<Ship>() {
      @Override
      public Ship parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Ship> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<Ship> getParserForType() {
      return PARSER;
    }

    @Override
    public Ship getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ShipDynamicOrBuilder extends
      // @@protoc_insertion_point(interface_extends:litong.ShipDynamic)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    String getId();
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>sint64 time = 3;</code>
     * @return The time.
     */
    long getTime();

    /**
     * <code>uint64 version = 4;</code>
     * @return The version.
     */
    long getVersion();

    /**
     * <code>double lon = 5;</code>
     * @return The lon.
     */
    double getLon();

    /**
     * <code>double lat = 6;</code>
     * @return The lat.
     */
    double getLat();

    /**
     * <code>float sog = 7;</code>
     * @return The sog.
     */
    float getSog();

    /**
     * <code>float cog = 8;</code>
     * @return The cog.
     */
    float getCog();

    /**
     * <code>float rot = 9;</code>
     * @return The rot.
     */
    float getRot();

    /**
     * <code>float heading = 10;</code>
     * @return The heading.
     */
    float getHeading();

    /**
     * <code>uint32 timeout = 23;</code>
     * @return The timeout.
     */
    int getTimeout();

    /**
     * <code>float draught = 25;</code>
     * @return The draught.
     */
    float getDraught();

    /**
     * <code>uint32 status = 28;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <code>uint32 alarms = 52;</code>
     * @return The alarms.
     */
    int getAlarms();

    /**
     * <code>uint64 tags = 53;</code>
     * @return The tags.
     */
    long getTags();

    /**
     * <code>uint32 special_tag = 54;</code>
     * @return The specialTag.
     */
    int getSpecialTag();

    /**
     * <code>uint64 tags_two = 55;</code>
     * @return The tagsTwo.
     */
    long getTagsTwo();

    /**
     * <code>uint32 origins = 56;</code>
     * @return The origins.
     */
    int getOrigins();
  }
  /**
   * Protobuf type {@code litong.ShipDynamic}
   */
  public static final class ShipDynamic extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:litong.ShipDynamic)
      ShipDynamicOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ShipDynamic.newBuilder() to construct.
    private ShipDynamic(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ShipDynamic() {
      id_ = "";
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ShipDynamic();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ShipOuterClass.internal_static_litong_ShipDynamic_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ShipOuterClass.internal_static_litong_ShipDynamic_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ShipDynamic.class, Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    @Override
    public String getId() {
      Object ref = id_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIME_FIELD_NUMBER = 3;
    private long time_ = 0L;
    /**
     * <code>sint64 time = 3;</code>
     * @return The time.
     */
    @Override
    public long getTime() {
      return time_;
    }

    public static final int VERSION_FIELD_NUMBER = 4;
    private long version_ = 0L;
    /**
     * <code>uint64 version = 4;</code>
     * @return The version.
     */
    @Override
    public long getVersion() {
      return version_;
    }

    public static final int LON_FIELD_NUMBER = 5;
    private double lon_ = 0D;
    /**
     * <code>double lon = 5;</code>
     * @return The lon.
     */
    @Override
    public double getLon() {
      return lon_;
    }

    public static final int LAT_FIELD_NUMBER = 6;
    private double lat_ = 0D;
    /**
     * <code>double lat = 6;</code>
     * @return The lat.
     */
    @Override
    public double getLat() {
      return lat_;
    }

    public static final int SOG_FIELD_NUMBER = 7;
    private float sog_ = 0F;
    /**
     * <code>float sog = 7;</code>
     * @return The sog.
     */
    @Override
    public float getSog() {
      return sog_;
    }

    public static final int COG_FIELD_NUMBER = 8;
    private float cog_ = 0F;
    /**
     * <code>float cog = 8;</code>
     * @return The cog.
     */
    @Override
    public float getCog() {
      return cog_;
    }

    public static final int ROT_FIELD_NUMBER = 9;
    private float rot_ = 0F;
    /**
     * <code>float rot = 9;</code>
     * @return The rot.
     */
    @Override
    public float getRot() {
      return rot_;
    }

    public static final int HEADING_FIELD_NUMBER = 10;
    private float heading_ = 0F;
    /**
     * <code>float heading = 10;</code>
     * @return The heading.
     */
    @Override
    public float getHeading() {
      return heading_;
    }

    public static final int TIMEOUT_FIELD_NUMBER = 23;
    private int timeout_ = 0;
    /**
     * <code>uint32 timeout = 23;</code>
     * @return The timeout.
     */
    @Override
    public int getTimeout() {
      return timeout_;
    }

    public static final int DRAUGHT_FIELD_NUMBER = 25;
    private float draught_ = 0F;
    /**
     * <code>float draught = 25;</code>
     * @return The draught.
     */
    @Override
    public float getDraught() {
      return draught_;
    }

    public static final int STATUS_FIELD_NUMBER = 28;
    private int status_ = 0;
    /**
     * <code>uint32 status = 28;</code>
     * @return The status.
     */
    @Override
    public int getStatus() {
      return status_;
    }

    public static final int ALARMS_FIELD_NUMBER = 52;
    private int alarms_ = 0;
    /**
     * <code>uint32 alarms = 52;</code>
     * @return The alarms.
     */
    @Override
    public int getAlarms() {
      return alarms_;
    }

    public static final int TAGS_FIELD_NUMBER = 53;
    private long tags_ = 0L;
    /**
     * <code>uint64 tags = 53;</code>
     * @return The tags.
     */
    @Override
    public long getTags() {
      return tags_;
    }

    public static final int SPECIAL_TAG_FIELD_NUMBER = 54;
    private int specialTag_ = 0;
    /**
     * <code>uint32 special_tag = 54;</code>
     * @return The specialTag.
     */
    @Override
    public int getSpecialTag() {
      return specialTag_;
    }

    public static final int TAGS_TWO_FIELD_NUMBER = 55;
    private long tagsTwo_ = 0L;
    /**
     * <code>uint64 tags_two = 55;</code>
     * @return The tagsTwo.
     */
    @Override
    public long getTagsTwo() {
      return tagsTwo_;
    }

    public static final int ORIGINS_FIELD_NUMBER = 56;
    private int origins_ = 0;
    /**
     * <code>uint32 origins = 56;</code>
     * @return The origins.
     */
    @Override
    public int getOrigins() {
      return origins_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!GeneratedMessageV3.isStringEmpty(id_)) {
        GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (time_ != 0L) {
        output.writeSInt64(3, time_);
      }
      if (version_ != 0L) {
        output.writeUInt64(4, version_);
      }
      if (Double.doubleToRawLongBits(lon_) != 0) {
        output.writeDouble(5, lon_);
      }
      if (Double.doubleToRawLongBits(lat_) != 0) {
        output.writeDouble(6, lat_);
      }
      if (Float.floatToRawIntBits(sog_) != 0) {
        output.writeFloat(7, sog_);
      }
      if (Float.floatToRawIntBits(cog_) != 0) {
        output.writeFloat(8, cog_);
      }
      if (Float.floatToRawIntBits(rot_) != 0) {
        output.writeFloat(9, rot_);
      }
      if (Float.floatToRawIntBits(heading_) != 0) {
        output.writeFloat(10, heading_);
      }
      if (timeout_ != 0) {
        output.writeUInt32(23, timeout_);
      }
      if (Float.floatToRawIntBits(draught_) != 0) {
        output.writeFloat(25, draught_);
      }
      if (status_ != 0) {
        output.writeUInt32(28, status_);
      }
      if (alarms_ != 0) {
        output.writeUInt32(52, alarms_);
      }
      if (tags_ != 0L) {
        output.writeUInt64(53, tags_);
      }
      if (specialTag_ != 0) {
        output.writeUInt32(54, specialTag_);
      }
      if (tagsTwo_ != 0L) {
        output.writeUInt64(55, tagsTwo_);
      }
      if (origins_ != 0) {
        output.writeUInt32(56, origins_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!GeneratedMessageV3.isStringEmpty(id_)) {
        size += GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (time_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(3, time_);
      }
      if (version_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, version_);
      }
      if (Double.doubleToRawLongBits(lon_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(5, lon_);
      }
      if (Double.doubleToRawLongBits(lat_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, lat_);
      }
      if (Float.floatToRawIntBits(sog_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, sog_);
      }
      if (Float.floatToRawIntBits(cog_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(8, cog_);
      }
      if (Float.floatToRawIntBits(rot_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, rot_);
      }
      if (Float.floatToRawIntBits(heading_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(10, heading_);
      }
      if (timeout_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(23, timeout_);
      }
      if (Float.floatToRawIntBits(draught_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(25, draught_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, status_);
      }
      if (alarms_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(52, alarms_);
      }
      if (tags_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(53, tags_);
      }
      if (specialTag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(54, specialTag_);
      }
      if (tagsTwo_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(55, tagsTwo_);
      }
      if (origins_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(56, origins_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ShipDynamic)) {
        return super.equals(obj);
      }
      ShipDynamic other = (ShipDynamic) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (getTime()
          != other.getTime()) return false;
      if (getVersion()
          != other.getVersion()) return false;
      if (Double.doubleToLongBits(getLon())
          != Double.doubleToLongBits(
              other.getLon())) return false;
      if (Double.doubleToLongBits(getLat())
          != Double.doubleToLongBits(
              other.getLat())) return false;
      if (Float.floatToIntBits(getSog())
          != Float.floatToIntBits(
              other.getSog())) return false;
      if (Float.floatToIntBits(getCog())
          != Float.floatToIntBits(
              other.getCog())) return false;
      if (Float.floatToIntBits(getRot())
          != Float.floatToIntBits(
              other.getRot())) return false;
      if (Float.floatToIntBits(getHeading())
          != Float.floatToIntBits(
              other.getHeading())) return false;
      if (getTimeout()
          != other.getTimeout()) return false;
      if (Float.floatToIntBits(getDraught())
          != Float.floatToIntBits(
              other.getDraught())) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (getAlarms()
          != other.getAlarms()) return false;
      if (getTags()
          != other.getTags()) return false;
      if (getSpecialTag()
          != other.getSpecialTag()) return false;
      if (getTagsTwo()
          != other.getTagsTwo()) return false;
      if (getOrigins()
          != other.getOrigins()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTime());
      hash = (37 * hash) + VERSION_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVersion());
      hash = (37 * hash) + LON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLon()));
      hash = (37 * hash) + LAT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          Double.doubleToLongBits(getLat()));
      hash = (37 * hash) + SOG_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getSog());
      hash = (37 * hash) + COG_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getCog());
      hash = (37 * hash) + ROT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getRot());
      hash = (37 * hash) + HEADING_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getHeading());
      hash = (37 * hash) + TIMEOUT_FIELD_NUMBER;
      hash = (53 * hash) + getTimeout();
      hash = (37 * hash) + DRAUGHT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getDraught());
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + ALARMS_FIELD_NUMBER;
      hash = (53 * hash) + getAlarms();
      hash = (37 * hash) + TAGS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTags());
      hash = (37 * hash) + SPECIAL_TAG_FIELD_NUMBER;
      hash = (53 * hash) + getSpecialTag();
      hash = (37 * hash) + TAGS_TWO_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTagsTwo());
      hash = (37 * hash) + ORIGINS_FIELD_NUMBER;
      hash = (53 * hash) + getOrigins();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ShipDynamic parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ShipDynamic parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ShipDynamic parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ShipDynamic parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ShipDynamic parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ShipDynamic parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ShipDynamic parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input);
    }
    public static ShipDynamic parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ShipDynamic parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input);
    }
    public static ShipDynamic parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ShipDynamic parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input);
    }
    public static ShipDynamic parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ShipDynamic prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code litong.ShipDynamic}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:litong.ShipDynamic)
        ShipDynamicOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ShipOuterClass.internal_static_litong_ShipDynamic_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ShipOuterClass.internal_static_litong_ShipDynamic_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ShipDynamic.class, Builder.class);
      }

      // Construct using litong.ShipOuterClass.ShipDynamic.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = "";
        time_ = 0L;
        version_ = 0L;
        lon_ = 0D;
        lat_ = 0D;
        sog_ = 0F;
        cog_ = 0F;
        rot_ = 0F;
        heading_ = 0F;
        timeout_ = 0;
        draught_ = 0F;
        status_ = 0;
        alarms_ = 0;
        tags_ = 0L;
        specialTag_ = 0;
        tagsTwo_ = 0L;
        origins_ = 0;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ShipOuterClass.internal_static_litong_ShipDynamic_descriptor;
      }

      @Override
      public ShipDynamic getDefaultInstanceForType() {
        return ShipDynamic.getDefaultInstance();
      }

      @Override
      public ShipDynamic build() {
        ShipDynamic result = buildPartial();
        if (!result.isInitialized()) {
          throw Builder.newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ShipDynamic buildPartial() {
        ShipDynamic result = new ShipDynamic(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(ShipDynamic result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.time_ = time_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.version_ = version_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lon_ = lon_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.lat_ = lat_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.sog_ = sog_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.cog_ = cog_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.rot_ = rot_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.heading_ = heading_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.timeout_ = timeout_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.draught_ = draught_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.status_ = status_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.alarms_ = alarms_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.tags_ = tags_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.specialTag_ = specialTag_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.tagsTwo_ = tagsTwo_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.origins_ = origins_;
        }
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ShipDynamic) {
          return mergeFrom((ShipDynamic)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ShipDynamic other) {
        if (other == ShipDynamic.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.getTime() != 0L) {
          setTime(other.getTime());
        }
        if (other.getVersion() != 0L) {
          setVersion(other.getVersion());
        }
        if (other.getLon() != 0D) {
          setLon(other.getLon());
        }
        if (other.getLat() != 0D) {
          setLat(other.getLat());
        }
        if (other.getSog() != 0F) {
          setSog(other.getSog());
        }
        if (other.getCog() != 0F) {
          setCog(other.getCog());
        }
        if (other.getRot() != 0F) {
          setRot(other.getRot());
        }
        if (other.getHeading() != 0F) {
          setHeading(other.getHeading());
        }
        if (other.getTimeout() != 0) {
          setTimeout(other.getTimeout());
        }
        if (other.getDraught() != 0F) {
          setDraught(other.getDraught());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getAlarms() != 0) {
          setAlarms(other.getAlarms());
        }
        if (other.getTags() != 0L) {
          setTags(other.getTags());
        }
        if (other.getSpecialTag() != 0) {
          setSpecialTag(other.getSpecialTag());
        }
        if (other.getTagsTwo() != 0L) {
          setTagsTwo(other.getTagsTwo());
        }
        if (other.getOrigins() != 0) {
          setOrigins(other.getOrigins());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 24: {
                time_ = input.readSInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 24
              case 32: {
                version_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 32
              case 41: {
                lon_ = input.readDouble();
                bitField0_ |= 0x00000008;
                break;
              } // case 41
              case 49: {
                lat_ = input.readDouble();
                bitField0_ |= 0x00000010;
                break;
              } // case 49
              case 61: {
                sog_ = input.readFloat();
                bitField0_ |= 0x00000020;
                break;
              } // case 61
              case 69: {
                cog_ = input.readFloat();
                bitField0_ |= 0x00000040;
                break;
              } // case 69
              case 77: {
                rot_ = input.readFloat();
                bitField0_ |= 0x00000080;
                break;
              } // case 77
              case 85: {
                heading_ = input.readFloat();
                bitField0_ |= 0x00000100;
                break;
              } // case 85
              case 184: {
                timeout_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 184
              case 205: {
                draught_ = input.readFloat();
                bitField0_ |= 0x00000400;
                break;
              } // case 205
              case 224: {
                status_ = input.readUInt32();
                bitField0_ |= 0x00000800;
                break;
              } // case 224
              case 416: {
                alarms_ = input.readUInt32();
                bitField0_ |= 0x00001000;
                break;
              } // case 416
              case 424: {
                tags_ = input.readUInt64();
                bitField0_ |= 0x00002000;
                break;
              } // case 424
              case 432: {
                specialTag_ = input.readUInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 432
              case 440: {
                tagsTwo_ = input.readUInt64();
                bitField0_ |= 0x00008000;
                break;
              } // case 440
              case 448: {
                origins_ = input.readUInt32();
                bitField0_ |= 0x00010000;
                break;
              } // case 448
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private Object id_ = "";
      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      public String getId() {
        Object ref = id_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private long time_ ;
      /**
       * <code>sint64 time = 3;</code>
       * @return The time.
       */
      @Override
      public long getTime() {
        return time_;
      }
      /**
       * <code>sint64 time = 3;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(long value) {

        time_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>sint64 time = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        time_ = 0L;
        onChanged();
        return this;
      }

      private long version_ ;
      /**
       * <code>uint64 version = 4;</code>
       * @return The version.
       */
      @Override
      public long getVersion() {
        return version_;
      }
      /**
       * <code>uint64 version = 4;</code>
       * @param value The version to set.
       * @return This builder for chaining.
       */
      public Builder setVersion(long value) {

        version_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 version = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearVersion() {
        bitField0_ = (bitField0_ & ~0x00000004);
        version_ = 0L;
        onChanged();
        return this;
      }

      private double lon_ ;
      /**
       * <code>double lon = 5;</code>
       * @return The lon.
       */
      @Override
      public double getLon() {
        return lon_;
      }
      /**
       * <code>double lon = 5;</code>
       * @param value The lon to set.
       * @return This builder for chaining.
       */
      public Builder setLon(double value) {

        lon_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>double lon = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLon() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lon_ = 0D;
        onChanged();
        return this;
      }

      private double lat_ ;
      /**
       * <code>double lat = 6;</code>
       * @return The lat.
       */
      @Override
      public double getLat() {
        return lat_;
      }
      /**
       * <code>double lat = 6;</code>
       * @param value The lat to set.
       * @return This builder for chaining.
       */
      public Builder setLat(double value) {

        lat_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>double lat = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearLat() {
        bitField0_ = (bitField0_ & ~0x00000010);
        lat_ = 0D;
        onChanged();
        return this;
      }

      private float sog_ ;
      /**
       * <code>float sog = 7;</code>
       * @return The sog.
       */
      @Override
      public float getSog() {
        return sog_;
      }
      /**
       * <code>float sog = 7;</code>
       * @param value The sog to set.
       * @return This builder for chaining.
       */
      public Builder setSog(float value) {

        sog_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>float sog = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearSog() {
        bitField0_ = (bitField0_ & ~0x00000020);
        sog_ = 0F;
        onChanged();
        return this;
      }

      private float cog_ ;
      /**
       * <code>float cog = 8;</code>
       * @return The cog.
       */
      @Override
      public float getCog() {
        return cog_;
      }
      /**
       * <code>float cog = 8;</code>
       * @param value The cog to set.
       * @return This builder for chaining.
       */
      public Builder setCog(float value) {

        cog_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>float cog = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCog() {
        bitField0_ = (bitField0_ & ~0x00000040);
        cog_ = 0F;
        onChanged();
        return this;
      }

      private float rot_ ;
      /**
       * <code>float rot = 9;</code>
       * @return The rot.
       */
      @Override
      public float getRot() {
        return rot_;
      }
      /**
       * <code>float rot = 9;</code>
       * @param value The rot to set.
       * @return This builder for chaining.
       */
      public Builder setRot(float value) {

        rot_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>float rot = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearRot() {
        bitField0_ = (bitField0_ & ~0x00000080);
        rot_ = 0F;
        onChanged();
        return this;
      }

      private float heading_ ;
      /**
       * <code>float heading = 10;</code>
       * @return The heading.
       */
      @Override
      public float getHeading() {
        return heading_;
      }
      /**
       * <code>float heading = 10;</code>
       * @param value The heading to set.
       * @return This builder for chaining.
       */
      public Builder setHeading(float value) {

        heading_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>float heading = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeading() {
        bitField0_ = (bitField0_ & ~0x00000100);
        heading_ = 0F;
        onChanged();
        return this;
      }

      private int timeout_ ;
      /**
       * <code>uint32 timeout = 23;</code>
       * @return The timeout.
       */
      @Override
      public int getTimeout() {
        return timeout_;
      }
      /**
       * <code>uint32 timeout = 23;</code>
       * @param value The timeout to set.
       * @return This builder for chaining.
       */
      public Builder setTimeout(int value) {

        timeout_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 timeout = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimeout() {
        bitField0_ = (bitField0_ & ~0x00000200);
        timeout_ = 0;
        onChanged();
        return this;
      }

      private float draught_ ;
      /**
       * <code>float draught = 25;</code>
       * @return The draught.
       */
      @Override
      public float getDraught() {
        return draught_;
      }
      /**
       * <code>float draught = 25;</code>
       * @param value The draught to set.
       * @return This builder for chaining.
       */
      public Builder setDraught(float value) {

        draught_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>float draught = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearDraught() {
        bitField0_ = (bitField0_ & ~0x00000400);
        draught_ = 0F;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <code>uint32 status = 28;</code>
       * @return The status.
       */
      @Override
      public int getStatus() {
        return status_;
      }
      /**
       * <code>uint32 status = 28;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {

        status_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 status = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000800);
        status_ = 0;
        onChanged();
        return this;
      }

      private int alarms_ ;
      /**
       * <code>uint32 alarms = 52;</code>
       * @return The alarms.
       */
      @Override
      public int getAlarms() {
        return alarms_;
      }
      /**
       * <code>uint32 alarms = 52;</code>
       * @param value The alarms to set.
       * @return This builder for chaining.
       */
      public Builder setAlarms(int value) {

        alarms_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 alarms = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlarms() {
        bitField0_ = (bitField0_ & ~0x00001000);
        alarms_ = 0;
        onChanged();
        return this;
      }

      private long tags_ ;
      /**
       * <code>uint64 tags = 53;</code>
       * @return The tags.
       */
      @Override
      public long getTags() {
        return tags_;
      }
      /**
       * <code>uint64 tags = 53;</code>
       * @param value The tags to set.
       * @return This builder for chaining.
       */
      public Builder setTags(long value) {

        tags_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 tags = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearTags() {
        bitField0_ = (bitField0_ & ~0x00002000);
        tags_ = 0L;
        onChanged();
        return this;
      }

      private int specialTag_ ;
      /**
       * <code>uint32 special_tag = 54;</code>
       * @return The specialTag.
       */
      @Override
      public int getSpecialTag() {
        return specialTag_;
      }
      /**
       * <code>uint32 special_tag = 54;</code>
       * @param value The specialTag to set.
       * @return This builder for chaining.
       */
      public Builder setSpecialTag(int value) {

        specialTag_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 special_tag = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearSpecialTag() {
        bitField0_ = (bitField0_ & ~0x00004000);
        specialTag_ = 0;
        onChanged();
        return this;
      }

      private long tagsTwo_ ;
      /**
       * <code>uint64 tags_two = 55;</code>
       * @return The tagsTwo.
       */
      @Override
      public long getTagsTwo() {
        return tagsTwo_;
      }
      /**
       * <code>uint64 tags_two = 55;</code>
       * @param value The tagsTwo to set.
       * @return This builder for chaining.
       */
      public Builder setTagsTwo(long value) {

        tagsTwo_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 tags_two = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearTagsTwo() {
        bitField0_ = (bitField0_ & ~0x00008000);
        tagsTwo_ = 0L;
        onChanged();
        return this;
      }

      private int origins_ ;
      /**
       * <code>uint32 origins = 56;</code>
       * @return The origins.
       */
      @Override
      public int getOrigins() {
        return origins_;
      }
      /**
       * <code>uint32 origins = 56;</code>
       * @param value The origins to set.
       * @return This builder for chaining.
       */
      public Builder setOrigins(int value) {

        origins_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 origins = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrigins() {
        bitField0_ = (bitField0_ & ~0x00010000);
        origins_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:litong.ShipDynamic)
    }

    // @@protoc_insertion_point(class_scope:litong.ShipDynamic)
    private static final ShipDynamic DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ShipDynamic();
    }

    public static ShipDynamic getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ShipDynamic>
        PARSER = new com.google.protobuf.AbstractParser<ShipDynamic>() {
      @Override
      public ShipDynamic parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ShipDynamic> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ShipDynamic> getParserForType() {
      return PARSER;
    }

    @Override
    public ShipDynamic getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ShipStaticOrBuilder extends
      // @@protoc_insertion_point(interface_extends:litong.ShipStatic)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    String getId();
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    com.google.protobuf.ByteString
        getIdBytes();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>uint32 mmsi = 11;</code>
     * @return The mmsi.
     */
    int getMmsi();

    /**
     * <code>uint32 imo = 12;</code>
     * @return The imo.
     */
    int getImo();

    /**
     * <code>string callsign = 13;</code>
     * @return The callsign.
     */
    String getCallsign();
    /**
     * <code>string callsign = 13;</code>
     * @return The bytes for callsign.
     */
    com.google.protobuf.ByteString
        getCallsignBytes();

    /**
     * <code>string english_name = 14;</code>
     * @return The englishName.
     */
    String getEnglishName();
    /**
     * <code>string english_name = 14;</code>
     * @return The bytes for englishName.
     */
    com.google.protobuf.ByteString
        getEnglishNameBytes();

    /**
     * <code>string chinese_name = 15;</code>
     * @return The chineseName.
     */
    String getChineseName();
    /**
     * <code>string chinese_name = 15;</code>
     * @return The bytes for chineseName.
     */
    com.google.protobuf.ByteString
        getChineseNameBytes();

    /**
     * <code>uint32 ship_type = 16;</code>
     * @return The shipType.
     */
    int getShipType();

    /**
     * <code>uint32 ais_ship_type = 42;</code>
     * @return The aisShipType.
     */
    int getAisShipType();

    /**
     * <code>uint32 file_ship_type = 43;</code>
     * @return The fileShipType.
     */
    int getFileShipType();

    /**
     * <code>float width = 17;</code>
     * @return The width.
     */
    float getWidth();

    /**
     * <code>float length = 18;</code>
     * @return The length.
     */
    float getLength();

    /**
     * <code>float to_bow = 19;</code>
     * @return The toBow.
     */
    float getToBow();

    /**
     * <code>float to_stern = 20;</code>
     * @return The toStern.
     */
    float getToStern();

    /**
     * <code>float to_port = 21;</code>
     * @return The toPort.
     */
    float getToPort();

    /**
     * <code>float to_starboard = 22;</code>
     * @return The toStarboard.
     */
    float getToStarboard();

    /**
     * <code>string region = 24;</code>
     * @return The region.
     */
    String getRegion();
    /**
     * <code>string region = 24;</code>
     * @return The bytes for region.
     */
    com.google.protobuf.ByteString
        getRegionBytes();

    /**
     * <code>sint64 eta = 26;</code>
     * @return The eta.
     */
    long getEta();

    /**
     * <code>string destination = 27;</code>
     * @return The destination.
     */
    String getDestination();
    /**
     * <code>string destination = 27;</code>
     * @return The bytes for destination.
     */
    com.google.protobuf.ByteString
        getDestinationBytes();

    /**
     * <code>string file_id = 40;</code>
     * @return The fileId.
     */
    String getFileId();
    /**
     * <code>string file_id = 40;</code>
     * @return The bytes for fileId.
     */
    com.google.protobuf.ByteString
        getFileIdBytes();

    /**
     * <code>string ship_no = 41;</code>
     * @return The shipNo.
     */
    String getShipNo();
    /**
     * <code>string ship_no = 41;</code>
     * @return The bytes for shipNo.
     */
    com.google.protobuf.ByteString
        getShipNoBytes();

    /**
     * <code>uint64 track = 30;</code>
     * @return The track.
     */
    long getTrack();
  }
  /**
   * Protobuf type {@code litong.ShipStatic}
   */
  public static final class ShipStatic extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:litong.ShipStatic)
      ShipStaticOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ShipStatic.newBuilder() to construct.
    private ShipStatic(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ShipStatic() {
      id_ = "";
      name_ = "";
      callsign_ = "";
      englishName_ = "";
      chineseName_ = "";
      region_ = "";
      destination_ = "";
      fileId_ = "";
      shipNo_ = "";
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ShipStatic();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ShipOuterClass.internal_static_litong_ShipStatic_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ShipOuterClass.internal_static_litong_ShipStatic_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ShipStatic.class, Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object id_ = "";
    /**
     * <code>string id = 1;</code>
     * @return The id.
     */
    @Override
    public String getId() {
      Object ref = id_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        id_ = s;
        return s;
      }
    }
    /**
     * <code>string id = 1;</code>
     * @return The bytes for id.
     */
    @Override
    public com.google.protobuf.ByteString
        getIdBytes() {
      Object ref = id_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        id_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @Override
    public String getName() {
      Object ref = name_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      Object ref = name_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MMSI_FIELD_NUMBER = 11;
    private int mmsi_ = 0;
    /**
     * <code>uint32 mmsi = 11;</code>
     * @return The mmsi.
     */
    @Override
    public int getMmsi() {
      return mmsi_;
    }

    public static final int IMO_FIELD_NUMBER = 12;
    private int imo_ = 0;
    /**
     * <code>uint32 imo = 12;</code>
     * @return The imo.
     */
    @Override
    public int getImo() {
      return imo_;
    }

    public static final int CALLSIGN_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile Object callsign_ = "";
    /**
     * <code>string callsign = 13;</code>
     * @return The callsign.
     */
    @Override
    public String getCallsign() {
      Object ref = callsign_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        callsign_ = s;
        return s;
      }
    }
    /**
     * <code>string callsign = 13;</code>
     * @return The bytes for callsign.
     */
    @Override
    public com.google.protobuf.ByteString
        getCallsignBytes() {
      Object ref = callsign_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        callsign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENGLISH_NAME_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile Object englishName_ = "";
    /**
     * <code>string english_name = 14;</code>
     * @return The englishName.
     */
    @Override
    public String getEnglishName() {
      Object ref = englishName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        englishName_ = s;
        return s;
      }
    }
    /**
     * <code>string english_name = 14;</code>
     * @return The bytes for englishName.
     */
    @Override
    public com.google.protobuf.ByteString
        getEnglishNameBytes() {
      Object ref = englishName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        englishName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CHINESE_NAME_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile Object chineseName_ = "";
    /**
     * <code>string chinese_name = 15;</code>
     * @return The chineseName.
     */
    @Override
    public String getChineseName() {
      Object ref = chineseName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        chineseName_ = s;
        return s;
      }
    }
    /**
     * <code>string chinese_name = 15;</code>
     * @return The bytes for chineseName.
     */
    @Override
    public com.google.protobuf.ByteString
        getChineseNameBytes() {
      Object ref = chineseName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        chineseName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHIP_TYPE_FIELD_NUMBER = 16;
    private int shipType_ = 0;
    /**
     * <code>uint32 ship_type = 16;</code>
     * @return The shipType.
     */
    @Override
    public int getShipType() {
      return shipType_;
    }

    public static final int AIS_SHIP_TYPE_FIELD_NUMBER = 42;
    private int aisShipType_ = 0;
    /**
     * <code>uint32 ais_ship_type = 42;</code>
     * @return The aisShipType.
     */
    @Override
    public int getAisShipType() {
      return aisShipType_;
    }

    public static final int FILE_SHIP_TYPE_FIELD_NUMBER = 43;
    private int fileShipType_ = 0;
    /**
     * <code>uint32 file_ship_type = 43;</code>
     * @return The fileShipType.
     */
    @Override
    public int getFileShipType() {
      return fileShipType_;
    }

    public static final int WIDTH_FIELD_NUMBER = 17;
    private float width_ = 0F;
    /**
     * <code>float width = 17;</code>
     * @return The width.
     */
    @Override
    public float getWidth() {
      return width_;
    }

    public static final int LENGTH_FIELD_NUMBER = 18;
    private float length_ = 0F;
    /**
     * <code>float length = 18;</code>
     * @return The length.
     */
    @Override
    public float getLength() {
      return length_;
    }

    public static final int TO_BOW_FIELD_NUMBER = 19;
    private float toBow_ = 0F;
    /**
     * <code>float to_bow = 19;</code>
     * @return The toBow.
     */
    @Override
    public float getToBow() {
      return toBow_;
    }

    public static final int TO_STERN_FIELD_NUMBER = 20;
    private float toStern_ = 0F;
    /**
     * <code>float to_stern = 20;</code>
     * @return The toStern.
     */
    @Override
    public float getToStern() {
      return toStern_;
    }

    public static final int TO_PORT_FIELD_NUMBER = 21;
    private float toPort_ = 0F;
    /**
     * <code>float to_port = 21;</code>
     * @return The toPort.
     */
    @Override
    public float getToPort() {
      return toPort_;
    }

    public static final int TO_STARBOARD_FIELD_NUMBER = 22;
    private float toStarboard_ = 0F;
    /**
     * <code>float to_starboard = 22;</code>
     * @return The toStarboard.
     */
    @Override
    public float getToStarboard() {
      return toStarboard_;
    }

    public static final int REGION_FIELD_NUMBER = 24;
    @SuppressWarnings("serial")
    private volatile Object region_ = "";
    /**
     * <code>string region = 24;</code>
     * @return The region.
     */
    @Override
    public String getRegion() {
      Object ref = region_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        region_ = s;
        return s;
      }
    }
    /**
     * <code>string region = 24;</code>
     * @return The bytes for region.
     */
    @Override
    public com.google.protobuf.ByteString
        getRegionBytes() {
      Object ref = region_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        region_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ETA_FIELD_NUMBER = 26;
    private long eta_ = 0L;
    /**
     * <code>sint64 eta = 26;</code>
     * @return The eta.
     */
    @Override
    public long getEta() {
      return eta_;
    }

    public static final int DESTINATION_FIELD_NUMBER = 27;
    @SuppressWarnings("serial")
    private volatile Object destination_ = "";
    /**
     * <code>string destination = 27;</code>
     * @return The destination.
     */
    @Override
    public String getDestination() {
      Object ref = destination_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        destination_ = s;
        return s;
      }
    }
    /**
     * <code>string destination = 27;</code>
     * @return The bytes for destination.
     */
    @Override
    public com.google.protobuf.ByteString
        getDestinationBytes() {
      Object ref = destination_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        destination_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ID_FIELD_NUMBER = 40;
    @SuppressWarnings("serial")
    private volatile Object fileId_ = "";
    /**
     * <code>string file_id = 40;</code>
     * @return The fileId.
     */
    @Override
    public String getFileId() {
      Object ref = fileId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        fileId_ = s;
        return s;
      }
    }
    /**
     * <code>string file_id = 40;</code>
     * @return The bytes for fileId.
     */
    @Override
    public com.google.protobuf.ByteString
        getFileIdBytes() {
      Object ref = fileId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        fileId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHIP_NO_FIELD_NUMBER = 41;
    @SuppressWarnings("serial")
    private volatile Object shipNo_ = "";
    /**
     * <code>string ship_no = 41;</code>
     * @return The shipNo.
     */
    @Override
    public String getShipNo() {
      Object ref = shipNo_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        shipNo_ = s;
        return s;
      }
    }
    /**
     * <code>string ship_no = 41;</code>
     * @return The bytes for shipNo.
     */
    @Override
    public com.google.protobuf.ByteString
        getShipNoBytes() {
      Object ref = shipNo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b =
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        shipNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TRACK_FIELD_NUMBER = 30;
    private long track_ = 0L;
    /**
     * <code>uint64 track = 30;</code>
     * @return The track.
     */
    @Override
    public long getTrack() {
      return track_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!GeneratedMessageV3.isStringEmpty(id_)) {
        GeneratedMessageV3.writeString(output, 1, id_);
      }
      if (!GeneratedMessageV3.isStringEmpty(name_)) {
        GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (mmsi_ != 0) {
        output.writeUInt32(11, mmsi_);
      }
      if (imo_ != 0) {
        output.writeUInt32(12, imo_);
      }
      if (!GeneratedMessageV3.isStringEmpty(callsign_)) {
        GeneratedMessageV3.writeString(output, 13, callsign_);
      }
      if (!GeneratedMessageV3.isStringEmpty(englishName_)) {
        GeneratedMessageV3.writeString(output, 14, englishName_);
      }
      if (!GeneratedMessageV3.isStringEmpty(chineseName_)) {
        GeneratedMessageV3.writeString(output, 15, chineseName_);
      }
      if (shipType_ != 0) {
        output.writeUInt32(16, shipType_);
      }
      if (Float.floatToRawIntBits(width_) != 0) {
        output.writeFloat(17, width_);
      }
      if (Float.floatToRawIntBits(length_) != 0) {
        output.writeFloat(18, length_);
      }
      if (Float.floatToRawIntBits(toBow_) != 0) {
        output.writeFloat(19, toBow_);
      }
      if (Float.floatToRawIntBits(toStern_) != 0) {
        output.writeFloat(20, toStern_);
      }
      if (Float.floatToRawIntBits(toPort_) != 0) {
        output.writeFloat(21, toPort_);
      }
      if (Float.floatToRawIntBits(toStarboard_) != 0) {
        output.writeFloat(22, toStarboard_);
      }
      if (!GeneratedMessageV3.isStringEmpty(region_)) {
        GeneratedMessageV3.writeString(output, 24, region_);
      }
      if (eta_ != 0L) {
        output.writeSInt64(26, eta_);
      }
      if (!GeneratedMessageV3.isStringEmpty(destination_)) {
        GeneratedMessageV3.writeString(output, 27, destination_);
      }
      if (track_ != 0L) {
        output.writeUInt64(30, track_);
      }
      if (!GeneratedMessageV3.isStringEmpty(fileId_)) {
        GeneratedMessageV3.writeString(output, 40, fileId_);
      }
      if (!GeneratedMessageV3.isStringEmpty(shipNo_)) {
        GeneratedMessageV3.writeString(output, 41, shipNo_);
      }
      if (aisShipType_ != 0) {
        output.writeUInt32(42, aisShipType_);
      }
      if (fileShipType_ != 0) {
        output.writeUInt32(43, fileShipType_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!GeneratedMessageV3.isStringEmpty(id_)) {
        size += GeneratedMessageV3.computeStringSize(1, id_);
      }
      if (!GeneratedMessageV3.isStringEmpty(name_)) {
        size += GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (mmsi_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(11, mmsi_);
      }
      if (imo_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(12, imo_);
      }
      if (!GeneratedMessageV3.isStringEmpty(callsign_)) {
        size += GeneratedMessageV3.computeStringSize(13, callsign_);
      }
      if (!GeneratedMessageV3.isStringEmpty(englishName_)) {
        size += GeneratedMessageV3.computeStringSize(14, englishName_);
      }
      if (!GeneratedMessageV3.isStringEmpty(chineseName_)) {
        size += GeneratedMessageV3.computeStringSize(15, chineseName_);
      }
      if (shipType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, shipType_);
      }
      if (Float.floatToRawIntBits(width_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(17, width_);
      }
      if (Float.floatToRawIntBits(length_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(18, length_);
      }
      if (Float.floatToRawIntBits(toBow_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(19, toBow_);
      }
      if (Float.floatToRawIntBits(toStern_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(20, toStern_);
      }
      if (Float.floatToRawIntBits(toPort_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(21, toPort_);
      }
      if (Float.floatToRawIntBits(toStarboard_) != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(22, toStarboard_);
      }
      if (!GeneratedMessageV3.isStringEmpty(region_)) {
        size += GeneratedMessageV3.computeStringSize(24, region_);
      }
      if (eta_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(26, eta_);
      }
      if (!GeneratedMessageV3.isStringEmpty(destination_)) {
        size += GeneratedMessageV3.computeStringSize(27, destination_);
      }
      if (track_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(30, track_);
      }
      if (!GeneratedMessageV3.isStringEmpty(fileId_)) {
        size += GeneratedMessageV3.computeStringSize(40, fileId_);
      }
      if (!GeneratedMessageV3.isStringEmpty(shipNo_)) {
        size += GeneratedMessageV3.computeStringSize(41, shipNo_);
      }
      if (aisShipType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, aisShipType_);
      }
      if (fileShipType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(43, fileShipType_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ShipStatic)) {
        return super.equals(obj);
      }
      ShipStatic other = (ShipStatic) obj;

      if (!getId()
          .equals(other.getId())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getMmsi()
          != other.getMmsi()) return false;
      if (getImo()
          != other.getImo()) return false;
      if (!getCallsign()
          .equals(other.getCallsign())) return false;
      if (!getEnglishName()
          .equals(other.getEnglishName())) return false;
      if (!getChineseName()
          .equals(other.getChineseName())) return false;
      if (getShipType()
          != other.getShipType()) return false;
      if (getAisShipType()
          != other.getAisShipType()) return false;
      if (getFileShipType()
          != other.getFileShipType()) return false;
      if (Float.floatToIntBits(getWidth())
          != Float.floatToIntBits(
              other.getWidth())) return false;
      if (Float.floatToIntBits(getLength())
          != Float.floatToIntBits(
              other.getLength())) return false;
      if (Float.floatToIntBits(getToBow())
          != Float.floatToIntBits(
              other.getToBow())) return false;
      if (Float.floatToIntBits(getToStern())
          != Float.floatToIntBits(
              other.getToStern())) return false;
      if (Float.floatToIntBits(getToPort())
          != Float.floatToIntBits(
              other.getToPort())) return false;
      if (Float.floatToIntBits(getToStarboard())
          != Float.floatToIntBits(
              other.getToStarboard())) return false;
      if (!getRegion()
          .equals(other.getRegion())) return false;
      if (getEta()
          != other.getEta()) return false;
      if (!getDestination()
          .equals(other.getDestination())) return false;
      if (!getFileId()
          .equals(other.getFileId())) return false;
      if (!getShipNo()
          .equals(other.getShipNo())) return false;
      if (getTrack()
          != other.getTrack()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + MMSI_FIELD_NUMBER;
      hash = (53 * hash) + getMmsi();
      hash = (37 * hash) + IMO_FIELD_NUMBER;
      hash = (53 * hash) + getImo();
      hash = (37 * hash) + CALLSIGN_FIELD_NUMBER;
      hash = (53 * hash) + getCallsign().hashCode();
      hash = (37 * hash) + ENGLISH_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getEnglishName().hashCode();
      hash = (37 * hash) + CHINESE_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getChineseName().hashCode();
      hash = (37 * hash) + SHIP_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getShipType();
      hash = (37 * hash) + AIS_SHIP_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getAisShipType();
      hash = (37 * hash) + FILE_SHIP_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getFileShipType();
      hash = (37 * hash) + WIDTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getWidth());
      hash = (37 * hash) + LENGTH_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getLength());
      hash = (37 * hash) + TO_BOW_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToBow());
      hash = (37 * hash) + TO_STERN_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToStern());
      hash = (37 * hash) + TO_PORT_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToPort());
      hash = (37 * hash) + TO_STARBOARD_FIELD_NUMBER;
      hash = (53 * hash) + Float.floatToIntBits(
          getToStarboard());
      hash = (37 * hash) + REGION_FIELD_NUMBER;
      hash = (53 * hash) + getRegion().hashCode();
      hash = (37 * hash) + ETA_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEta());
      hash = (37 * hash) + DESTINATION_FIELD_NUMBER;
      hash = (53 * hash) + getDestination().hashCode();
      hash = (37 * hash) + FILE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getFileId().hashCode();
      hash = (37 * hash) + SHIP_NO_FIELD_NUMBER;
      hash = (53 * hash) + getShipNo().hashCode();
      hash = (37 * hash) + TRACK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTrack());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ShipStatic parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ShipStatic parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ShipStatic parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ShipStatic parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ShipStatic parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ShipStatic parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ShipStatic parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input);
    }
    public static ShipStatic parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ShipStatic parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input);
    }
    public static ShipStatic parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ShipStatic parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input);
    }
    public static ShipStatic parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return GeneratedMessageV3.parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ShipStatic prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code litong.ShipStatic}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:litong.ShipStatic)
        ShipStaticOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ShipOuterClass.internal_static_litong_ShipStatic_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ShipOuterClass.internal_static_litong_ShipStatic_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ShipStatic.class, Builder.class);
      }

      // Construct using litong.ShipOuterClass.ShipStatic.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = "";
        name_ = "";
        mmsi_ = 0;
        imo_ = 0;
        callsign_ = "";
        englishName_ = "";
        chineseName_ = "";
        shipType_ = 0;
        aisShipType_ = 0;
        fileShipType_ = 0;
        width_ = 0F;
        length_ = 0F;
        toBow_ = 0F;
        toStern_ = 0F;
        toPort_ = 0F;
        toStarboard_ = 0F;
        region_ = "";
        eta_ = 0L;
        destination_ = "";
        fileId_ = "";
        shipNo_ = "";
        track_ = 0L;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ShipOuterClass.internal_static_litong_ShipStatic_descriptor;
      }

      @Override
      public ShipStatic getDefaultInstanceForType() {
        return ShipStatic.getDefaultInstance();
      }

      @Override
      public ShipStatic build() {
        ShipStatic result = buildPartial();
        if (!result.isInitialized()) {
          throw Builder.newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ShipStatic buildPartial() {
        ShipStatic result = new ShipStatic(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(ShipStatic result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.mmsi_ = mmsi_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.imo_ = imo_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.callsign_ = callsign_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.englishName_ = englishName_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.chineseName_ = chineseName_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.shipType_ = shipType_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.aisShipType_ = aisShipType_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.fileShipType_ = fileShipType_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.width_ = width_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.length_ = length_;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.toBow_ = toBow_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.toStern_ = toStern_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.toPort_ = toPort_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.toStarboard_ = toStarboard_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.region_ = region_;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.eta_ = eta_;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.destination_ = destination_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.fileId_ = fileId_;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.shipNo_ = shipNo_;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.track_ = track_;
        }
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ShipStatic) {
          return mergeFrom((ShipStatic)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ShipStatic other) {
        if (other == ShipStatic.getDefaultInstance()) return this;
        if (!other.getId().isEmpty()) {
          id_ = other.id_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.getMmsi() != 0) {
          setMmsi(other.getMmsi());
        }
        if (other.getImo() != 0) {
          setImo(other.getImo());
        }
        if (!other.getCallsign().isEmpty()) {
          callsign_ = other.callsign_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (!other.getEnglishName().isEmpty()) {
          englishName_ = other.englishName_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (!other.getChineseName().isEmpty()) {
          chineseName_ = other.chineseName_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (other.getShipType() != 0) {
          setShipType(other.getShipType());
        }
        if (other.getAisShipType() != 0) {
          setAisShipType(other.getAisShipType());
        }
        if (other.getFileShipType() != 0) {
          setFileShipType(other.getFileShipType());
        }
        if (other.getWidth() != 0F) {
          setWidth(other.getWidth());
        }
        if (other.getLength() != 0F) {
          setLength(other.getLength());
        }
        if (other.getToBow() != 0F) {
          setToBow(other.getToBow());
        }
        if (other.getToStern() != 0F) {
          setToStern(other.getToStern());
        }
        if (other.getToPort() != 0F) {
          setToPort(other.getToPort());
        }
        if (other.getToStarboard() != 0F) {
          setToStarboard(other.getToStarboard());
        }
        if (!other.getRegion().isEmpty()) {
          region_ = other.region_;
          bitField0_ |= 0x00010000;
          onChanged();
        }
        if (other.getEta() != 0L) {
          setEta(other.getEta());
        }
        if (!other.getDestination().isEmpty()) {
          destination_ = other.destination_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (!other.getFileId().isEmpty()) {
          fileId_ = other.fileId_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (!other.getShipNo().isEmpty()) {
          shipNo_ = other.shipNo_;
          bitField0_ |= 0x00100000;
          onChanged();
        }
        if (other.getTrack() != 0L) {
          setTrack(other.getTrack());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                id_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 88: {
                mmsi_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 88
              case 96: {
                imo_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 96
              case 106: {
                callsign_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 106
              case 114: {
                englishName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000020;
                break;
              } // case 114
              case 122: {
                chineseName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 122
              case 128: {
                shipType_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 128
              case 141: {
                width_ = input.readFloat();
                bitField0_ |= 0x00000400;
                break;
              } // case 141
              case 149: {
                length_ = input.readFloat();
                bitField0_ |= 0x00000800;
                break;
              } // case 149
              case 157: {
                toBow_ = input.readFloat();
                bitField0_ |= 0x00001000;
                break;
              } // case 157
              case 165: {
                toStern_ = input.readFloat();
                bitField0_ |= 0x00002000;
                break;
              } // case 165
              case 173: {
                toPort_ = input.readFloat();
                bitField0_ |= 0x00004000;
                break;
              } // case 173
              case 181: {
                toStarboard_ = input.readFloat();
                bitField0_ |= 0x00008000;
                break;
              } // case 181
              case 194: {
                region_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00010000;
                break;
              } // case 194
              case 208: {
                eta_ = input.readSInt64();
                bitField0_ |= 0x00020000;
                break;
              } // case 208
              case 218: {
                destination_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00040000;
                break;
              } // case 218
              case 240: {
                track_ = input.readUInt64();
                bitField0_ |= 0x00200000;
                break;
              } // case 240
              case 322: {
                fileId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00080000;
                break;
              } // case 322
              case 330: {
                shipNo_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00100000;
                break;
              } // case 330
              case 336: {
                aisShipType_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 336
              case 344: {
                fileShipType_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 344
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private Object id_ = "";
      /**
       * <code>string id = 1;</code>
       * @return The id.
       */
      public String getId() {
        Object ref = id_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          id_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @return The bytes for id.
       */
      public com.google.protobuf.ByteString
          getIdBytes() {
        Object ref = id_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          id_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = getDefaultInstance().getId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string id = 1;</code>
       * @param value The bytes for id to set.
       * @return This builder for chaining.
       */
      public Builder setIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public String getName() {
        Object ref = name_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private int mmsi_ ;
      /**
       * <code>uint32 mmsi = 11;</code>
       * @return The mmsi.
       */
      @Override
      public int getMmsi() {
        return mmsi_;
      }
      /**
       * <code>uint32 mmsi = 11;</code>
       * @param value The mmsi to set.
       * @return This builder for chaining.
       */
      public Builder setMmsi(int value) {

        mmsi_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 mmsi = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearMmsi() {
        bitField0_ = (bitField0_ & ~0x00000004);
        mmsi_ = 0;
        onChanged();
        return this;
      }

      private int imo_ ;
      /**
       * <code>uint32 imo = 12;</code>
       * @return The imo.
       */
      @Override
      public int getImo() {
        return imo_;
      }
      /**
       * <code>uint32 imo = 12;</code>
       * @param value The imo to set.
       * @return This builder for chaining.
       */
      public Builder setImo(int value) {

        imo_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 imo = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearImo() {
        bitField0_ = (bitField0_ & ~0x00000008);
        imo_ = 0;
        onChanged();
        return this;
      }

      private Object callsign_ = "";
      /**
       * <code>string callsign = 13;</code>
       * @return The callsign.
       */
      public String getCallsign() {
        Object ref = callsign_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          callsign_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string callsign = 13;</code>
       * @return The bytes for callsign.
       */
      public com.google.protobuf.ByteString
          getCallsignBytes() {
        Object ref = callsign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          callsign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string callsign = 13;</code>
       * @param value The callsign to set.
       * @return This builder for chaining.
       */
      public Builder setCallsign(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        callsign_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>string callsign = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearCallsign() {
        callsign_ = getDefaultInstance().getCallsign();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <code>string callsign = 13;</code>
       * @param value The bytes for callsign to set.
       * @return This builder for chaining.
       */
      public Builder setCallsignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        callsign_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private Object englishName_ = "";
      /**
       * <code>string english_name = 14;</code>
       * @return The englishName.
       */
      public String getEnglishName() {
        Object ref = englishName_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          englishName_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string english_name = 14;</code>
       * @return The bytes for englishName.
       */
      public com.google.protobuf.ByteString
          getEnglishNameBytes() {
        Object ref = englishName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          englishName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string english_name = 14;</code>
       * @param value The englishName to set.
       * @return This builder for chaining.
       */
      public Builder setEnglishName(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        englishName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>string english_name = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnglishName() {
        englishName_ = getDefaultInstance().getEnglishName();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <code>string english_name = 14;</code>
       * @param value The bytes for englishName to set.
       * @return This builder for chaining.
       */
      public Builder setEnglishNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        englishName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private Object chineseName_ = "";
      /**
       * <code>string chinese_name = 15;</code>
       * @return The chineseName.
       */
      public String getChineseName() {
        Object ref = chineseName_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          chineseName_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @return The bytes for chineseName.
       */
      public com.google.protobuf.ByteString
          getChineseNameBytes() {
        Object ref = chineseName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          chineseName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @param value The chineseName to set.
       * @return This builder for chaining.
       */
      public Builder setChineseName(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        chineseName_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearChineseName() {
        chineseName_ = getDefaultInstance().getChineseName();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <code>string chinese_name = 15;</code>
       * @param value The bytes for chineseName to set.
       * @return This builder for chaining.
       */
      public Builder setChineseNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        chineseName_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private int shipType_ ;
      /**
       * <code>uint32 ship_type = 16;</code>
       * @return The shipType.
       */
      @Override
      public int getShipType() {
        return shipType_;
      }
      /**
       * <code>uint32 ship_type = 16;</code>
       * @param value The shipType to set.
       * @return This builder for chaining.
       */
      public Builder setShipType(int value) {

        shipType_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 ship_type = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearShipType() {
        bitField0_ = (bitField0_ & ~0x00000080);
        shipType_ = 0;
        onChanged();
        return this;
      }

      private int aisShipType_ ;
      /**
       * <code>uint32 ais_ship_type = 42;</code>
       * @return The aisShipType.
       */
      @Override
      public int getAisShipType() {
        return aisShipType_;
      }
      /**
       * <code>uint32 ais_ship_type = 42;</code>
       * @param value The aisShipType to set.
       * @return This builder for chaining.
       */
      public Builder setAisShipType(int value) {

        aisShipType_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 ais_ship_type = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearAisShipType() {
        bitField0_ = (bitField0_ & ~0x00000100);
        aisShipType_ = 0;
        onChanged();
        return this;
      }

      private int fileShipType_ ;
      /**
       * <code>uint32 file_ship_type = 43;</code>
       * @return The fileShipType.
       */
      @Override
      public int getFileShipType() {
        return fileShipType_;
      }
      /**
       * <code>uint32 file_ship_type = 43;</code>
       * @param value The fileShipType to set.
       * @return This builder for chaining.
       */
      public Builder setFileShipType(int value) {

        fileShipType_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 file_ship_type = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileShipType() {
        bitField0_ = (bitField0_ & ~0x00000200);
        fileShipType_ = 0;
        onChanged();
        return this;
      }

      private float width_ ;
      /**
       * <code>float width = 17;</code>
       * @return The width.
       */
      @Override
      public float getWidth() {
        return width_;
      }
      /**
       * <code>float width = 17;</code>
       * @param value The width to set.
       * @return This builder for chaining.
       */
      public Builder setWidth(float value) {

        width_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>float width = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearWidth() {
        bitField0_ = (bitField0_ & ~0x00000400);
        width_ = 0F;
        onChanged();
        return this;
      }

      private float length_ ;
      /**
       * <code>float length = 18;</code>
       * @return The length.
       */
      @Override
      public float getLength() {
        return length_;
      }
      /**
       * <code>float length = 18;</code>
       * @param value The length to set.
       * @return This builder for chaining.
       */
      public Builder setLength(float value) {

        length_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>float length = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearLength() {
        bitField0_ = (bitField0_ & ~0x00000800);
        length_ = 0F;
        onChanged();
        return this;
      }

      private float toBow_ ;
      /**
       * <code>float to_bow = 19;</code>
       * @return The toBow.
       */
      @Override
      public float getToBow() {
        return toBow_;
      }
      /**
       * <code>float to_bow = 19;</code>
       * @param value The toBow to set.
       * @return This builder for chaining.
       */
      public Builder setToBow(float value) {

        toBow_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_bow = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearToBow() {
        bitField0_ = (bitField0_ & ~0x00001000);
        toBow_ = 0F;
        onChanged();
        return this;
      }

      private float toStern_ ;
      /**
       * <code>float to_stern = 20;</code>
       * @return The toStern.
       */
      @Override
      public float getToStern() {
        return toStern_;
      }
      /**
       * <code>float to_stern = 20;</code>
       * @param value The toStern to set.
       * @return This builder for chaining.
       */
      public Builder setToStern(float value) {

        toStern_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_stern = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearToStern() {
        bitField0_ = (bitField0_ & ~0x00002000);
        toStern_ = 0F;
        onChanged();
        return this;
      }

      private float toPort_ ;
      /**
       * <code>float to_port = 21;</code>
       * @return The toPort.
       */
      @Override
      public float getToPort() {
        return toPort_;
      }
      /**
       * <code>float to_port = 21;</code>
       * @param value The toPort to set.
       * @return This builder for chaining.
       */
      public Builder setToPort(float value) {

        toPort_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_port = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearToPort() {
        bitField0_ = (bitField0_ & ~0x00004000);
        toPort_ = 0F;
        onChanged();
        return this;
      }

      private float toStarboard_ ;
      /**
       * <code>float to_starboard = 22;</code>
       * @return The toStarboard.
       */
      @Override
      public float getToStarboard() {
        return toStarboard_;
      }
      /**
       * <code>float to_starboard = 22;</code>
       * @param value The toStarboard to set.
       * @return This builder for chaining.
       */
      public Builder setToStarboard(float value) {

        toStarboard_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>float to_starboard = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearToStarboard() {
        bitField0_ = (bitField0_ & ~0x00008000);
        toStarboard_ = 0F;
        onChanged();
        return this;
      }

      private Object region_ = "";
      /**
       * <code>string region = 24;</code>
       * @return The region.
       */
      public String getRegion() {
        Object ref = region_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          region_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string region = 24;</code>
       * @return The bytes for region.
       */
      public com.google.protobuf.ByteString
          getRegionBytes() {
        Object ref = region_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          region_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string region = 24;</code>
       * @param value The region to set.
       * @return This builder for chaining.
       */
      public Builder setRegion(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        region_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>string region = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearRegion() {
        region_ = getDefaultInstance().getRegion();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }
      /**
       * <code>string region = 24;</code>
       * @param value The bytes for region to set.
       * @return This builder for chaining.
       */
      public Builder setRegionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        region_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }

      private long eta_ ;
      /**
       * <code>sint64 eta = 26;</code>
       * @return The eta.
       */
      @Override
      public long getEta() {
        return eta_;
      }
      /**
       * <code>sint64 eta = 26;</code>
       * @param value The eta to set.
       * @return This builder for chaining.
       */
      public Builder setEta(long value) {

        eta_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>sint64 eta = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearEta() {
        bitField0_ = (bitField0_ & ~0x00020000);
        eta_ = 0L;
        onChanged();
        return this;
      }

      private Object destination_ = "";
      /**
       * <code>string destination = 27;</code>
       * @return The destination.
       */
      public String getDestination() {
        Object ref = destination_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          destination_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string destination = 27;</code>
       * @return The bytes for destination.
       */
      public com.google.protobuf.ByteString
          getDestinationBytes() {
        Object ref = destination_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          destination_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string destination = 27;</code>
       * @param value The destination to set.
       * @return This builder for chaining.
       */
      public Builder setDestination(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        destination_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>string destination = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearDestination() {
        destination_ = getDefaultInstance().getDestination();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <code>string destination = 27;</code>
       * @param value The bytes for destination to set.
       * @return This builder for chaining.
       */
      public Builder setDestinationBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        destination_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private Object fileId_ = "";
      /**
       * <code>string file_id = 40;</code>
       * @return The fileId.
       */
      public String getFileId() {
        Object ref = fileId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          fileId_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string file_id = 40;</code>
       * @return The bytes for fileId.
       */
      public com.google.protobuf.ByteString
          getFileIdBytes() {
        Object ref = fileId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          fileId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string file_id = 40;</code>
       * @param value The fileId to set.
       * @return This builder for chaining.
       */
      public Builder setFileId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        fileId_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>string file_id = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileId() {
        fileId_ = getDefaultInstance().getFileId();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <code>string file_id = 40;</code>
       * @param value The bytes for fileId to set.
       * @return This builder for chaining.
       */
      public Builder setFileIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        fileId_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private Object shipNo_ = "";
      /**
       * <code>string ship_no = 41;</code>
       * @return The shipNo.
       */
      public String getShipNo() {
        Object ref = shipNo_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          shipNo_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>string ship_no = 41;</code>
       * @return The bytes for shipNo.
       */
      public com.google.protobuf.ByteString
          getShipNoBytes() {
        Object ref = shipNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b =
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          shipNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string ship_no = 41;</code>
       * @param value The shipNo to set.
       * @return This builder for chaining.
       */
      public Builder setShipNo(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        shipNo_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>string ship_no = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearShipNo() {
        shipNo_ = getDefaultInstance().getShipNo();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }
      /**
       * <code>string ship_no = 41;</code>
       * @param value The bytes for shipNo to set.
       * @return This builder for chaining.
       */
      public Builder setShipNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        AbstractMessageLite.checkByteStringIsUtf8(value);
        shipNo_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      private long track_ ;
      /**
       * <code>uint64 track = 30;</code>
       * @return The track.
       */
      @Override
      public long getTrack() {
        return track_;
      }
      /**
       * <code>uint64 track = 30;</code>
       * @param value The track to set.
       * @return This builder for chaining.
       */
      public Builder setTrack(long value) {

        track_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 track = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearTrack() {
        bitField0_ = (bitField0_ & ~0x00200000);
        track_ = 0L;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:litong.ShipStatic)
    }

    // @@protoc_insertion_point(class_scope:litong.ShipStatic)
    private static final ShipStatic DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ShipStatic();
    }

    public static ShipStatic getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ShipStatic>
        PARSER = new com.google.protobuf.AbstractParser<ShipStatic>() {
      @Override
      public ShipStatic parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ShipStatic> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ShipStatic> getParserForType() {
      return PARSER;
    }

    @Override
    public ShipStatic getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_litong_Ship_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_litong_Ship_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_litong_ShipDynamic_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_litong_ShipDynamic_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_litong_ShipStatic_descriptor;
  private static final
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_litong_ShipStatic_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\020proto/ship.proto\022\006litong\"\224\005\n\004Ship\022\n\n\002i" +
      "d\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\022\014\n\004time\030\003 \001(\022\022\017\n\007v" +
      "ersion\030\004 \001(\004\022\013\n\003lon\030\005 \001(\001\022\013\n\003lat\030\006 \001(\001\022\013" +
      "\n\003sog\030\007 \001(\002\022\013\n\003cog\030\010 \001(\002\022\013\n\003rot\030\t \001(\002\022\017\n" +
      "\007heading\030\n \001(\002\022\014\n\004mmsi\030\013 \001(\r\022\013\n\003imo\030\014 \001(" +
      "\r\022\020\n\010callsign\030\r \001(\t\022\024\n\014english_name\030\016 \001(" +
      "\t\022\024\n\014chinese_name\030\017 \001(\t\022\021\n\tship_type\030\020 \001" +
      "(\r\022\025\n\rais_ship_type\030* \001(\r\022\026\n\016file_ship_t" +
      "ype\030+ \001(\r\022\r\n\005width\030\021 \001(\002\022\016\n\006length\030\022 \001(\002" +
      "\022\016\n\006to_bow\030\023 \001(\002\022\020\n\010to_stern\030\024 \001(\002\022\017\n\007to" +
      "_port\030\025 \001(\002\022\024\n\014to_starboard\030\026 \001(\002\022\017\n\007tim" +
      "eout\030\027 \001(\r\022\016\n\006region\030\030 \001(\t\022\017\n\007draught\030\031 " +
      "\001(\002\022\013\n\003eta\030\032 \001(\022\022\023\n\013destination\030\033 \001(\t\022\016\n" +
      "\006status\030\034 \001(\r\022\017\n\007file_id\030( \001(\t\022\017\n\007ship_n" +
      "o\030) \001(\t\022\016\n\006alarms\0304 \001(\r\022\014\n\004tags\0305 \001(\004\022\023\n" +
      "\013special_tag\0306 \001(\r\022\020\n\010tags_two\0307 \001(\004\022\017\n\007" +
      "origins\0308 \001(\r\022\r\n\005track\030\036 \001(\004\022\023\n\013reliabil" +
      "ity\030F \001(\r\"\222\002\n\013ShipDynamic\022\n\n\002id\030\001 \001(\t\022\014\n" +
      "\004time\030\003 \001(\022\022\017\n\007version\030\004 \001(\004\022\013\n\003lon\030\005 \001(" +
      "\001\022\013\n\003lat\030\006 \001(\001\022\013\n\003sog\030\007 \001(\002\022\013\n\003cog\030\010 \001(\002" +
      "\022\013\n\003rot\030\t \001(\002\022\017\n\007heading\030\n \001(\002\022\017\n\007timeou" +
      "t\030\027 \001(\r\022\017\n\007draught\030\031 \001(\002\022\016\n\006status\030\034 \001(\r" +
      "\022\016\n\006alarms\0304 \001(\r\022\014\n\004tags\0305 \001(\004\022\023\n\013specia" +
      "l_tag\0306 \001(\r\022\020\n\010tags_two\0307 \001(\004\022\017\n\007origins" +
      "\0308 \001(\r\"\214\003\n\nShipStatic\022\n\n\002id\030\001 \001(\t\022\014\n\004nam" +
      "e\030\002 \001(\t\022\014\n\004mmsi\030\013 \001(\r\022\013\n\003imo\030\014 \001(\r\022\020\n\010ca" +
      "llsign\030\r \001(\t\022\024\n\014english_name\030\016 \001(\t\022\024\n\014ch" +
      "inese_name\030\017 \001(\t\022\021\n\tship_type\030\020 \001(\r\022\025\n\ra" +
      "is_ship_type\030* \001(\r\022\026\n\016file_ship_type\030+ \001" +
      "(\r\022\r\n\005width\030\021 \001(\002\022\016\n\006length\030\022 \001(\002\022\016\n\006to_" +
      "bow\030\023 \001(\002\022\020\n\010to_stern\030\024 \001(\002\022\017\n\007to_port\030\025" +
      " \001(\002\022\024\n\014to_starboard\030\026 \001(\002\022\016\n\006region\030\030 \001" +
      "(\t\022\013\n\003eta\030\032 \001(\022\022\023\n\013destination\030\033 \001(\t\022\017\n\007" +
      "file_id\030( \001(\t\022\017\n\007ship_no\030) \001(\t\022\r\n\005track\030" +
      "\036 \001(\004b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_litong_Ship_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_litong_Ship_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_litong_Ship_descriptor,
        new String[] { "Id", "Name", "Time", "Version", "Lon", "Lat", "Sog", "Cog", "Rot", "Heading", "Mmsi", "Imo", "Callsign", "EnglishName", "ChineseName", "ShipType", "AisShipType", "FileShipType", "Width", "Length", "ToBow", "ToStern", "ToPort", "ToStarboard", "Timeout", "Region", "Draught", "Eta", "Destination", "Status", "FileId", "ShipNo", "Alarms", "Tags", "SpecialTag", "TagsTwo", "Origins", "Track", "Reliability", });
    internal_static_litong_ShipDynamic_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_litong_ShipDynamic_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_litong_ShipDynamic_descriptor,
        new String[] { "Id", "Time", "Version", "Lon", "Lat", "Sog", "Cog", "Rot", "Heading", "Timeout", "Draught", "Status", "Alarms", "Tags", "SpecialTag", "TagsTwo", "Origins", });
    internal_static_litong_ShipStatic_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_litong_ShipStatic_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_litong_ShipStatic_descriptor,
        new String[] { "Id", "Name", "Mmsi", "Imo", "Callsign", "EnglishName", "ChineseName", "ShipType", "AisShipType", "FileShipType", "Width", "Length", "ToBow", "ToStern", "ToPort", "ToStarboard", "Region", "Eta", "Destination", "FileId", "ShipNo", "Track", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
