package com.xx.web.proto;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER})
public @interface ProtoField {
    boolean Ignore() default false;
    Class TargetClass() default Void.class;
    String TargetName() default "";

    String Function() default "";
}
