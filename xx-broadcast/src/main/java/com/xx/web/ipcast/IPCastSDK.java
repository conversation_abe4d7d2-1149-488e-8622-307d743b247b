/**
 * 用途：jna 调用DLL测试
 */
package com.xx.web.ipcast;

import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.win32.StdCallLibrary;

public interface IPCastSDK  extends StdCallLibrary {
	IPCastSDK INSTANCE = (IPCastSDK) Native.loadLibrary(System.getProperty("user.dir") + "\\xx-broadcast\\dll\\ip_cast.dll", IPCastSDK.class);

	/* *************************** 1.系统功能******************************** */
	/**
	 * 定义回调函数
	 * @param i_event 返回事件类型
	 * @param ptr_param 返回事件具体信息
	 * @param i_length 参数的长度
	 * @param ptr_user 函数类对象指针
	 */
	public interface ip_callback extends StdCallCallback{
		public int invoke(int i_event, String ptr_param, int i_length, String ptr_user);
	}
	/**
	 * 设置回调函数
	 * @param ptr_func 回调函数指针
	 * @param ptr_user 回调函数类对象
	 */
	public void ip_set_callback(ip_callback ptr_func, Object ptr_user);
	/**
	 * 用户登录
	 * @param server_ip 服务器ip
	 * @param user_name 用户名称
	 * @param password 登录密码
	 * @return 200 成功，其它失败
	 */
	public int ip_user_login(String server_ip, String user_name, String password);
	/**
	 * 用户注销登录
	 * @return 200 成功
	 */
	public int ip_user_logout();
	/**
	 * 释放指针
	 * @param ptr 回传指针
	 */
	public void ip_realse_ptr(Pointer ptr);

	/* **************************** 2.任务控制******************************** */
	/**
	 * 停止任务
	 * 2023/04/23
	 * @param hashkey 任务hashkey
	 * @return true-成功， false-失败
	 */
	public boolean ip_stop_server_task(String hashkey);
	/**
	 * 调整任务音量
	 * @param hashkey 任务hashkey
	 * @param volume 任务音量(0-100),数值越大音量越大,建议最大值不要超过95
	 * @return true：成功，false：失败
	 */
	public boolean ip_set_task_volume(String hashkey, int volume);
	/**
	 * 终端音量调节
	 * @param ipv4 终端ip地址
	 * @param volume 终端音量
	 * @return true：成功，false：失败
	 */
	public boolean ip_set_endpoint_volume(String ipv4, int volume);
	/**
	 * 添加终端
	 * 2023/04/23
	 * @param hashkey 任务hashkey
	 * @param endpointips ip地址之间以逗号分隔,ip1,ip2,
	 * @return true：成功，false：失败
	 */
	public boolean ip_req_push_endpoints(String hashkey, String endpointips);
	/**
	 * 移除终端
	 * 2023/04/23
	 * @param hashkey 任务hashkey
	 * @param endpointips ip地址之间以逗号分隔,ip1,ip2,
	 * @return true：成功，false：失败
	 */
	public boolean ip_req_pop_endpoints(String hashkey, String endpointips);
	/**
	 * 获取分区信息
	 * @return 分区信息指针，失败返回NULL，需要使用ip_realse_ptr 函数进行释放。
	 */
	public Pointer ip_get_all_zone();
	/**
	 * 获取用户终端信息
	 * @return json
	 * 注：1 调用p_realse_ptr 函数释放返回值
	 */
	public Pointer ip_get_endpoint_group();
	/**
	 * 获取当前本地播放MP3任务id
	 * 2023/04/23
	 * @return 返回当前mp3播放任务信息,当执行多个本地mp3任务时可调用全部获取进行操作
	 */
	public Pointer ip_get_all_mp3_taskid();
	/**
	 * 分区功放设置,控制8分区通道的开关
	 * 2023/04/23
	 * @param ip 分区功放的IP地址
	 * @param nEightZone 需要打开的通道,八位二进制,255为全部通道打开,0-关闭全部通道
	 * @return true：成功，false：失败
	 */
	public boolean ip_set_terminal_eightzone(String ip, int nEightZone);



	/* **************************** 3.本地MP3播放 *******************************/
	/**
	 * 开始mp3 文件播放
	 * @param task_name 任务名称
	 * @param endpoint_ip 终端播放列表‘,’分隔
	 * @param mp3_fullpath 所有播放文件路径‘,’分隔
	 * @param play_mode [0:顺序播放 1:循环播放 2:真随机播放（歌曲可能会重复）3:伪随机播放（歌曲不会重复）4:单曲循环 5:单曲播放]
	 * @param play_volume 任务音量，0：不对终端进行统一任务音量控制，音量将以服务器终端音量为准， >0:下发控制任务音量
	 * @param play_second 播放时长限制  0：无限制
	 * @return 任务关键字hashkey
	 */
	public Pointer ip_play_mp3_start(String task_name,String endpoint_ip,
			String mp3_fullpath, int play_mode, int play_volume, int play_second);
	/**
	 * 设置播放模式
	 * @param taskHashkey 任务hashkey
	 * @param nPlayMode 播放模式[0:顺序播放 1:循环播放 2:真随机播放（歌曲可能会重复）3:伪随机播放（歌曲不会重复）4:单曲循环 5:单曲播放]
	 * @return 成功返回true，失败返回false
	 */
	public boolean ip_play_mp3_set_mode(String taskHashkey, int nPlayMode);

	/**
	 * 播放控制（暂停/恢复）
	 * @param hashkey 任务关键字
	 * @param play_status 播放状态：0:停止任务，1:播放，2：暂停
	 * @return true：成功，false：失败
	 */
	public boolean ip_play_control(String hashkey, int play_status);
	/**
	 * 播放上一曲
	 * @param hashkey 任务关键字
	 * @return true：成功，false：失败
	 */
	public boolean ip_play_last_mp3(String hashkey);
	/**
	 * 播放下一曲
	 * @param hashkey 任务关键字
	 * @return true：成功，false：失败
	 */
	public boolean ip_play_next_mp3(String hashkey);
	/**
	 * 播放指定文件
	 * @param hashkey 任务关键字
	 * @param mp3_index 本地MP3任务中的音乐索引，下标从0开始
	 * @return true：成功，false：失败
	 */
	public boolean ip_play_choose_mp3(String hashkey, int mp3_index);
	/**
	 * 当前播放歌曲进度调节
	 * @param hashkey 任务关键字
	 * @param process 进度百分比(1-100)
	 * @return true：成功，false：失败
	 */
	public boolean ip_play_drag_process(String hashkey, float process);


	/* **************************** 4.远程mp3播放 *******************************/
	/**
	 * 创建/修改远程任务
	 * 2023/04/23
	 * @param nway 创建/修改远程任务
	 * @param taskname 任务名称，建议用有意义的字符串
	 * @param ips 播放终端IP地址，以逗号分隔
	 * @param musicids 服务器音乐id，通过4-2可获取
	 * @param priority 任务优先级，0表示默认优先级
	 * @param volume 任务音量，0表示默认音量
	 * @param playmode 播放模式[0:顺序播放，1:列表循环,2:真随机播放 (可能重复),3:伪随机播放(不重复)]
	 * @param hashkey 预留值，默认传空即可(不能传NULL)
	 */
	public void ip_req_alter_remote_task_play(int nway, String taskname, String ips, String musicids,
			int priority, int volume, int playmode, String hashkey);
	/**
	 * 获取服务器音频列表
	 * 2023/04/23
	 * @return json信息
	 */
	public Pointer ip_get_server_music_list();
	/**
	 * 获取远程任务列表
	 * 2023/04/23
	 * @return 远程任务json
	 */
	public Pointer ip_get_remote_task_info();
	/**
	 * 删除远程任务
	 * 2023/04/23
	 * @param remotetaskid 远程任务ID列表,"111,113,114,210"
	 * @return true-成功，false-失败
	 */
	public boolean ip_req_delete_remote_task(String remotetaskid);
	/**
	 * 执行远程任务
	 * 2023/04/23
	 * @param nremotetaskid 需要执行的任务id
	 * @param nAutoStart 1-自动播放(默认),0-生成任务而不播放,通过4-6播放控制操作(订阅进度条件下使用)
	 * @return true-成功，false-失败
	 */
	public boolean ip_req_exec_remote_task(int nremotetaskid , int nAutoStart);
	/**
	 * 远程任务播放控制
	 * 2023/04/23
	 * @param hashkey 任务hashkey，通过回调函数获取
	 * @param controlcode 控制码[0-暂停播放 1-恢复播放 2-上一曲 3-下一曲 4播放指定序号歌曲(下标从0开始) 5-跳动指定进度%(1-100)
	 * 					  6-设置播放模式 7-开始播放 8-停止播放 9-订阅进度(0-取消订阅 1-订阅)]
	 * @param controlvalue 控制值，配合控制码使用
	 * @return true-成功，false-失败
	 */
	public boolean ip_control_play_remote(String hashkey, int controlcode, int controlvalue);


	/* **************************** 5.实时音频采集 *******************************/
	/**
	 * 发起广播
	 * @param endpoint 广播发起终端(即音源端，必须是本广播厂家的数字话筒)
	 * @param peer_endpoint 广播接收终端，多个用‘,’ 号分隔，如”***********,***********”
	 * @return 成功返回任务hashkey 一个字符串，失败返回NULL
	 */
	public Pointer ip_task_request_broadcast(String endpoint, String peer_endpoint);
	/**
	 * 发起对讲,一对一双向讲话
	 * @param endpoint 对讲发起终端(必须是本广播厂家的数字话筒)
	 * @param peer_endpoint 对讲接收终端(必须是本广播厂家的数字话筒)
	 * @return 成功返回任务hashkey 一个字符串，失败返回NULL
	 */
	public Pointer ip_task_request_talk(String endpoint, String peer_endpoint);
	/**
	 * 开始终端采播
	 * @param szTaskEndpoint 采集终端
	 * @param szTaskName 任务名称
	 * @param szUserName 用户名
	 * @param szPeerEndpoint 接收终端
	 * @param nTaskVolume 任务音量
	 * @param bRecord 是否录音
	 * @param nRecordTime 录音时长
	 * @return 成功:任务hashkey,失败NULL
	 */
	public Pointer ip_task_request_endpoint_collect(String szTaskEndpoint,
			String szTaskName, String szUserName, String szPeerEndpoint,
			int nTaskVolume, boolean bRecord, int nRecordTime);
	/**
	 * 本地声卡采集
	 * 2023/04/23
	 * @param taskname 任务名称
	 * @param endpoint_ip 终端播放列表IP,多个设备之间以逗号分隔 ip1,ip2...
	 * @param soundcardname 本机声卡名称,通过[5-5获取声卡信息]获取
	 * @param nAudioQuality 采样率,16或者24,默认用16
	 * @param nFormat 默认填0即可
	 * @param nVolume 任务音量,0-表示用默认音量
	 * @return 成功-任务hashkey,失败返回空
	 */
	public Pointer ip_request_collect_local_soundcard(String taskname, String endpoint_ip,
			String soundcardname, int nAudioQuality, int nFormat, int nVolume);
	/**
	 * 获取声卡信息
	 * 2023/04/23
	 * @return 本机声卡信息，如"声卡1\n\n声卡2\n\n"，没有可用声卡时返回空
	 */
	public Pointer ip_get_local_soundcard_info();


	/* **************************** 6.文本广播  *******************************/
	/**
	 * 开始文本广播
	 * @param TaskName 任务名称
	 * @param strText TTS要转换的文本内容,内容长度不超过1000字,字数越多,转换时间越长
	 * @param SexVoice 发音人序号,通过[6-2 获取TTS引擎]获取
	 * @param VoiceSpeed 语速,1-9,1最慢,9最快, 5是正常语速
	 * @param nPlayTimes 播放次数
	 * @param nVolume 播放音量（0-100）,0为默认音量
	 * @param endpointgroup 终端播放列表,多个ip地址之间以逗号分隔
	 * @param bRecord 是否保存录音,默认填false,节省磁盘空间
	 * @return 成功:任务hashkey,失败:空
	 * 注意：TTS 不支持多线程,只能等前面一个转换完成才能继续转换
	 */
	public Pointer CreateTTSTask(String TaskName, String strText, int SexVoice, int VoiceSpeed,
			int nPlayTimes, int nVolume, String endpointgroup, boolean bRecord);
	/**
	 * 获取TTS引擎
	 * 2021年5月25日
	 * @return Json格式TTS引擎
	 */
	public Pointer ip_get_tts_engine();



}















