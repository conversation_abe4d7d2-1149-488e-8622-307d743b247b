/**
 * 用途：java 简单测试demo
 */
package com.xx.web.Util;

import com.sun.jna.Pointer;
import com.xx.web.ipcast.IPCastSDK;
import com.xx.web.ipcast.IPCastSDK.ip_callback;

import java.io.UnsupportedEncodingException;
import java.util.Scanner;


public class IPCastSDKUtil {

	private static String ip="************";
	private static String user="admin";
	private static String pass="123456";
	static IPCastSDK IPCastSDKObj = IPCastSDK.INSTANCE;


	/* *************************** 1.系统功能******************************** */
	// 定义回调函数
	public static ip_callback ipcb;

	public static ip_callback getIpcb() {
		return ipcb;
	}

	public static void setIpcb(ip_callback ipcb) {
		IPCastSDKUtil.ipcb = ipcb;
	}

	//用户登录
	public static int login() {
//		System.loadLibrary("hellojni");
		System.out.println("用户登录！");
		return IPCastSDKObj.ip_user_login(ip, user, pass);
	}

	//用户注销登录
	public static int logout() {
		System.out.println("用户注销登录！");
		return IPCastSDKObj.ip_user_logout();
	}

	//释放指针
	public static void realsePtr(Pointer r_ptr) {
		System.out.println("释放回传结果");
		IPCastSDKObj.ip_realse_ptr(r_ptr);
	}

	/* **************************** 2.任务控制******************************** */
	public static boolean stopTask(String hashkey) {
		System.out.println("停止任务");
		return IPCastSDKObj.ip_stop_server_task(hashkey);
	}

	//调整任务音量
	public static boolean setTaskVol(String hashkey, int volume) {
		System.out.println("调整任务音量");
		return IPCastSDKObj.ip_set_task_volume(hashkey, volume);
	}

	//终端音量调节
	public static boolean setTermVol(String ipv4, int volume) {
		System.out.println("终端音量调节");
		return IPCastSDKObj.ip_set_endpoint_volume(ipv4, volume);
	}

	//添加终端
	public static boolean pushEndpoints(String hashkey, String endpointips) {
		System.out.println("添加终端");
		return IPCastSDKObj.ip_req_push_endpoints(hashkey, endpointips);
	}

	//移除终端
	public static boolean popEndpoints(String hashkey, String endpointips) {
		System.out.println("添加终端");
		return IPCastSDKObj.ip_req_pop_endpoints(hashkey, endpointips);
	}

	//获取分区信息
	public static Pointer getAllZone() {
		System.out.println("获取分区信息");
		return IPCastSDKObj.ip_get_all_zone();
	}

	//获取用户终端信息
	public static Pointer getTermList() {
		System.out.println("获取用户终端信息--");
		return IPCastSDKObj.ip_get_endpoint_group();
	}

	//获取当前本地播放MP3任务id
	public static Pointer getAllMp3Task() {
		System.out.println("");
		return IPCastSDKObj.ip_get_all_mp3_taskid();
	}

	//分区功放设置，控制8分区通道的开关
	public static boolean setPropEightzone(String ip, int nEightZone) {
		System.out.println("分区功放设置，控制8分区通道的开关");
		return IPCastSDKObj.ip_set_terminal_eightzone(ip, nEightZone);
	}

	/* **************************** 3.本地MP3播放 *******************************/
	//开始mp3 文件播放
	public static Pointer startPlayMp3(String endpoint) {
		String mp3 = "test.mp3";
		String mp3Url = "F:\\KwDownload";
		Pointer result = IPCastSDKObj.ip_play_mp3_start("播放：" + mp3, endpoint, mp3Url
				+ "\\" + mp3 + "," + mp3Url + "\\徐良 - 虐心（feat.孙羽幽）.mp3" , 1, 34, 0);
		return result;
	}

	//设置播放模式
	public static boolean setMode(String taskHashkey, int mode) {
		System.out.println("设置播放模式");
		return IPCastSDKObj.ip_play_mp3_set_mode(taskHashkey, mode);
	}

	//播放控制
	public static boolean playCtrl(String taskHashkey, int status) {
		System.out.println("播放控制");
		return IPCastSDKObj.ip_play_control(taskHashkey, status);
	}

	//播放上一曲
	public static boolean lastMp3(String taskHashkey) {
		System.out.println("播放上一曲");
		return IPCastSDKObj.ip_play_last_mp3(taskHashkey);
	}

	//播放下一曲
	public static boolean nextMp3(String taskHashkey) {
		System.out.println("播放下一曲");
		return IPCastSDKObj.ip_play_next_mp3(taskHashkey);
	}

	//播放指定文件
	public static boolean chooseMp3(String hashkey, int mp3_index) {
		System.out.println("播放指定文件");
		return IPCastSDKObj.ip_play_choose_mp3(hashkey, mp3_index);
	}

	//当前播放歌曲进度调节
	public static boolean dragProcess(String hashkey, float process) {
		System.out.println("当前播放歌曲进度调节");
		return IPCastSDKObj.ip_play_drag_process(hashkey, process);
	}

	/* **************************** 4.远程mp3播放 *******************************/
	//创建/修改远程任务
	public static void alterRemoteTask(int nway) {
		System.out.println("创建/修改远程任务");
		String taskname = "远程mp3播放任务";
		String ips = "*************";
		String musicids = "1,3";
		int priority = 0;
		int volume = 40;
		int playmode = 0;
		String hashkey = "";
		IPCastSDKObj.ip_req_alter_remote_task_play(nway, taskname, ips,
				musicids, priority, volume, playmode, hashkey);
	}

	//获取服务器音频列表
	public static Pointer getServerMusicList() {
		System.out.println("获取服务器音频列表");
		return IPCastSDKObj.ip_get_server_music_list();
	}

	//获取远程任务列表
	public static Pointer getRemotTaskInfo() {
		System.out.println("获取远程任务列表");
		return IPCastSDKObj.ip_get_remote_task_info();
	}

	//删除远程任务
	public static boolean delRemoteTask(String remotetaskid) {
		System.out.println("删除远程任务");
		return IPCastSDKObj.ip_req_delete_remote_task(remotetaskid);
	}

	//执行远程任务
	public static boolean execRemoteTask(int nremotetaskid , int nAutoStart) {
		System.out.println("执行远程任务");
		return IPCastSDKObj.ip_req_exec_remote_task(nremotetaskid, nAutoStart);
	}

	//远程任务播放控制
	public static boolean ctrlPlayRemote(String hashkey, int controlcode, int controlvalue) {
		System.out.println("远程任务播放控制");
		return IPCastSDKObj.ip_control_play_remote(hashkey, controlcode, controlvalue);
	}

	/* **************************** 5.实时音频采集 *******************************/
	//发起广播
	public static Pointer startCall(String endpoint, String peer_endpoint) {
		System.out.println("发起广播");
		return IPCastSDKObj.ip_task_request_broadcast(endpoint, peer_endpoint);
	}

	//发起对讲
	public static Pointer startTalk(String endpoint, String peer_endpoint) {
		System.out.println("发起对讲");
		return IPCastSDKObj.ip_task_request_talk(endpoint, peer_endpoint);
	}

	/**
	 * 开始终端采播
	 * @param szTaskEndpoint采集终端
	 * @param szTaskName任务名称
	 * @param szUserName用户名
	 * @param szPeerEndpoint接收终端
	 * @param nTaskVolume任务音量
	 * @param bRecord是否录音
	 * @param nRecordTime录音时长
	 * @return
	 */
	public static Pointer startTermCollect(String szTaskEndpoint, String szPeerEndpoint) {
		System.out.println("开始终端采播");
		String szTaskName = "终端采播";
		String szUserName = "admin";
		int nTaskVolume = 50;
		boolean bRecord = false;
		int nRecordTime = 30;
		return IPCastSDKObj.ip_task_request_endpoint_collect(szTaskEndpoint,
				szTaskName, szUserName, szPeerEndpoint, nTaskVolume, bRecord, nRecordTime);
	}

	//本地声卡采集
	public static Pointer startLocalCollect(String endpoint_ip, String soundcardname) {
		System.out.println("开始声卡采播");
		String taskname = "声卡采播";
		int nVolume = 50;
		return IPCastSDKObj.ip_request_collect_local_soundcard(taskname, endpoint_ip,
				soundcardname, 16, 0, nVolume);
	}

	//获取声卡信息
	public static Pointer getLocalSoundcard() {
		System.out.println("获取声卡信息");
		return IPCastSDKObj.ip_get_local_soundcard_info();
	}

	/* **************************** 6.文本广播  *******************************/
	//开始文本广播
	public static Pointer createTTSTask(String strText, int SexVoice) {
		System.out.println("开始文本广播");
		String TaskName = "文本广播任务";
		int VoiceSpeed = 5;
		int nPlayTimes = 2;
		int nVolume = 50;
		String endpointgroup = "*************";
		boolean bRecord = false;
		return IPCastSDKObj.CreateTTSTask(TaskName, strText, SexVoice, VoiceSpeed,
				nPlayTimes, nVolume, endpointgroup, bRecord);
	}

	//获取TTS引擎
	public static Pointer getTTSEngine() {
		System.out.println("获取TTS引擎");
		return IPCastSDKObj.ip_get_tts_engine();
	}



/* *********************************************************************************** */

	// 系统功能-选项1
    public static void sysFuncSelect(){
		System.out.println("---------------系统功能---------------------------");
		System.out.println("1：用户登录  2：注销登录  其它：返回上一层");
		System.out.println("--------------------------------------------------");
		Scanner sc=new Scanner(System.in);
        switch(sc.nextInt()){
			case 1:
				if(login() == 200) {
					System.out.println("用户登录成功");
				}else {
					System.out.println("用户登录失败");
				}
				break;
			case 2:
				logout();
				System.out.println("注销登录");
				break;
			default: break;
        }
    }
    // 任务控制-选项2
    public static void controlFunSelect(){
		System.out.println("---------------任务控制功能---------------------------");
		System.out.println("1：停止任务  2：调整任务音量  3：终端音量调节  4：添加终端  5：移除终端  6：获取分区信息 "
				+ "7：获取终端信息 8：获取当前本地播放MP3任务id 9：分区功放设置  0：退出任务控制功能");
		System.out.println("--------------------------------------------------");
		boolean flag = true;
		while(flag){
			Scanner sc=new Scanner(System.in);
            switch(sc.nextInt()){
				case 1:    //停止任务
					System.out.println("输入hashkey：");
					String hashkey = new Scanner(System.in).nextLine();
					System.out.println("停止任务：" + stopTask(hashkey));
					break;
				case 2:    //调整任务音量
					System.out.println("输入任务hashkey：");
					String taskHashkey = new Scanner(System.in).nextLine();
					System.out.println("输入任务音量：");
					int volume = new Scanner(System.in).nextInt();
					if(setTaskVol(taskHashkey, volume)) {
						System.out.println("调整任务音量成功！");
					}else{
						System.out.println("调整任务音量失败");
					}
					break;
				case 3:    //终端音量调节
					System.out.println("输入终端IP：");
					String ipv4 = new Scanner(System.in).nextLine();
					System.out.println("输入音量：");
					int vol = new Scanner(System.in).nextInt();
					if(setTermVol(ipv4, vol)){
						System.out.println("终端音量调节成功！");
					}else {
						System.out.println("终端音量调节失败");
					}
					break;
				case 4:    //添加终端
					System.out.println("输入任务hashkey：");
					String addHashkey = new Scanner(System.in).nextLine();
					System.out.println("ip地址之间以逗号分隔：");
					String addEndP = new Scanner(System.in).nextLine();
					if(pushEndpoints(addHashkey, addEndP)){
						System.out.println("添加终端成功！");
					}else{
						System.out.println("添加终端失败");
					}
					break;
				case 5:    //移除终端
					System.out.println("输入任务hashkey：");
					String removeHashkey = new Scanner(System.in).nextLine();
					System.out.println("ip地址之间以逗号分隔：");
					String removeEndp = new Scanner(System.in).nextLine();
					if(popEndpoints(removeHashkey, removeEndp)){
						System.out.println("移除终端成功！");
					}else{
						System.out.println("移除终端失败");
					}
					break;
				case 6:    //获取分区信息
					Pointer allzone = getAllZone();
					System.out.println(allzone.getString(0));
					//	realsePtr(allzone);
					break;
				case 7:    //获取终端信息
					Pointer user_points = getTermList();
					System.out.println(user_points.getString(0));
					realsePtr(user_points);
					break;
				case 8:    //获取当前本地播放MP3任务id
					Pointer taskList = getAllMp3Task();
					System.out.println(taskList.getString(0));
					realsePtr(taskList);
					break;
				case 9:    //分区功放设置
					System.out.println("输入IP地址ipv4：");
					String ipv = new Scanner(System.in).nextLine();
					System.out.println("输入需要打开的通道：");
					int nEightZone = new Scanner(System.in).nextInt();
					if(setPropEightzone(ipv, nEightZone)) {
						System.out.println("控制成功。");
					}else {
						System.out.println("控制失败！");
					}
					break;
				case 0: //退出任务控制功能
					flag = false;
				default: break;
            }
        }
    }

    // 本地MP3播放-选项3
    public static void localPlayFunSelect(){
		System.out.println("---------------本地MP3播放功能---------------------------");
		System.out.println("1：开始mp3播放  2：设置播放模式  3：播放控制(暂停/恢复)  4：播放上一曲  5：播放下一曲  "
				+ "6：播放指定文件 7：当前播放歌曲进度调节  0：退出播放功能");
		System.out.println("--------------------------------------------------");
		boolean flag = true;
		while(flag){
			Scanner sc=new Scanner(System.in);
            switch(sc.nextInt()){
				case 1:    //开始mp3播放
					System.out.println("输入播放endpoint：");
					String endpoint = new Scanner(System.in).nextLine();
					Pointer mp3Hashkey = startPlayMp3(endpoint);
					System.out.println(mp3Hashkey.getString(0));
					realsePtr(mp3Hashkey);
					break;
				case 2:    //设置播放模式
					System.out.println("输入任务hashkey：");
					String taskHashkey = new Scanner(System.in).nextLine();
					System.out.println("输入播放模式：(0:顺序播放 1:循环播放 2:真随机播放（歌曲可能会重复）3:伪随机播放（歌曲不会重复）4:单曲循环 5:单曲播放)");
					int mode = new Scanner(System.in).nextInt();
					boolean status = setMode(taskHashkey, mode);
					if(status){
						System.out.println("播放模式设置成功！");
					}else{
						System.out.println("播放模式设置失败");
					}
					break;
				case 3:    //播放控制(暂停/恢复)
					System.out.println("输入任务hashkey：");
					String ctrlkey = new Scanner(System.in).nextLine();
					System.out.println("输入播放控制：(0:停止任务，1:播放，2：暂停)");
					int ctrlstatus = new Scanner(System.in).nextInt();
					if(playCtrl(ctrlkey, ctrlstatus)){
						System.out.println("播放控制成功！");
					}else{
						System.out.println("播放控制失败");
					}
					break;
				case 4:    //播放上一曲
					System.out.println("输入任务hashkey：");
					String lastkey = new Scanner(System.in).nextLine();
					if(lastMp3(lastkey)){
						System.out.println("播放上一曲成功！");
					}else{
						System.out.println("播放上一曲失败");
					}
					break;
				case 5:    //播放下一曲
					System.out.println("输入任务hashkey：");
					String nextkey = new Scanner(System.in).nextLine();
					if(nextMp3(nextkey)){
						System.out.println("播放下一曲成功！");
					}else{
						System.out.println("播放下一曲失败");
					}
					break;
				case 6:    //播放指定文件
					System.out.println("输入任务hashkey：");
					String chooselkey = new Scanner(System.in).nextLine();
					System.out.println("输入音乐文件下标（从0开始）：");
					int mp3_index = new Scanner(System.in).nextInt();
					if(chooseMp3(chooselkey, mp3_index)){
						System.out.println("播放指定文件成功！");
					}else{
						System.out.println("播放指定文件失败");
					}
					break;
				case 7:    //当前播放歌曲进度调节
					System.out.println("输入任务hashkey：");
					String processkey = new Scanner(System.in).nextLine();
					System.out.println("输入进度：");
					float process = new Scanner(System.in).nextFloat();
					if(dragProcess(processkey, process)){
						System.out.println("进度调节成功！");
					}else{
						System.out.println("进度调节失败");
					}
					break;
				case 0: //退出本地MP3播放功能
					flag = false;
				default: break;
            }
        }
    }

    // 远程mp3播放-选项4
    public static void remotePlayFunSelect(){
		System.out.println("---------------远程mp3播放功能---------------------------");
		System.out.println("1：创建/修改远程任务  2：获取服务器音频列表  3：获取远程任务列表  4：删除远程任务 5：执行远程任务 "
				+ "6：远程任务播放控制  0：返回上一层");
		System.out.println("--------------------------------------------------");
		boolean flag = true;
		while(flag){
			Scanner sc=new Scanner(System.in);
            switch(sc.nextInt()){
				case 1:    //创建/修改远程任务
					System.out.println("输入任务nway(0-新建,任务id-修改)：");
					int taskNway = new Scanner(System.in).nextInt();
					alterRemoteTask(taskNway);
					break;
				case 2:    //获取服务器音频列表
					Pointer musicList = getServerMusicList();
					System.out.println(musicList.getString(0));
					realsePtr(musicList);
					break;
				case 3:    //获取远程任务列表
					Pointer taskInfo = getRemotTaskInfo();
					System.out.println("远程任务列表：" + taskInfo.getString(0));
					break;
				case 4:    //删除远程任务
					System.out.println("输入远程任务ID：");
					String remotetaskid = new Scanner(System.in).nextLine();
					if(delRemoteTask(remotetaskid)) {
						System.out.println("远程任务删除成功！");
					}else {
						System.out.println("远程任务删除失败！");
					}
					break;
				case 5:    //执行远程任务
					System.out.println("输入需执行的任务ID：");
					int nremotetaskid = new Scanner(System.in).nextInt();
					System.out.println("输入任务nAutoStart：");
					int nAutoStart = new Scanner(System.in).nextInt();
					if(execRemoteTask(nremotetaskid, nAutoStart)) {
						System.out.println("远程任务执行成功！");
					}else {
						System.out.println("远程任务执行失败！");
					}
					break;
				case 6:    //远程任务播放控制
					System.out.println("输入任务hashkey：");
					String remotekey = new Scanner(System.in).nextLine();
					System.out.println("输入任务的控制码：");
					int controlcode = new Scanner(System.in).nextInt();
					System.out.println("输入任务控制值：");
					int controlvalue = new Scanner(System.in).nextInt();
					if(ctrlPlayRemote(remotekey, controlcode, controlvalue)) {
						System.out.println("远程任务控制成功！");
					}else {
						System.out.println("远程任务控制失败！");
					}
					break;
				case 0:    //退出远程mp3播放功能
					flag = true;
                default: break;
            }
		}
    }

    // 实时音频采集-选项5
    public static void broadcastFunSelect(){
		System.out.println("---------------实时音频采集-----------------------------");
		System.out.println("1：发起广播  2：发起对讲  3：开始终端采集  4：本地声卡采集 5：获取声卡信息  0：返回上一层");
		System.out.println("-------------------------------------------------------");
		boolean flag = true;
		while(flag){
			Scanner sc=new Scanner(System.in);
            switch(sc.nextInt()){
				case 1:    //发起广播
					System.out.println("输入发起终端：");
					String endpoint = new Scanner(System.in).nextLine();
					System.out.println("输入响应终端：");
					String peer_endpoint = new Scanner(System.in).nextLine();
					Pointer p = startCall(endpoint, peer_endpoint);
					System.out.println("hashkey:" + p.getString(0));
					realsePtr(p);
					break;
				case 2:    //发起对讲
					System.out.println("输入发起终端：");
					String endpoint2 = new Scanner(System.in).nextLine();
					System.out.println("输入响应终端：");
					String peer_endpoint2 = new Scanner(System.in).nextLine();
					Pointer talkRs = startTalk(endpoint2, peer_endpoint2);
					System.out.println("hashkey:" + talkRs.getString(0));
					realsePtr(talkRs);
					break;
				case 3:    //开始终端采集
					System.out.println("输入采集终端：");
					String szTaskEndpoint = new Scanner(System.in).nextLine();
					System.out.println("输入接收终端：");
					String szPeerEndpoint = new Scanner(System.in).nextLine();
					Pointer rs = startTermCollect(szTaskEndpoint, szPeerEndpoint);
					System.out.println("终端采集任务ID：" + rs.getString(0));
					realsePtr(rs);
					break;
				case 4:    //开始声卡采集
					System.out.println("输入播放终端IP列表：");
					String endpoint_ip = new Scanner(System.in).nextLine();
					System.out.println("输入本机声卡名称：");
					String soundcardname = new Scanner(System.in).nextLine();
					Pointer localKey = startLocalCollect(endpoint_ip, soundcardname);
					System.out.println("声卡采集任务ID：" + localKey.getString(0));
					realsePtr(localKey);
					break;
				case 5:    //获取声卡信息
					Pointer soundCard = getLocalSoundcard();
					System.out.println("声卡信息：" + soundCard.getString(0));
					realsePtr(soundCard);
					break;
				case 0: //退出文本广播功能
					flag = false;
				default: break;
            }
		}
    }
    // 文本广播功能-选项6
    public static void textFunSelect(){
		System.out.println("---------------文本广播-----------------------------------------");
		System.out.println("1:开始文本广播  2:获取TTS引擎  0:返回上一层");
		System.out.println("----------------------------------------------------------------");
		boolean flag = true;
		while(flag){
			Scanner sc=new Scanner(System.in);
            switch(sc.nextInt()){
				case 1:    //开始文本广播
					System.out.println("输入文本内容：");
					String strText = new Scanner(System.in).nextLine();
					System.out.println("输入发音人序号：");
					int SexVoice = new Scanner(System.in).nextInt();
					System.out.println("hashkey:" + createTTSTask(strText, SexVoice).getString(0));
					break;
				case 2:    //获取TTS引擎
					Pointer tts = getTTSEngine();
					System.out.println("tts：" + tts.getString(0));
					break;
				case 0: //退出文本广播功能
					flag = false;
				default: break;
            }
		}
    }

	//主功能
	public static void MainFuncSelect(){
		System.out.println("------------------------------------------------------------------------------------------------");
        System.out.println("功能测试选项：");
        System.out.println("1:系统功能  2:任务控制  3:本地MP3播放  4:远程MP3播放  5:实时音频采集 6:文本广播");
        System.out.println("------------------------------------------------------------------------------------------------");
    }

	/**
	 * main 函数
	 * @param <setIpcb>
	 * @throws UnsupportedEncodingException
	 * @throws JSONException
	 */
	public static void main(String[] args) throws UnsupportedEncodingException{
		System.setProperty("jna.encoding","GBK");
		boolean flag = true;
		if(login() == 200) {
			System.out.println("登录成功！");
		}else {
			System.out.println("登录失败，请检查！");
		}

		//设置回调函数
		setIpcb(new ip_callback() {
			@Override
			public int invoke(int i_event, String ptr_param, int i_length, String ptr_user) {
				//	System.out.println("回调信息：    事件：" + i_event + " ,ptr_param：" + ptr_param);
				return 0;
			}
		});

		Pointer p =  Pointer.NULL;
		IPCastSDKObj.ip_set_callback(ipcb, p);
		do{
			MainFuncSelect();
			Scanner sc=new Scanner(System.in);
			int select=sc.nextInt();
			switch(select){
				case 1:    //系统功能
					sysFuncSelect();
					break;
				case 2:    //任务控制
					controlFunSelect();
					break;
				case 3:    //本地MP3播放
					localPlayFunSelect();
					break;
				case 4:    //远程MP3播放
					remotePlayFunSelect();
					break;
				case 5:    //实时音频采集
					broadcastFunSelect();
					break;
				case 6:    //文本广播
					textFunSelect();
					break;
				case 0:    //
					flag = false;
				default:
					System.out.println("输入有误");
					break;
			}
		}while(flag);
		System.out.println("调用结束！");

	}

}
