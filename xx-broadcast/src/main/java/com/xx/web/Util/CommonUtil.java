package com.xx.web.Util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class CommonUtil {

	public static String toString(int[] ids){

		if(ids.length>0){
			String str = ids[0] + "";
			for(int i=1; i<ids.length; i++){

			}
		}

		return "aa";
	}


	//以","隔开的字符串转成int数组
	public static int[] toIntArray(String str){
		String[] strArray = str.split(",");
		int[] intArray = new int[strArray.length];//int类型数组
		for(int i=0;i<strArray.length;i++){
			intArray[i] = Integer.parseInt(strArray[i]);//整数数组
		}
		return intArray;
	}
	//去掉int数组后面的0
	public static int[] removeZeroFromIntArray(int[] a) {
		int j = 0;
		// 这个for循环计算出你传入的这个数组去掉0后的长度
		for (int i = 0; i < a.length; i++) {
			if (a[i] != 0) j++;
		}
		int[] newArr = new int[j];	// 定义新数组的长度
		j = 0;
		// 将不为零的copy到新数组中去
		for (int i = 0; i < a.length; i++) {
			if (a[i] != 0) {
				newArr[j] = a[i];
				j++;
			}
		}
		// 循环打印
		/*for (int i = 0; i < newArr.length; i++) {
			System.out.println(newArr[i]);
		}*/
		return newArr;
	}

	/**
     * 获取前一天日期yyyyMMdd
     * @see 经测试，针对闰年02月份或跨年等情况，该代码仍有效。测试代码如下
     * @see calendar.set(Calendar.YEAR, 2013);
     * @see calendar.set(Calendar.MONTH, 0);
     * @see calendar.set(Calendar.DATE, 1);
     * @see 测试时，将其放到<code>calendar.add(Calendar.DATE, -1);</code>前面即可
     * @return 返回的日期格式为yyyyMMdd
     */
    public static String getYestoday(){
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        return new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
    }


    /**
     * 获取当前的日期yyyyMMdd
     */
    public static String getCurrentDate(){
        return new SimpleDateFormat("yyyyMMdd").format(new Date());
    }


    /**
     * 获取当前的时间yyyyMMddHHmmss
     */
    public static String getCurrentTime(){
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }



    /**
     * <p>
     * 检查字符串是否全部为小写.
     * </p>
     *
     * <pre>
     * StringUtil.isAllLowerCase(null)   = false
     * StringUtil.isAllLowerCase("")     = false
     * StringUtil.isAllLowerCase("  ")   = false
     * StringUtil.isAllLowerCase("abc")  = true
     * StringUtil.isAllLowerCase("abC") = false
     * </pre>
     *
     * @param cs
     *            源字符串
     * @return String
     */
    public static boolean isAllLowerCase(String cs) {
        if (cs == null || isEmpty(cs)) {
            return false;
        }
        int sz = cs.length();
        for (int i = 0; i < sz; i++) {
            if (Character.isLowerCase(cs.charAt(i)) == false) {
                return false;
            }
        }
        return true;
    }
    /**
     * 判断是否为空或者null
     * @param str
     * @return
     */
    public static boolean isEmpty(String str) {
		if("".equals(str) || str==null){
			return true;
		}else{
			return false;
		}
	}
    /**
     * 判断是否不为空或者null
     * @param str
     * @return
     */
    public static boolean isNotEmpty(String str) {
		if("".equals(str) || str==null){
			return false;
		}else{
			return true;
		}
	}

	/**
     * <p>
     * 检查是否都是大写.
     * </p>
     *
     * <pre>
     * StringUtil.isAllUpperCase(null)   = false
     * StringUtil.isAllUpperCase("")     = false
     * StringUtil.isAllUpperCase("  ")   = false
     * StringUtil.isAllUpperCase("ABC")  = true
     * StringUtil.isAllUpperCase("aBC") = false
     * </pre>
     *
     * @param cs
     *            源字符串
     * @return String
     */
    public static boolean isAllUpperCase(String cs) {
        if (cs == null || CommonUtil.isEmpty(cs)) {
            return false;
        }
        int sz = cs.length();
        for (int i = 0; i < sz; i++) {
            if (Character.isUpperCase(cs.charAt(i)) == false) {
                return false;
            }
        }
        return true;
    }

    /**
     * <p>
     * 反转字符串.
     * </p>
     *
     * <pre>
     * StringUtil.reverse(null)  = null
     * StringUtil.reverse("")    = ""
     * StringUtil.reverse("bat") = "tab"
     * </pre>
     *
     * @param str
     *            源字符串
     * @return String
     */
    public static String reverse(String str) {
        if (str == null) {
            return null;
        }
        return new StringBuilder(str).reverse().toString();
    }


}
